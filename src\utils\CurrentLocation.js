/* function to download file */
const CurrentLocation = () => {
  let locationInfo = {
    lat: "",
    long: "",
    city: "",
    state: "",
    country: "",
    zipcode: "",
  };

  if (localStorage.getItem("locationInfo")) {
    locationInfo = JSON.parse(localStorage.getItem("locationInfo"));
  }

  if (locationInfo.zipcode === "") {
    const apiKey = "AIzaSyDt4dbtdHCHLj8grTzTVxpztvqUGDkvkaI";

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;

          // get location details using google api and set LocationInfo
          if (latitude && longitude) {
            locationInfo.lat = latitude;
            locationInfo.long = longitude;

            const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;

            //console.log('location apiUrl', apiUrl);

            await fetch(apiUrl)
              .then((response) => response.json())
              .then((data) => {
                if (data.results.length > 0) {
                  // Extract the city and country from the response
                  const results = data.results[0].address_components;

                  for (let i = 0; i < results.length; i++) {
                    if (results[i].types.includes("locality")) {
                      locationInfo.city = results[i].long_name;
                    }
                    if (
                      results[i].types.includes("administrative_area_level_1")
                    ) {
                      locationInfo.state = results[i].long_name;
                    }
                    if (results[i].types.includes("country")) {
                      locationInfo.country = results[i].long_name;
                    }
                    if (results[i].types.includes("postal_code")) {
                      locationInfo.zipcode = results[i].long_name;
                    }
                  }

                  // Use the city and country data
                  // console.log("locationInfo", locationInfo);

                  localStorage.setItem(
                    "locationInfo",
                    JSON.stringify(locationInfo)
                  );
                } else {
                  // console.error("No results found");
                }
              })
              .catch((error) => {
                // console.error("Error fetching data:", error);
              });
          }
        },
        (error) => {
          // console.error("Error getting location:", error);
        }
      );
    } else {
      console.error("Geolocation is not supported by your browser.");
    }
  } else {
    console.log("location already fetched");
    console.log("locationInfo", locationInfo);
  }
};

export { CurrentLocation };
