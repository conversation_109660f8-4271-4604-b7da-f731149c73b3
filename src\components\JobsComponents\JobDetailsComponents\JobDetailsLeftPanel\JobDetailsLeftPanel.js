/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";
// import use translation ------------------------------
import { useTranslation } from "react-i18next";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, putData } from "utils/Gateway";

import { assetImages } from "constants";

const JobDetailsLeftPanel = ({
  reloadLeftPanel = false,
  setReloadLeftPanel = () => {},
}) => {
  const { t } = useTranslation(); // translation marker
  const params = useParams();
  const token = localStorage.getItem("token");

  const [isLoading, setIsLoading] = useState(true);
  const [isApplied, setIsApplied] = useState(false);
  const [applicationCount, setapplicationCount] = useState(0);
  const [challengeType, setChallengeType] = useState("talenthunt");
  const [title, setTitle] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [companyLogoPath, setCompanyLogoPath] = useState("");
  const [jobType, setJobType] = useState("");
  const [minSalary, setMinSalary] = useState("");
  const [maxSalary, setMaxSalary] = useState("");
  const [cityName, setCityName] = useState("");
  const [country, setCountry] = useState("");
  const [tagList, setTagList] = useState([]);

  //function for get project details
  const getJobDetails = async () => {
    setIsLoading(true);
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_CHALLENGE_DETAILS +
        `/${params.id}?token=${token}`;

      const response = await getData(requestUrl);
      setIsLoading(false);
      console.log(response);
      if (response.status && response.data) {
        const data = response.data;
        setTitle(data.name);
        setCompanyLogoPath(data.companylogopath);
        setCompanyName(data.companynamestring);
        setJobType(data.jobtype);
        setMinSalary(data.minsalary);
        setMaxSalary(data.maxsalary);
        setCityName(data.city);
        setCountry(data.country);
        setTagList(data.tagsbyuser);
        setIsApplied(response.data.isappiled);
        setapplicationCount(response.data.applicationcount);
        setChallengeType(data.challengetype);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id) {
      getJobDetails();
    }
  }, [params.id]);

  useEffect(() => {
    if (reloadLeftPanel) {
      getJobDetails();
      setReloadLeftPanel(false);
    }
  }, [reloadLeftPanel]);

  // Placeholder styles for loading state
  const placeholderStyle = {
    background: "linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",
    backgroundSize: "200% 100%",
    animation: "glowing 1.5s infinite",
    borderRadius: "4px",
    display: "inline-block",
  };

  // Render loading state or actual content
  return (
    <div className="pnl_leftpnl">
      <div className="white_shadow_bx people_leftusr">
        <div className="groups_info jobs_info_left">
          <div className="company_logotxt">
            {isLoading ? (
              <span>
                <div
                  style={{
                    ...placeholderStyle,
                    width: "35px",
                    height: "35px",
                    borderRadius: "50%",
                  }}
                ></div>
                <div
                  style={{
                    ...placeholderStyle,
                    width: "100px",
                    height: "20px",
                    marginLeft: "8px",
                  }}
                ></div>
              </span>
            ) : (
              <>
                <span>
                  <img
                    src={
                      companyLogoPath == ""
                        ? assetImages.defaultUser
                        : url.SERVER_URL + companyLogoPath
                    }
                    alt=""
                    height={35}
                    className="rounded-circle"
                  />
                </span>
                <span className="ms-2">{companyName}</span>
              </>
            )}
          </div>
          <div className="experts_holdertext">
            {isLoading ? (
              <div
                style={{
                  ...placeholderStyle,
                  width: "80%",
                  height: "24px",
                  margin: "10px 0",
                }}
              ></div>
            ) : (
              <h3>{title}</h3>
            )}
          </div>
          <div className="job_info">
            {isLoading ? (
              <div
                style={{
                  ...placeholderStyle,
                  width: "60%",
                  height: "20px",
                  margin: "10px 0",
                }}
              ></div>
            ) : (
              <ul className="list_stye_none">
                <li>{jobType}</li>
                <li>{applicationCount} applied</li>
              </ul>
            )}
          </div>
          <div className="">
            <div className="price_fill">
              {isLoading ? (
                <div
                  style={{
                    ...placeholderStyle,
                    width: "120px",
                    height: "20px",
                    margin: "10px 0",
                  }}
                ></div>
              ) : (
                <span>
                  €{minSalary} - {maxSalary}
                </span>
              )}
            </div>
            <div className="event_tags">
              {isLoading ? (
                <div
                  style={{
                    ...placeholderStyle,
                    width: "150px",
                    height: "20px",
                    margin: "10px 0",
                  }}
                ></div>
              ) : (
                <ul className="list_stye_none">
                  <li>
                    <i className="material-icons-outlined"> place </i>
                    <span className="">
                      {cityName}, {country}
                    </span>
                  </li>
                </ul>
              )}
            </div>
          </div>
          <div className="feedback_jointbtnOuter">
            {isLoading ? (
              <div
                style={{
                  ...placeholderStyle,
                  width: "120px",
                  height: "36px",
                  margin: "10px 0",
                }}
              ></div>
            ) : (
              <>
                <div className="details_apply">
                  {challengeType && challengeType === "capitalquest" ? (
                    <Link
                      to={`/capitalquestresponse/${params.id}`}
                      className="btn outline"
                    >
                      {isApplied ? "Applied" : "Apply Now"}
                    </Link>
                  ) : (
                    <Link
                      to={`/cvbuilder/${params.id}`}
                      className="btn outline"
                    >
                      {isApplied ? "Applied" : "Apply Now"}
                    </Link>
                  )}
                </div>

                <div className="tag_btn">
                  <Link
                    data-bs-target="#tags_modal"
                    data-bs-toggle="modal"
                    to="#"
                    className="btn gray_btn"
                  >
                    <i className="material-icons-outlined">more</i>
                  </Link>
                </div>
              </>
            )}
          </div>
          <div className="feedback_jointbtnOuter">
            <div className="tags_links mt-4 mb-2">
              {isLoading ? (
                <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
                  <div
                    style={{
                      ...placeholderStyle,
                      width: "60px",
                      height: "24px",
                      margin: "4px 0",
                    }}
                  ></div>
                  <div
                    style={{
                      ...placeholderStyle,
                      width: "80px",
                      height: "24px",
                      margin: "4px 0",
                    }}
                  ></div>
                  <div
                    style={{
                      ...placeholderStyle,
                      width: "70px",
                      height: "24px",
                      margin: "4px 0",
                    }}
                  ></div>
                </div>
              ) : (
                <ul className="list_stye_none d-flex flex-wrap gap-2 ">
                  {tagList.map((tag, index) => {
                    return (
                      <li key={index}>
                        <Link
                          to="#"
                          className="d-flex align-items-center gap-2"
                        >
                          <span>{tag}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              )}
            </div>
          </div>
        </div>
      </div>
      <style jsx>{`
        @keyframes glowing {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </div>
  );
};

export default JobDetailsLeftPanel;
