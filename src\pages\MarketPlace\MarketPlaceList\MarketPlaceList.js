/* eslint-disable */
import { useEffect } from "react";
import AuthHeader from "components/common/AuthHeader";
import MarketPlaceListBody from "components/MarketPlaceComponents/List/MarketPlaceListBody";

const MarketPlaceList = () => {
  useEffect(() => {
    document.title = "Market Place";
  }, []);

  return (
    <>
      <AuthHeader moduleName={"marketplace"} />
      <MarketPlaceListBody />
    </>
  );
};

export default MarketPlaceList;
