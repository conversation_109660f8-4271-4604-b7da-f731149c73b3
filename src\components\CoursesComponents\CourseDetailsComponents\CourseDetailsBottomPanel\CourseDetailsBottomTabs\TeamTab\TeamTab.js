/* eslint-disable */
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData, postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import MostImportantPanel from "../../MostImportantPanel/MostImportantPanel";
import { assetImages } from "constants";

const TeamTab = ({ activeTab = "" }) => {
  const token = localStorage.getItem("token");
  const params = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [teamMembers, setTeamMembers] = useState([]);

  //get all team member list
  const getCourseTeamMembers = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_GET_ALL_ACTIVE_COURSE_MEMBERS +
        `?token=${token}&courseid=${params.id}&modulefrom=frontend`;

      const response = await getData(requestURL);

      setIsLoading(false);

      console.log("response in team member list------>", response);

      if (response.status) {
        setTeamMembers(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && activeTab === "team") {
      getCourseTeamMembers();
    }
  }, [params.id, activeTab]);

  if (isLoading) {
    return (
      <div className="inner_pnl_outer">
        <div className="inner_leftpnl">
          <div className="post_item_outer">
            <div className="post_item">
              {/* Placeholder for 3 member cards */}
              {[1, 2, 3].map((_, index) => (
                <div
                  className="white_shadow_bx people_bx placeholder-glow"
                  key={index}
                >
                  <div className="people_row d-flex justify-content-between">
                    <div className="left_people d-flex">
                      {/* Profile Image */}
                      <div className="people_profile me-3">
                        <div
                          className="placeholder rounded-circle"
                          style={{ width: "60px", height: "60px" }}
                        ></div>
                      </div>

                      {/* Profile Info */}
                      <div className="people_profile_info">
                        <h3>
                          <span className="placeholder col-6 mb-2"></span>
                        </h3>
                        <h5>
                          <span className="placeholder col-4 mb-2"></span>
                        </h5>

                        {/* Rating */}
                        <div className="event_tags">
                          <ul className="list_stye_none mb-0">
                            <li className="d-flex align-items-center gap-1">
                              <span
                                className="placeholder"
                                style={{ width: "20px", height: "20px" }}
                              ></span>
                              <span className="placeholder col-3"></span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* View Button */}
                    <div className="right_people_btn">
                      <span
                        className="placeholder btn col-6"
                        style={{ height: "38px" }}
                      ></span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="inner_pnl_outer">
      <div className="inner_leftpnl">
        <div className="post_item_outer">
          <div className="post_item">
            {teamMembers.map((memberData, index) => {
              return (
                <div className="white_shadow_bx people_bx" key={index}>
                  <div className="people_row">
                    <div className="left_people">
                      <div className="people_profile">
                        <img
                          width="64"
                          height="64"
                          src={
                            memberData?.image && memberData?.image !== ""
                              ? url.SERVER_URL + memberData?.image
                              : assetImages.defaultUser
                          }
                          alt=""
                        />
                      </div>
                      <div className="people_profile_info">
                        <h3>{memberData.name}</h3>
                        <h5>{memberData.rolestring}</h5>
                      </div>
                    </div>
                    <div className="right_people_btn">
                      <Link
                        to={`/peoples/details/${memberData?.user}`}
                        className="btn outline"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      {/* -------- most important panel ------------- */}
      <MostImportantPanel />
    </div>
  );
};

export default TeamTab;
