/* eslint-disable */
import React, { useContext, useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import momentjs from "moment";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import {
  getCalenderDateInString,
  getFullTextDateString,
} from "helper/EventHelper/EventHelper";

const SetupMeetingModal = ({
  personName,
  setSelectedMeetingDate,
  setSelectedTimeSlot,
  selectedTimeSlot,
  setcomments,
  setmeetingReason,
}) => {
  const token = localStorage.getItem("token");
  const params = useParams();
  const todayValue = new Date().toISOString().split("T")[0];

  const [dateValue, setDateValue] = useState(momentjs(todayValue));

  const [selectedMeetingDateText, setSelectedMeetingDateText] = useState("");

  const [timeSlotList, setTimeSlotList] = useState([
    {
      slot: "09:00",
      availability: false,
    },
    {
      slot: "10:00",
      availability: false,
    },
    {
      slot: "11:00",
      availability: false,
    },
    {
      slot: "12:00",
      availability: false,
    },
    {
      slot: "13:00",
      availability: false,
    },
    {
      slot: "14:00",
      availability: false,
    },
    {
      slot: "15:00",
      availability: false,
    },
    {
      slot: "16:00",
      availability: false,
    },
    {
      slot: "17:00",
      availability: false,
    },
    {
      slot: "18:00",
      availability: false,
    },
    {
      slot: "19:00",
      availability: false,
    },
    {
      slot: "20:00",
      availability: false,
    },
    {
      slot: "21:00",
      availability: false,
    },
  ]);

  const [selectedSlotIndex, setSelectedSlotIndex] = useState(null);

  const [errorMessage, setErrorMessage] = useState("");

  //get all time slot
  const getAvailibiltyHandler = async (value) => {
    const meetingDateText = getFullTextDateString(value._d);
    setSelectedMeetingDateText(meetingDateText);
    const selectedDate = getCalenderDateInString(value._d);

    setSelectedMeetingDate(selectedDate);

    let requestUrl =
      url.API_BASE_URL +
      url.API_GET_USER_TIMES_BY_DATE +
      `?token=${token}&memberid=${params.id}&agendadate=${selectedDate}`;

    const response = await getData(requestUrl);

    console.log(response);

    if (response.status && response.data.length > 0) {
      setTimeSlotList(response.data);
    }
  };

  //slot select
  const slotSelectionHandler = (val, index) => {
    setSelectedSlotIndex(index);
    setSelectedTimeSlot(val.slot);
  };

  //meeting details modal
  const showMeetingDetailsModalhandler = () => {
    if (selectedTimeSlot != "") {
      let meetingModal = document.querySelector("#meeting_setup");
      let modal = bootstrap.Modal.getInstance(meetingModal);
      modal.hide();

      //show register modal
      let myModal = new bootstrap.Modal(
        document.getElementById("meeting_details")
      );
      myModal.show();
    } else {
      const hasAvailableSlot = timeSlotList.some(
        (slot) => slot.availability === true
      );

      if (hasAvailableSlot) {
        setErrorMessage("Please select a time slot");
      } else {
        setErrorMessage(
          "Sorry! No slots available in this date.Try a different date. "
        );
      }
    }
    setSelectedSlotIndex(null);
    setDateValue(momentjs(todayValue));
  };

  const resetHandler = () => {
    setDateValue(momentjs(todayValue));
    setSelectedMeetingDateText("");
    setSelectedSlotIndex(null);
    setSelectedMeetingDate("");
    setSelectedTimeSlot("");
    setmeetingReason("");
    setcomments("");
    setErrorMessage("");
  };

  useEffect(() => {
    if (params.id) {
      getAvailibiltyHandler(momentjs(todayValue));
    }
  }, [params.id]);

  return (
    <div className="post_modal request_modal">
      <div
        className="modal fade"
        id="meeting_setup"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-body">
              <div className="row">
                <div className="col-sm-4 mb-4 mb-sm-0">
                  <p className="fw-bold mb-2">{personName}</p>
                  <h4 className="fw-bold mb-2">60 MINUTE MEETING</h4>
                  <p className="d-flex align-items-center gap-1 fw-bold">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      schedule
                    </span>
                    <span className="d-block">60 min</span>
                  </p>
                </div>
                <div className="col-sm-8 d-flex align-items-start gap-3">
                  <div className="calendar">
                    <LocalizationProvider dateAdapter={AdapterMoment}>
                      <DateCalendar
                        value={dateValue}
                        onChange={(value) => {
                          setSelectedSlotIndex(null);
                          setErrorMessage("");
                          setDateValue(value);
                          getAvailibiltyHandler(value);
                        }}
                      />
                    </LocalizationProvider>
                  </div>
                  <div className="date_list">
                    <p className="text-black mb-4 fw-semibold">
                      {selectedMeetingDateText}
                    </p>
                    <ul className="list_style_none">
                      {timeSlotList.map((timeData, index) => {
                        return (
                          <li
                            className={`bg_white px-3 py-2 rounded-4 fs-sm fw-semibold mb-2 text-center  ${
                              timeData.availability
                                ? "text-primary"
                                : "text-secondary"
                            } ${
                              selectedSlotIndex == index
                                ? "border border-primary rounded"
                                : ""
                            }`}
                            key={index}
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                              if (timeData.availability) {
                                setErrorMessage("");
                                slotSelectionHandler(timeData, index);
                              } else {
                                setErrorMessage(
                                  `Sorry, ${timeData.slot} is not available. Kindly select another slot.`
                                );
                              }
                            }}
                          >
                            {timeData.slot}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              </div>
              <div className="post_btn d-flex justify-content-between">
                <button
                  className="btn outline w-auto"
                  data-bs-dismiss="modal"
                  onClick={resetHandler}
                >
                  Back
                </button>

                <div className="d-flex gap-2 align-items-center justify-content-end">
                  <p className={errorMessage === "" ? "d-none" : "error"}>
                    <span className="material-icons-outlined me-1">info</span>
                    {errorMessage}
                  </p>
                  <button
                    className="btn outline bg-primary text-white w-auto"
                    onClick={showMeetingDetailsModalhandler}
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SetupMeetingModal;
