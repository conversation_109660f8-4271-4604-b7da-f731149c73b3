/* eslint-disable */
import { useEffect, useState } from "react";
import { Link, useHistory } from "react-router-dom";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, putData } from "utils/Gateway";

import { getConvertedDateText } from "helper/EventHelper/EventHelper";

import MeetingListFilterPopup from "../Popup/MeetingListFilterPopup";
import { assetImages } from "constants";
import MeetingReviewModal from "../Modal/MeetingReviewModal";
import ReviewAddedSuccessModal from "components/common/Modals/ReviewAddedSuccessModal";
import MeetingDetailsModal from "../Modal/MeetingDetailsModal";

const MyMeetingsBody = () => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const history = useHistory();

  const todayValue = new Date().toISOString().split("T")[0];

  const glowCount = [1, 2, 3];

  const [isLoading, setIsLoading] = useState(false);
  const [meetingList, setMeetingList] = useState([]);

  const [filterMeetingDate, setfilterMeetingDate] = useState("");
  const [filterMeetingType, setfilterMeetingType] = useState("");
  const [filterMeetingTitle, setfilterMeetingTitle] = useState("");
  const [filterSentList, setfilterSentList] = useState(false);
  const [filterRecieveList, setfilterRecieveList] = useState(false);
  const [filterPendingList, setfilterPendingList] = useState(false);
  const [filterAcceptList, setfilterAcceptList] = useState(false);

  const [feedbackAgendaId, setfeedbackAgendaId] = useState(null);
  const [userFeedbackMeetingReason, setUserFeedbackMeetingReason] =
    useState("");

  const [selectedAgendaId, setSelectedAgendaId] = useState(null);

  //function for get all event members
  const getAllMyMeetings = async () => {
    setIsLoading(true);
    try {
      let requestUrl =
        url.API_BASE_URL + url.API_GET_ALL_MY_MEETINGS + `?token=${token}`;

      if (filterMeetingDate != "") {
        requestUrl += `&filterdates=${filterMeetingDate}`;
      }

      if (filterMeetingTitle != "") {
        requestUrl += `&filterdescription=${filterMeetingTitle}`;
      }

      if (filterMeetingType != "") {
        requestUrl += `&filtermeetingtype=${filterMeetingType}`;
      }

      if (filterSentList) {
        requestUrl += `&filtersentlist=${filterSentList}`;
      }

      if (filterRecieveList) {
        requestUrl += `&filterrecievelist=${filterRecieveList}`;
      }

      if (filterPendingList) {
        requestUrl += `&filterpendinglist=${filterPendingList}`;
      }

      if (filterAcceptList) {
        requestUrl += `&filteracceptlist=${filterAcceptList}`;
      }

      const response = await getData(requestUrl);

      setIsLoading(false);

      console.log(response);

      if (response.status) {
        resetFilter();
        setMeetingList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //accept/reject
  const meetingActionHandler = async (agendaData, action) => {
    try {
      let agendaDataUpdated = {
        senderuser: agendaData.userid?._id,
        meetinguser: agendaData.meetinguserid?._id,
        meetingdate: agendaData.date,
        starttime: agendaData.starttime,
        endtime: agendaData.endtime,
        action,
      };

      console.log(agendaDataUpdated);

      let requestUrl =
        url.API_BASE_URL +
        url.API_ACCPET_REJECT_MEETING +
        `/${agendaData._id}?token=${token}`;

      const response = await putData(requestUrl, agendaDataUpdated);

      console.log("response", response);

      getAllMyMeetings();
    } catch (error) {
      console.log(error.message);
    }
  };

  //create chat
  const createContactChatHandler = async (userId) => {
    if (userId && userId.toString() !== userInfo._id.toString()) {
      try {
        let requestURL =
          url.API_BASE_URL +
          url.API_SEARCH_CONTACT +
          `?token=${token}&member=${userId}`;

        const response = await getData(requestURL);

        // console.log(response);
        if (response.status) {
          const externalUrl = `/conversation/contact/${response.data._id}`;
          history.push(externalUrl);
        }
      } catch (error) {
        console.log(error.message);
      }
    } else {
    }
  };

  const resetFilter = () => {
    setfilterMeetingDate("");
    setfilterMeetingTitle("");
    setfilterMeetingType("");
    setfilterSentList(false);
    setfilterRecieveList(false);
    setfilterPendingList(false);
    setfilterAcceptList(false);
  };

  const refreshHandler = () => {
    getAllMyMeetings();
  };

  const resetReviewData = () => {
    setfeedbackAgendaId(null);
    setUserFeedbackMeetingReason("");
  };

  useEffect(() => {
    if (
      filterMeetingDate != "" ||
      filterMeetingTitle != "" ||
      filterMeetingType != "" ||
      filterSentList ||
      filterRecieveList ||
      filterPendingList ||
      filterAcceptList
    ) {
      getAllMyMeetings();
    }
  }, [
    filterMeetingDate,
    filterMeetingTitle,
    filterMeetingType,
    filterSentList,
    filterRecieveList,
    filterPendingList,
    filterAcceptList,
  ]);

  useEffect(() => {
    getAllMyMeetings();
  }, []);

  return (
    <section className="feed_pnltotal my-meeting">
      <div className="container-fluid">
        {/* top row  */}
        <div className="top_filter_row mb-4">
          <div className="filter_right d-flex align-items-center gap-2 justify-content-end">
            <div className="filter_btn">
              <Link to="#" onClick={refreshHandler}>
                <span className="material-icons-outlined text-secondary">
                  refresh
                </span>
              </Link>
            </div>
            <div className="filter_btn">
              <Link
                to="#"
                data-bs-toggle="offcanvas"
                data-bs-target="#offcanvasMyMeetings"
              >
                <span className="material-icons-outlined">filter_list</span>
              </Link>
            </div>
          </div>
        </div>

        {/* meetings list  */}
        {isLoading ? (
          glowCount.map((item, index) => {
            return (
              <div
                className="meeting_row white_shadow_bx d-flex flex-wrap align-items-center py-4 mb-3"
                key={index}
              >
                <div className="meeting_left meeting_item position-relative px-3">
                  <div className="placeholder-glow mb-3">
                    <span className="placeholder col-6"></span>
                  </div>
                  <div className="placeholder-glow mb-2">
                    <span className="placeholder col-4"></span>
                  </div>
                  <div className="d-flex gap-2 mt-3">
                    <span className="placeholder btn gray_btn col-4"></span>
                    <span className="placeholder btn gray_btn col-3"></span>
                  </div>
                </div>

                <div className="meeting_mdl meeting_item position-relative px-4">
                  <div className="d-flex align-items-center gap-4">
                    <div className="rounded-circle overflow-hidden">
                      <span
                        className="placeholder rounded-circle"
                        style={{ width: "64px", height: "64px" }}
                      ></span>
                    </div>
                    <div className="flex-grow-1">
                      <div className="placeholder-glow mb-2">
                        <span className="placeholder col-7"></span>
                      </div>
                      <div className="placeholder-glow mb-2">
                        <span className="placeholder col-5"></span>
                      </div>
                      <div className="placeholder-glow">
                        <span className="placeholder col-4 me-2"></span>
                        <span className="placeholder col-3"></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="meeting_right meeting_item position-relative px-3 text-center">
                  <div className="placeholder-glow mb-2">
                    <span className="placeholder col-6"></span>
                  </div>
                  <div className="placeholder-glow mb-2">
                    <span className="placeholder col-8"></span>
                  </div>
                  <div className="placeholder-glow mb-1">
                    <span className="placeholder col-7"></span>
                  </div>
                  <div className="placeholder-glow">
                    <span className="placeholder col-5"></span>
                  </div>
                </div>
              </div>
            );
          })
        ) : meetingList.length > 0 ? (
          <div className="meeting_outer">
            {meetingList.map((agendaData, index) => {
              return (
                <div
                  className="meeting_row white_shadow_bx d-flex flex-wrap align-items-center py-4 mb-3"
                  key={index}
                >
                  <div className="meeting_left meeting_item position-relative px-3">
                    <h4 className="d-flex align-items-center text-black mb-3 fs-md gap-1">
                      <i className="material-icons-outlined text-blue fs-lg d-inline-block">
                        schedule
                      </i>
                      <span className="d-inline-block">
                        {agendaData.starttime} - {agendaData.endtime}
                      </span>
                    </h4>
                    <h5 className="d-flex align-items-center fs-sm gap-1 text-gray fs-sm">
                      <i className="material-icons-outlined text-blue fs-md d-inline-block">
                        event
                      </i>
                      <span className="d-inline-block">
                        {agendaData.meetingdate}
                      </span>
                    </h5>

                    <div className="event_user grps_user mt-3">
                      <ul className="list_stye_none">
                        <li>
                          <Link
                            to={`/peoples/details/${
                              agendaData.issent
                                ? agendaData?.userid?._id
                                : agendaData?.meetinguserid?._id
                            }`}
                          >
                            <img
                              height={40}
                              width={40}
                              className="rounded-circle"
                              src={
                                agendaData.issent
                                  ? agendaData?.userimage === ""
                                    ? assetImages.defaultUser
                                    : url.SERVER_URL + agendaData?.userimage
                                  : agendaData?.meetinguserimage === ""
                                  ? assetImages.defaultUser
                                  : url.SERVER_URL +
                                    agendaData?.meetinguserimage
                              }
                              alt=""
                            />
                          </Link>
                        </li>
                        <li>
                          <Link
                            to={`/peoples/details/${
                              agendaData.issent
                                ? agendaData?.userid?._id
                                : agendaData?.meetinguserid?._id
                            }`}
                          >
                            {agendaData.issent
                              ? agendaData?.username
                              : agendaData?.meetingusername}
                          </Link>
                        </li>
                      </ul>
                    </div>

                    {agendaData.meetingstate == "1" ? (
                      <div className="meet_btns d-flex align-items-center gap-3 mt-3">
                        <button className="btn gray_btn disabled">
                          <span>Accepted</span>
                        </button>
                        {agendaData.date > todayValue ? null : (
                          <button
                            className="btn outline"
                            data-bs-toggle="modal"
                            data-bs-target="#meeting_feedback_modal"
                            onClick={() => {
                              setfeedbackAgendaId(agendaData._id);
                              setUserFeedbackMeetingReason(
                                agendaData.meetingreason
                              );
                            }}
                          >
                            Feedback
                          </button>
                        )}
                      </div>
                    ) : agendaData.issent ? (
                      <div className="meet_btns d-flex align-items-center gap-2 mt-3">
                        <button className="btn gray_btn disabled">
                          <span>Awaiting Response</span>
                        </button>
                        <button
                          className="btn outline"
                          onClick={() => {
                            meetingActionHandler(agendaData, "2");
                          }}
                        >
                          Cancel Request
                        </button>
                      </div>
                    ) : (
                      <div className="meet_btns d-flex align-items-center gap-3 mt-3">
                        <button
                          className="btn gray_btn accepted_btn"
                          onClick={() => {
                            meetingActionHandler(agendaData, "1");
                          }}
                        >
                          <span>Accept</span>
                        </button>
                        <button
                          className="btn gray_btn rejected_btn"
                          onClick={() => {
                            meetingActionHandler(agendaData, "2");
                          }}
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </div>
                  <div className="meeting_mdl meeting_item position-relative px-4">
                    <div className="meeting_usr_info d-flex align-items-center gap-4">
                      <div className="meeting_usr rounded-circle overflow-hidden">
                        <img
                          src={
                            agendaData?.meetinguserimage === ""
                              ? assetImages.defaultUser
                              : url.SERVER_URL + agendaData?.meetinguserimage
                          }
                          className="h-100 w-100"
                          alt=""
                        />
                      </div>
                      <div className="meeting_user_text">
                        <h4 className="fs-xl fw-medium text-black mb-3">
                          {agendaData.description}

                          <button
                            className="btn outline ms-3"
                            onClick={() => {
                              createContactChatHandler(
                                agendaData?.meetinguserid._id
                              );
                            }}
                          >
                            <i className="material-icons-outlined">chat</i>
                          </button>
                        </h4>
                        <h5 className="fs-md mb-3">
                          {agendaData?.meetingusername}
                        </h5>
                        <ul className="list_stye_none d-flex align-items-center gap-3">
                          <li className="d-flex align-items-center gap-1">
                            <i className="material-icons-outlined text-blue fs-lg d-inline-block">
                              timer
                            </i>
                            <span className="d-inline-block">
                              {agendaData.duration}
                            </span>
                          </li>
                          {/* <li className="d-flex align-items-center gap-1">
                            <i className="material-icons-outlined text-blue fs-lg d-inline-block">
                              link
                            </i>
                            <span className="d-inline-block">Meet Link</span>
                          </li> */}
                        </ul>
                        <button
                          className="btn outline mt-3"
                          data-bs-toggle="modal"
                          data-bs-target="#meeting_details"
                          onClick={() => {
                            setSelectedAgendaId(agendaData._id);
                          }}
                        >
                          <span>View Details</span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="meeting_right meeting_item position-relative px-3 text-center">
                    <h5 className="mb-1 fs-md fw-medium">
                      {agendaData?.meetingusername}
                    </h5>
                    <h3 className="fs-xl mb-2 fw-bold">
                      {agendaData.duration} Minute Meeting
                    </h3>
                    <ul className="hours_list list_stye_none">
                      <li className="d-flex gap-1 justify-content-center mb-3">
                        <i className="material-icons-outlined text-blue fs-lg d-inline-block">
                          schedule
                        </i>
                        <span className="d-inline-block">
                          {agendaData.starttime} - {agendaData.endtime},{" "}
                          {getConvertedDateText(agendaData.date)}
                        </span>
                      </li>
                      <li className="d-flex gap-1 justify-content-center">
                        <i className="material-icons-outlined text-blue fs-lg d-inline-block">
                          schedule
                        </i>
                        <span className="d-inline-block">
                          India Standard Time
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-lg text-gray fw-semibold mb-4">
                Sorry....! No meetings availbale at this moment
              </p>
            </div>
          </div>
        )}
      </div>

      <MeetingListFilterPopup
        setfilterMeetingTitle={setfilterMeetingTitle}
        setfilterMeetingType={setfilterMeetingType}
        setfilterMeetingDate={setfilterMeetingDate}
        setfilterSentList={setfilterSentList}
        setfilterRecieveList={setfilterRecieveList}
        setfilterPendingList={setfilterPendingList}
        setfilterAcceptList={setfilterAcceptList}
      />

      <MeetingReviewModal
        moduleName="agenda"
        moduleId={feedbackAgendaId}
        meetingReason={userFeedbackMeetingReason}
        onModalClose={resetReviewData}
      />

      <ReviewAddedSuccessModal afterModalClose={resetReviewData} />

      <MeetingDetailsModal
        selectedAgendaId={selectedAgendaId}
        setSelectedAgendaId={setSelectedAgendaId}
      />
    </section>
  );
};

export default MyMeetingsBody;
