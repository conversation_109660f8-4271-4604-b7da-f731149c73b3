/* eslint-disable */
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { getConvertedDateText } from "helper/EventHelper/EventHelper";
import { assetImages } from "constants";

const MeetingDetailsModal = ({
  selectedAgendaId = null,
  setSelectedAgendaId = () => {},
}) => {
  const token = localStorage.getItem("token");

  const [isLoading, setIsLoading] = useState(false);
  const [meetingDetails, setmeetingDetails] = useState({});

  //function for get meeting details
  const getMeetingDetails = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_GET_AGENDA_DETAILS +
        `/${selectedAgendaId}?token=${token}`;

      const response = await getData(requestURL);

      setIsLoading(false);

      console.log(response);

      if (response.status) {
        setmeetingDetails(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (selectedAgendaId) {
      getMeetingDetails();
    }
  }, [selectedAgendaId]);

  const onModalClose = () => {
    setSelectedAgendaId(null);
  };

  return (
    <div className="post_modal request_modal">
      <div
        className="modal fade"
        id="meeting_details"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog">
          <div className="modal-content">
            {isLoading ? (
              <div className="modal-body text-center">
                <div className="gretings_icon mb-3">
                  <span
                    className="placeholder rounded-circle bg-success d-inline-block"
                    style={{ width: "40px", height: "40px" }}
                  ></span>
                </div>

                <h4 className="fw-bold mb-2 placeholder-glow">
                  <span className="placeholder col-6"></span>
                </h4>

                <div className="event_user grps_user mt-3">
                  <ul className="list_stye_none justify-content-center d-flex align-items-center gap-2">
                    <li>
                      <span
                        className="placeholder rounded-circle d-inline-block"
                        style={{ width: "40px", height: "40px" }}
                      ></span>
                    </li>
                    <li>
                      <p className="fw-semibold placeholder-glow">
                        <span className="placeholder col-6"></span>
                      </p>
                    </li>
                  </ul>
                </div>

                <div className="px-3 py-4 my-3 border rounded-3">
                  <p className="fw-bold mb-2 placeholder-glow">
                    <span className="placeholder col-7"></span>
                  </p>
                  <p className="fw-bold mb-2 placeholder-glow">
                    <span className="placeholder col-5"></span>
                  </p>

                  <h3 className="mb-2 placeholder-glow">
                    <span className="placeholder col-6"></span>
                  </h3>

                  <p className="d-flex align-items-center justify-content-center gap-2 fw-bold mb-2 placeholder-glow">
                    <span
                      className="placeholder rounded bg-primary"
                      style={{ width: "20px", height: "20px" }}
                    ></span>
                    <span className="placeholder col-5"></span>
                  </p>

                  <p className="d-flex align-items-center justify-content-center gap-2 fw-bold placeholder-glow">
                    <span
                      className="placeholder rounded bg-primary"
                      style={{ width: "20px", height: "20px" }}
                    ></span>
                    <span className="placeholder col-4"></span>
                  </p>

                  <p className="d-flex align-items-center justify-content-center gap-2 fw-bold mt-2 placeholder-glow">
                    <span
                      className="placeholder rounded bg-primary"
                      style={{ width: "20px", height: "20px" }}
                    ></span>
                    <span className="placeholder col-6"></span>
                  </p>

                  <p className="d-flex align-items-center justify-content-center gap-2 fw-bold mt-2 placeholder-glow">
                    <span
                      className="placeholder rounded bg-primary"
                      style={{ width: "20px", height: "20px" }}
                    ></span>
                    <span className="placeholder col-7"></span>
                  </p>
                </div>

                <div className="post_btn placeholder-glow">
                  <span className="btn btn-outline-secondary disabled placeholder col-2"></span>
                </div>
              </div>
            ) : (
              <div className="modal-body text-center">
                <div className="gretings_icon mb-3">
                  <span className="material-icons-outlined">task_alt</span>
                </div>
                <h4 className="fw-bold mb-2">Meeting Details</h4>

                <div className="event_user grps_user mt-3">
                  <ul className="list_stye_none justify-content-center">
                    <li>
                      <img
                        height={40}
                        width={40}
                        className="rounded-circle"
                        src={
                          meetingDetails.issent
                            ? meetingDetails?.userimage === ""
                              ? assetImages.defaultUser
                              : url.SERVER_URL + meetingDetails?.userimage
                            : meetingDetails?.meetinguserimage === ""
                            ? assetImages.defaultUser
                            : url.SERVER_URL + meetingDetails?.meetinguserimage
                        }
                        alt=""
                      />
                    </li>
                    <li>
                      <p className="fw-semibold">
                        {meetingDetails.issent
                          ? meetingDetails?.username
                          : meetingDetails?.meetingusername}
                      </p>
                    </li>
                  </ul>
                </div>

                <div className="px-3 py-4 my-3 border rounded-3">
                  <p className="fw-bold mb-2">{meetingDetails?.description}</p>
                  <p className="fw-bold mb-2">
                    {meetingDetails?.meetingdetails}
                  </p>

                  <h3 className="mb-2">
                    {meetingDetails?.duration} Minute Meeting
                  </h3>
                  <p className="d-flex align-items-center justify-content-center gap-1 fw-bold mb-2">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      calendar_month
                    </span>
                    <span className="d-block">
                      {meetingDetails.starttime} - {meetingDetails.endtime},{" "}
                      {getConvertedDateText(meetingDetails.date)}
                    </span>
                  </p>
                  <p className="d-flex align-items-center justify-content-center gap-1 fw-bold">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      language
                    </span>
                    <span className="d-block">India Standard Time</span>
                  </p>
                  <p className="d-flex align-items-center justify-content-center gap-1 fw-bold mt-2">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      work
                    </span>
                    {meetingDetails?.meetingreason}
                  </p>
                  <p className="d-flex align-items-center justify-content-center gap-1 fw-bold mt-2">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      message
                    </span>
                    {meetingDetails?.message}
                  </p>
                </div>

                <div className="post_btn">
                  <button
                    className="btn outline w-auto"
                    data-bs-dismiss="modal"
                    onClick={onModalClose}
                  >
                    Done
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MeetingDetailsModal;
