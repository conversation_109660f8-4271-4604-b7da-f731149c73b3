import meetingReasonOptions from "Data/MeetingReason.json";
import UserCard from "../UserCard/UserCard";

const StepOne = ({
  componentStyle = {},
  personName = "",
  userImage = "",
  companyName = "",
  userPosition = "",
  totalNoReviews = "0",
  expertRating = "0.0",
  userRate = 0,
  comments,
  setcomments,
  meetingReason,
  setmeetingReason,
  commentError = false,
  setcommentError = () => {},
  reasonError = false,
  setreasonError = () => {},
}) => {
  return (
    <div className="step-1" style={componentStyle}>
      <div className="row">
        <div className="col-lg-4 mb-4 mb-lg-0">
          <UserCard
            personName={personName}
            userImage={userImage}
            companyName={companyName}
            userPosition={userPosition}
            totalNoReviews={totalNoReviews}
            expertRating={expertRating}
            userRate={userRate}
          />
        </div>
        <div className="col-lg-8">
          <label className="fw-bold mb-3 text-black fs-md">
            Reason of meet
          </label>
          {meetingReasonOptions.map((reason) => (
            <div className="form-check mb-3" key={reason.value}>
              <input
                className="form-check-input p-0 shadow-none"
                type="radio"
                name="relationshipType"
                id={reason.value}
                value={reason.value}
                checked={meetingReason === reason.value}
                onChange={(e) => {
                  setmeetingReason(e.target.value);
                  setreasonError(false);
                }}
              />
              <label className="form-check-label" htmlFor={reason.value}>
                {reason.label}
              </label>
            </div>
          ))}

          <p className={reasonError ? "error" : "d-none"}>
            <span className="material-icons-outlined">info</span>
            Please select reason
          </p>

          <label className="fw-bold mt-1 text-black fs-md">Comments</label>
          <div className="post_description">
            <textarea
              className="form-control"
              placeholder="Comment to meet"
              value={comments}
              onChange={(e) => {
                setcomments(e.target.value);
                setcommentError(false);
              }}
            ></textarea>

            <p className={commentError ? "error" : "d-none"}>
              <span className="material-icons-outlined">info</span>
              Please enter comment
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepOne;
