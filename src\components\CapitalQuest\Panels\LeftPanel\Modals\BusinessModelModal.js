/* eslint-disable */
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { FormControlLabel, Switch } from "@mui/material";

const BusinessModelModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });
  
  const [pricingStrategy, setPricingStrategy] = useState({
    duration: "monthly",
    name: "",
    price: ""
  });

  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  const handleSwitchChange = (e) => {
    setFormData(prevData => ({
      ...prevData,
      realtimeordertracking: e.target.checked
    }));
  };

  const handlePricingInputChange = (e) => {
    const { name, value } = e.target;
    setPricingStrategy(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddPricingStrategy = () => {
    if (pricingStrategy.name && pricingStrategy.price) {
      setFormData(prevData => ({
        ...prevData,
        pricingstrategy: [...(prevData.pricingstrategy || []), { ...pricingStrategy }]
      }));
      setPricingStrategy({
        duration: "monthly",
        name: "",
        price: ""
      });
    }
  };

  const handleDeletePricingStrategy = (index) => {
    setFormData(prevData => ({
      ...prevData,
      pricingstrategy: prevData.pricingstrategy.filter((_, i) => i !== index)
    }));
  };

  const resetHandler = () => {
    setFormData({
      revenuesources: capitalQuestResponseData.revenuesources || "",
      distrubutionchannels: capitalQuestResponseData.distrubutionchannels || "",
      grossmargin: capitalQuestResponseData.grossmargin || "",
      scalabilityplan: capitalQuestResponseData.scalabilityplan || "",
      realtimeordertracking: capitalQuestResponseData.realtimeordertracking || false,
      pricingstrategy: capitalQuestResponseData.pricingstrategy || [],
    });
    
    setPricingStrategy({
      duration: "monthly",
      name: "",
      price: ""
    });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData(prevData => ({
      ...prevData,
      revenuesources: formData.revenuesources,
      distrubutionchannels: formData.distrubutionchannels,
      grossmargin: formData.grossmargin,
      scalabilityplan: formData.scalabilityplan,
      realtimeordertracking: formData.realtimeordertracking,
      pricingstrategy: formData.pricingstrategy,
    }));
    
    let modal = document.querySelector("#business_model_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="business_model_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Business Model</h3>
                <h5>Provide details about your business model</h5>
              </div>
              <button type="button" className="close" data-bs-dismiss="modal" aria-label="Close" onClick={resetHandler}>
                <i className="material-icons-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Revenue Sources</label>
                    <textarea
                      className="form-control"
                      name="revenuesources"
                      value={formData.revenuesources || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Revenue Sources"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Distribution Channels</label>
                    <textarea
                      className="form-control"
                      name="distrubutionchannels"
                      value={formData.distrubutionchannels || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Distribution Channels"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Gross Margin</label>
                    <textarea
                      className="form-control"
                      name="grossmargin"
                      value={formData.grossmargin || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Gross Margin"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Scalability Plan</label>
                    <textarea
                      className="form-control"
                      name="scalabilityplan"
                      value={formData.scalabilityplan || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Scalability Plan"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formData.realtimeordertracking || false}
                          onChange={handleSwitchChange}
                          name="realtimeordertracking"
                          color="primary"
                        />
                      }
                      label="Real-time Order Tracking"
                    />
                  </div>

                  {/* Pricing Strategy Section */}
                  <div className="form-group">
                    <label>Pricing Strategy</label>
                    <div className="pricing-strategy-form d-flex gap-2 mb-2">
                      <select
                        className="form-control"
                        name="duration"
                        value={pricingStrategy.duration}
                        onChange={handlePricingInputChange}
                      >
                        <option value="monthly">Monthly</option>
                        <option value="6months">6 Months</option>
                        <option value="yearly">Yearly</option>
                      </select>
                      <input
                        type="text"
                        className="form-control"
                        name="name"
                        value={pricingStrategy.name}
                        onChange={handlePricingInputChange}
                        placeholder="Plan Name"
                      />
                      <input
                        type="number"
                        className="form-control"
                        name="price"
                        value={pricingStrategy.price}
                        onChange={handlePricingInputChange}
                        placeholder="Price"
                      />
                      <Link to="#" onClick={handleAddPricingStrategy}>
                        <i className="material-icons">add</i>
                      </Link>
                    </div>

                    {/* Pricing Strategy List */}
                    <div className="pricing-strategy-list">
                      {formData.pricingstrategy?.map((strategy, index) => (
                        <div key={index} className="pricing-item d-flex justify-content-between align-items-center p-2 mb-2 bg-light">
                          <span>
                            {strategy.duration} - {strategy.name} (${strategy.price})
                          </span>
                          <Link to="#" className="text-danger" onClick={() => handleDeletePricingStrategy(index)}>
                            <i className="material-icons">delete</i>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button onClick={resetHandler} data-bs-dismiss="modal" className="btn gray">
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessModelModal;
