import * as url from "helper/UrlHelper";

import {
  getConvertedDateText,
  getNextHourSlot,
} from "helper/EventHelper/EventHelper";

const UserCard = ({
  userImage,
  personName,
  companyName,
  userPosition,
  totalNoReviews,
  expertRating,
  userRate,
  currentStep = 0,
  selectedMeetingDate = "",
  selectedTimeSlot = "",
}) => {
  return (
    <div className="d-flex gap-3">
      <div
        className="avatar flex-shrink-0 rounded-circle overflow-hidden"
        style={{ width: "100px", height: "100px" }}
      >
        <img
          src={
            userImage === ""
              ? "/images/default-user.png"
              : url.SERVER_URL + userImage
          }
          alt="Pierre Gagnaire"
          className="w-100 h-100 object-center object-fit-cover"
        />
      </div>
      <div>
        <h3 className="fw-semibold mb-2">{personName}</h3>
        <p className="text-gray mb-3">
          {userPosition} at {companyName}
        </p>
        <div className="d-flex gap-2 mb-4">
          <div
            className="jb_rating star_color py-2 px-3"
            style={{ height: "auto" }}
          >
            <i className="material-icons-outlined ornge_icon"> star </i>
            <span className="rating">
              {expertRating} <em>({totalNoReviews})</em>
            </span>
          </div>
          <div
            className={
              userRate === 0
                ? "d-none"
                : "rounded-90 py-2 px-3 border-green bg-light-green text-green fs-sm fw-medium"
            }
          >
            €{userRate} / Hr
          </div>
        </div>
        <h4 className="fw-bold mb-2">60 MINUTE MEETING</h4>
        <p
          className={
            currentStep === 3
              ? "d-flex align-items-center gap-1 fw-bold"
              : "d-none"
          }
        >
          <span
            className="d-block material-icons-outlined text-primary"
            style={{ fontSize: "18px" }}
          >
            schedule
          </span>
          <span className="d-block">60 min</span>
        </p>
        <p
          className={
            currentStep === 3
              ? "d-flex align-items-center gap-1 fw-bold my-2"
              : "d-none"
          }
        >
          <span
            className="d-block material-icons-outlined text-primary"
            style={{ fontSize: "18px" }}
          >
            calendar_month
          </span>
          <span className="d-block">
            {selectedTimeSlot} - {getNextHourSlot(selectedTimeSlot)},{" "}
            {getConvertedDateText(selectedMeetingDate)}
          </span>
        </p>
        <p className="d-flex align-items-center gap-1 fw-bold">
          <span
            className="d-block material-icons-outlined text-primary"
            style={{ fontSize: "18px" }}
          >
            language
          </span>
          <span className="d-block">India Standard Time</span>
        </p>
      </div>
    </div>
  );
};

export default UserCard;
