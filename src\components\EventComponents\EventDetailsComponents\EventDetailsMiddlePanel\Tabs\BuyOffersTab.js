/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

const BuyOffersTab = ({ isModerator, isApproved, tabName = "" }) => {
  const token = localStorage.getItem("token");
  const params = useParams();

  const [buyQuotations, setBuyQuotations] = useState([]);

  const [activeBuyQuotations, setActiveBuyQuotations] = useState([]);

  const [highPrice, setHighPrice] = useState(0);
  const [lowPrice, setLowPrice] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });

  // Fetch buy quotations when the component mounts or eventId changes
  const fetchBuyQuotations = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL + url.API_GET_ALL_QUOTATIONS + `?token=${token}`;

      requestURL += `&eventid=${params.id}`;
      requestURL += `&type=buy`; // Assuming type is always 'buy' for this case

      const response = await getData(requestURL);

      console.log("fetchBuyQuotations response:", response);

      setIsLoading(false);
      if (response.status) {
        setBuyQuotations(response.data);

        // active quotations are with payment not null
        setActiveBuyQuotations(
          response.data.filter((quotation) => quotation.payment !== null)
        );

        // for max and min price check all response.data and get the max and min value of totalprice
        const prices = response.data.map((quotation) => quotation.totalprice);

        if (prices.length > 0) {
          setHighPrice(Math.max(...prices));
          setLowPrice(Math.min(...prices));
        } else {
          setHighPrice(0);
          setLowPrice(0);
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  const handleSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });

    const sortedData = [...buyQuotations].sort((a, b) => {
      if (a[key] < b[key]) return direction === "ascending" ? -1 : 1;
      if (a[key] > b[key]) return direction === "ascending" ? 1 : -1;
      return 0;
    });
    setBuyQuotations(sortedData);
  };

  const getSortIcon = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === "ascending" ? "▲" : "▼";
    }
    return "⇅";
  };

  // Fetch buy quotations from the API
  useEffect(() => {
    if (params.id && tabName === "buyoffers") {
      fetchBuyQuotations();
    }
  }, [params.id, tabName]);

  if (isLoading) {
    return (
      <div className="purchase-offers p-4 rounded-10 bg-white border border-gray-400 overflow-hidden mb-3">
        <div className="d-flex flex-wrap align-items-center justify-content-between gap-2 mb-3">
          <h5 className="mb-0 placeholder-glow">
            <span
              className="placeholder col-3"
              style={{ width: "100px" }}
            ></span>
          </h5>
          <button
            className="btn py-2 px-4 btn-success disabled placeholder col-1"
            aria-hidden="true"
          ></button>
        </div>
        <div className="table-responsive mb-3">
          <table className="rounded border-gray-400 overflow-hidden mb-0 border-0 w-100">
            <thead>
              <tr>
                <th className="position-relative py-2 px-3 bg-light text-black">
                  <span className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </span>
                </th>
                <th className="position-relative py-2 px-3 bg-light text-black">
                  <span className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </span>
                </th>
                <th className="position-relative py-2 px-3 bg-light text-black">
                  <span className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </span>
                </th>
                <th className="position-relative py-2 px-3 bg-light text-black">
                  <span className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </span>
                </th>
              </tr>
            </thead>
          </table>
        </div>

        <div className="table-responsive">
          <table className="table table-bordered align-middle text-center rounded border-gray-400 overflow-hidden mb-0">
            <thead className="bg-dark text-black">
              <tr>
                <th className="text-start">
                  <span className="placeholder-glow">
                    <span className="placeholder col-3"></span>
                  </span>
                </th>
                <th className="text-start">
                  <span className="placeholder-glow">
                    <span className="placeholder col-3"></span>
                  </span>
                </th>
                <th>
                  <span className="placeholder-glow">
                    <span className="placeholder col-2"></span>
                  </span>
                </th>
                <th>
                  <span className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </span>
                </th>
                <th>
                  <span className="placeholder-glow">
                    <span className="placeholder col-2"></span>
                  </span>
                </th>
                <th>
                  <span className="placeholder-glow">
                    <span className="placeholder col-3"></span>
                  </span>
                </th>
              </tr>
            </thead>
            <tbody className="border-0">
              {[...Array(2)].map((_, index) => (
                <tr className="border-gray-400" key={index}>
                  <td className="border-gray-400">
                    <div className="d-flex align-items-center">
                      <div className="profile-img me-2 placeholder-glow">
                        <span
                          className="placeholder rounded-circle"
                          style={{ width: "30px", height: "30px" }}
                        ></span>
                      </div>
                      <p className="name">
                        <span className="placeholder-glow">
                          <span className="placeholder col-6"></span>
                        </span>
                      </p>
                    </div>
                  </td>
                  <td className="border-gray-400">
                    <span className="placeholder-glow">
                      <span className="placeholder col-8"></span>
                    </span>
                  </td>
                  <td className="border-gray-400">
                    <span className="placeholder-glow">
                      <span className="placeholder col-2"></span>
                    </span>
                  </td>
                  <td className="border-gray-400">
                    <span className="placeholder-glow">
                      <span className="placeholder col-5"></span>
                    </span>
                  </td>
                  <td className="border-gray-400">
                    <span className="placeholder-glow">
                      <span className="placeholder col-3"></span>
                    </span>
                  </td>
                  <td className="border-gray-400">
                    <span className="placeholder-glow">
                      <span className="placeholder col-4"></span>
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="purchase-offers p-4 rounded-10 bg-white border border-gray-400 overflow-hidden mb-3">
      <div className="d-flex flex-wrap align-items-center justify-content-between gap-2 mb-3">
        <h5 className="mb-0">Purchase Offers</h5>
        {isModerator || isApproved ? (
          <Link
            to="#"
            className="btn py-2 px-4 btn-success"
            data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasBuyQuotation"
          >
            Buy
          </Link>
        ) : null}
      </div>
      <div className="table-responsive mb-3 top_info_table">
        <table className="rounded-10 border-gray-400 overflow-hidden mb-0 border-0 w-100">
          <thead>
            <tr>
              <th className="position-relative py-2 px-3 bg-light text-black fs-md fw-medium">
                Offers:{" "}
                <span className="text-gray">{buyQuotations.length}</span>
              </th>
              <th className="position-relative py-2 px-3 bg-light text-black fs-md fw-medium">
                Buyers:{" "}
                <span className="text-gray">{activeBuyQuotations.length}</span>
              </th>
              <th className="position-relative py-2 px-3 bg-light text-black fs-md fw-medium">
                Low Price: <span className="text-gray">€{lowPrice} </span>
              </th>
              <th className="position-relative py-2 px-3 bg-light text-black fs-md fw-medium">
                High Price: <span className="text-gray">€{highPrice}</span>
              </th>
            </tr>
          </thead>
        </table>
      </div>

      <div className="table-responsive">
        <table className="table table-bordered align-middle fs-xs text-center text-gray rounded-10 border-gray-400 overflow-hidden mb-0">
          <thead className="bg-dark text-black">
            <tr>
              <th
                className="buyer text-start fw-bold"
                onClick={() => handleSort("customer.name")}
              >
                Buyer {getSortIcon("customer.name")}
              </th>
              <th
                className="buyer text-start fw-bold"
                onClick={() => handleSort("product.name")}
              >
                Product {getSortIcon("product.name")}
              </th>
              <th className="unit fw-bold" onClick={() => handleSort("units")}>
                Units {getSortIcon("units")}
              </th>
              <th
                className="date-of-purchase fw-bold"
                onClick={() => handleSort("createdAt")}
              >
                Date of Purchase {getSortIcon("createdAt")}
              </th>
              <th
                className="price fw-bold"
                onClick={() => handleSort("totalprice")}
              >
                Price {getSortIcon("totalprice")}
              </th>
              <th className="status fw-bold">Status</th>
            </tr>
          </thead>
          <tbody className="border-0">
            {buyQuotations &&
              buyQuotations.map((row, index) => {
                const formattedDate = new Date(row.createdAt)
                  .toLocaleDateString("en-GB")
                  .replace(/\//g, "-");
                return (
                  <tr className="border-gray-400" key={index}>
                    <td className="buyer border-gray-400">
                      <div className="buyer-profile d-flex align-items-center">
                        <a
                          href="javascript: void(0);"
                          className="profile-img me-2"
                        >
                          <img
                            src={row.buyerImage}
                            alt={row.buyerName}
                            className="img-fluid d-block w-100 h-100 object-cover object-center"
                          />
                        </a>
                        <p className="name fs-xs">
                          {row.customer?.name ?? ""}{" "}
                          {row.customer?.surname ?? ""}
                        </p>
                      </div>
                    </td>
                    <td className="unit border-gray-400">
                      <span>{row.product?.name ?? ""}</span>
                    </td>
                    <td className="unit border-gray-400">
                      <span>{row.units ?? 0}</span>
                    </td>
                    <td className="date-of-purchase border-gray-400">
                      <span>{formattedDate}</span>
                    </td>
                    <td className="price border-gray-400">
                      <span>€{row.totalprice}</span>
                    </td>
                    <td className={`status border-gray-400`}>
                      <span
                        className={`position-relative d-inline-block ${
                          row.payment ? "text-success" : "text-danger"
                        }`}
                      >
                        {row.payment ? "Active" : "Pending"}
                      </span>
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BuyOffersTab;
