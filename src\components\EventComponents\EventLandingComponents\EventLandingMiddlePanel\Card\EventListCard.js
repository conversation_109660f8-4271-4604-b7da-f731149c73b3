/* eslint-disable */
import React from "react";
import { <PERSON> } from "react-router-dom";
import * as url from "helper/UrlHelper";

//import images
import { assetImages } from "constants";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

const EventListCard = ({ eventData = {} }) => {
  const { t } = useTranslation(); // translation marker

  const memberLimit = 5; //for displaying the limit number of members

  return (
    <div className="groups_item">
      <div className="top_company_row company_top">
        <div className="left_company">
          {/* ----- event name start ----- */}
          <div className="event_hdng">
            <h3>{eventData.title}</h3>
          </div>
          {/* ----- event name end ----- */}

          <div className="event_tags">
            <ul className="list_stye_none">
              {/* ----- event review and rating start ----- */}
              <li>
                <i className="material-icons-outlined ornge_icon">star</i>
                <span className="rating">
                  {eventData.ratingtotal ?? "0.0"}{" "}
                  <em>({eventData.reviewtotal ?? "0"})</em>
                </span>
              </li>
              {/* ----- event review and rating end ----- */}

              {/* ----- event location start ----- */}
              <li className={eventData.eventlocation === "" ? "d-none" : ""}>
                <i className="material-icons-outlined">place</i>
                <span>{eventData.eventlocation}</span>
              </li>
              {/* ----- event location end ----- */}
            </ul>
          </div>
        </div>
        {/* ----- event logo start ----- */}
        {eventData.eventlogo === "" ? (
          <span
            className="placeholder rounded-8"
            style={{ width: "80px", height: "80px" }}
          ></span>
        ) : (
          <div className="company_logo">
            <img
              className="thumb_logo"
              src={url.SERVER_URL + eventData.eventlogo}
              alt=""
            />
          </div>
        )}
        {/* ----- event logo end ----- */}
      </div>

      {/* ----- event summary start ----- */}
      <div className="event_para">
        <p>{eventData.summary}</p>
      </div>
      {/* ----- event summary end ----- */}

      <div className="company_info_outer">
        {/* event category start  */}
        {eventData.categorylist && eventData.categorylist.length > 0 ? (
          <div className="company_info_row">
            <label>{t("Categories")}</label>

            <div className="event_tags">
              <ul className="list_stye_none">
                {eventData.categorylist.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">{item.name}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ) : null}

        {/* ----- event search start ----- */}
        {eventData.searchinglist && eventData.searchinglist.length > 0 ? (
          <div className="company_info_row">
            <label>{t("Search")}</label>

            <div className="event_tags">
              <ul className="list_stye_none">
                {eventData.searchinglist.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">{item.name}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ) : null}
        {/* ----- event search end ----- */}

        {/* ----- event interest start ----- */}
        {eventData.interestlist && eventData.interestlist.length > 0 ? (
          <div className="company_info_row">
            <label>Interest</label>

            <div className="event_tags">
              <ul className="list_stye_none">
                {eventData.interestlist.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">{item.name}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ) : null}
        {/* ----- event interest end ----- */}
      </div>

      {/* ----- event members start ----- */}
      <div className="event_user grps_user">
        <ul className="list_stye_none">
          {eventData.eventmemberlist
            .slice(0, memberLimit)
            .map((member, index) => {
              return (
                <li key={index}>
                  <Link to="#">
                    <img
                      height={40}
                      width={40}
                      className="rounded-circle"
                      src={
                        member.eventmemberimage === ""
                          ? assetImages.defaultUser
                          : url.SERVER_URL + member.eventmemberimage
                      }
                      alt=""
                    />
                  </Link>
                </li>
              );
            })}
          <li>
            {eventData.eventmemberlist.length > memberLimit ? (
              <Link to="#">
                +{eventData.eventmemberlist.length - memberLimit} more
              </Link>
            ) : null}
          </li>
        </ul>
      </div>
      {/* ----- event members end ----- */}

      <div className="details_btn">
        <Link to={`/events/details/${eventData._id}`} className="btn outline">
          {t("View Details")}
        </Link>
      </div>
    </div>
  );
};

export default EventListCard;
