/* eslint-disable */
import React, { useState } from "react";

const MeetingListFilterPopup = ({
  setfilterMeetingTitle,
  setfilterSentList,
  setfilterRecieveList,
  setfilterPendingList,
  setfilterAcceptList,
  setfilterMeetingType,
  setfilterMeetingDate,
}) => {
  const [fromDate, setfromDate] = useState("");
  const [toDate, settoDate] = useState("");
  const [meetingTitle, setmeetingTitle] = useState("");

  const [sentList, setsentList] = useState(false);
  const [reciveList, setreciveList] = useState(false);
  const [pendingList, setpendingList] = useState(false);
  const [acceptList, setacceptList] = useState(false);

  //function for assign search data for filter and close pop up
  const searchDataHandler = () => {
    //check start date and end date are not empty.If end date empty then only search by start date
    if (fromDate != "" && toDate != "" && fromDate < toDate) {
      setfilterMeetingDate(fromDate + "," + toDate);
    } else if (fromDate != "") {
      setfilterMeetingDate(fromDate);
    }

    if (meetingTitle !== "") {
      setfilterMeetingTitle(meetingTitle);
    }

    if (sentList) {
      setfilterMeetingType("1");
      setfilterSentList(sentList);
    }

    if (reciveList) {
      setfilterMeetingType("1");
      setfilterRecieveList(reciveList);
    }

    if (pendingList) {
      setfilterMeetingType("1");
      setfilterPendingList(pendingList);
    }

    if (acceptList) {
      setfilterMeetingType("1");
      setfilterAcceptList(acceptList);
    }

    // close the offcanvas of filter
    let offCanvasPopup = document.querySelector("#offcanvasMyMeetings");
    let offCanvas = bootstrap.Offcanvas.getInstance(offCanvasPopup);
    offCanvas.hide();
    resetHandler();
  };

  //function for reset search
  const resetHandler = () => {
    setfromDate("");
    settoDate("");
    setmeetingTitle("");
    setsentList(false);
    setreciveList(false);
    setpendingList(false);
    setacceptList(false);
  };

  return (
    <div className="offcanvas_sidebar">
      <div
        className="offcanvas offcanvas-end body_bg"
        tabIndex="-1"
        id="offcanvasMyMeetings"
        aria-labelledby="offcanvasTopLabel"
      >
        <div className="offcanvas-header justify-content-end">
          <button
            type="button"
            className="close-btn text-reset filter_cross d-flex align-items-center justify-content-center"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
          >
            <span className="material-icons-outlined">close</span>
          </button>
        </div>
        <div className="offcanvas-body">
          <div className="input_fill mb-3">
            <label className="fw-semibold text-black mb-2 fs-md">Dates</label>
            <div className="dates_outer d-flex gap-2">
              <input
                type="date"
                className="form-control"
                placeholder="From date"
                value={fromDate}
                onChange={(e) => setfromDate(e.target.value)}
              />
              <input
                type="date"
                className="form-control"
                placeholder="To date"
                value={toDate}
                onChange={(e) => settoDate(e.target.value)}
              />
            </div>
          </div>
          <div className="input_fill mb-4">
            <label className="fw-semibold text-black mb-2 fs-md">
              Search by Title
            </label>
            <input
              type="text"
              className="form-control"
              placeholder="Search by Title"
              value={meetingTitle}
              onChange={(e) => setmeetingTitle(e.target.value)}
            />
          </div>
          <div className="input_fill mb-3">
            <label className="fw-semibold text-black mb-2 fs-md">
              Select Meeting Type
            </label>

            <div className="form-check mb-3">
              <input
                className="form-check-input p-0 shadow-none"
                type="checkbox"
                checked={sentList}
                onChange={(e) => {
                  setsentList(e.target.checked);
                }}
              />
              <label className="form-check-label" htmlFor="beYourSupplier">
                Sent
              </label>
            </div>
            <div className="form-check mb-3">
              <input
                className="form-check-input p-0 shadow-none"
                type="checkbox"
                checked={reciveList}
                onChange={(e) => {
                  setreciveList(e.target.checked);
                }}
              />
              <label className="form-check-label" htmlFor="beYourPartner">
                Recieve
              </label>
            </div>
            <div className="form-check mb-3">
              <input
                className="form-check-input p-0 shadow-none"
                type="checkbox"
                checked={pendingList}
                onChange={(e) => {
                  setpendingList(e.target.checked);
                }}
              />
              <label className="form-check-label" htmlFor="iDontKnow">
                Pending
              </label>
            </div>
            <div className="form-check mb-3">
              <input
                className="form-check-input p-0 shadow-none"
                type="checkbox"
                checked={acceptList}
                onChange={(e) => {
                  setacceptList(e.target.checked);
                }}
              />
              <label className="form-check-label" htmlFor="beYourClient">
                Accepted
              </label>
            </div>
          </div>
          <div className="input_fill mb-3">
            <button className="btn w-100" onClick={searchDataHandler}>
              Search
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MeetingListFilterPopup;
