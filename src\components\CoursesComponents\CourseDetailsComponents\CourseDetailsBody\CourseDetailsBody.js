/* eslint-disable */
import { useState } from "react";
import { Link, useParams } from "react-router-dom";

import CourseDetailsBodyTopPanel from "../CourseDetailsBodyTopPanel/CourseDetailsBodyTopPanel";
import CourseDetailsBottomPanel from "../CourseDetailsBottomPanel/CourseDetailsBottomPanel";
import CourseJoinModal from "../Modals/CourseJoinModal";
import TagsModal from "components/common/Modals/TagsModal";
import TagsAddedSuccessModal from "components/common/Modals/TagsAddedSuccessModal";
import GiveReviewModal from "components/common/Modals/GiveReviewModal";
import ReviewAddedSuccessModal from "components/common/Modals/ReviewAddedSuccessModal";
import JoinSuccessModal from "../Modals/JoinSuccessModal";

const CourseDetailsBody = () => {
  const params = useParams();

  const [courseTopPanelReload, setCourseTopPanelReload] = useState(false);
  const [courseLessonTabReload, setCourseLessonTabReload] = useState(false);
  const [courseReviewTabReload, setCourseReviewTabReload] = useState(false);

  const afterReviewModalClose = () => {
    setCourseTopPanelReload(true);
    setCourseReviewTabReload(true);
  };

  const afterJoinModalClose = () => {
    setCourseTopPanelReload(true);
    setCourseLessonTabReload(true);
    setCourseReviewTabReload(true);
  };

  // useEffect(() => {
  //   const modalTarget = document.querySelector('#esg_modal');
  //   const modal = new bootstrap.Modal(modalTarget);
  //   modal.show();
  // }, []);

  return (
    <section className="feed_pnltotal">
      <div className="container-fluid">
        <div className="back_btn">
          <Link to="/courses">
            <i className="material-icons-outlined">arrow_back</i>
            <span>Back</span>
          </Link>
        </div>
        <div className="pnl_outer lesson_pnl_outer">
          {/* -------- top panel -------- */}
          <CourseDetailsBodyTopPanel
            courseTopPanelReload={courseTopPanelReload}
            setCourseTopPanelReload={setCourseTopPanelReload}
          />

          {/* -------- bottom panel ------- */}
          <CourseDetailsBottomPanel
            courseReviewTabReload={courseReviewTabReload}
            setCourseReviewTabReload={setCourseReviewTabReload}
            courseLessonTabReload={courseLessonTabReload}
            setCourseLessonTabReload={setCourseLessonTabReload}
          />
        </div>
      </div>

      <CourseJoinModal />

      {/* ------- feedback modal -------- */}
      <GiveReviewModal moduleName="course" />

      <ReviewAddedSuccessModal afterModalClose={afterReviewModalClose} />

      {/* ----------- tags modal --------------- */}
      <TagsModal moduleName="coursedetails" selectedId={params.id} />

      {/* --------- tags added success / greetings modal ------------- */}
      <TagsAddedSuccessModal
        afterTagModalClose={() => {
          setCourseTopPanelReload(true);
        }}
      />

      {/* success modal for join  */}
      <JoinSuccessModal afterModalClose={afterJoinModalClose} />
    </section>
  );
};

export default CourseDetailsBody;
