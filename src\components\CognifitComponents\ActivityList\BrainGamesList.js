/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

const BrainGamesList = () => {
  const token = localStorage.getItem("token"); // user token after login

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [fetchingAccessToken, setFetchingAccessToken] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const [userToken, setUserToken] = useState(localStorage.getItem("cognifitToken") || '');
  const [accessToken, setAccessToken] = useState('');
  const [clientId, setClientId] = useState('');

  const [brainGames, setBrainGames] = useState([]);

  const [selectedGameKey, setSelectedGameKey] = useState('');


  // get the cognifit token
  const getCognifitToken = async (gamekey) => {
    setFetchingAccessToken(true);
    setErrorMessage('');

    setSelectedGameKey(gamekey);

    try {
      let requestURL = url.API_BASE_URL + url.API_COGNIFIT_TOKEN + `?token=${token}`;

      console.log('requestURL braingame list: ', requestURL);

      // get token using post method
      const response = await getData(requestURL);

      console.log('Response for getCognifitToken: ', response);

      if (response.token) {
        setUserToken(response.token);
        setAccessToken(response.accesstoken);

        localStorage.setItem("cognifitToken", response.token);

        console.log("Cognifit Token: ", response.token);
        console.log("Cognifit Access Token: ", response.accesstoken);
      }else{
        setErrorMessage('Failed to fetch Cognifit token. Please try again.');
        console.error("Error fetching Cognifit token: ", response);
      }

    } catch (error) {
      console.log(error.message);
    } finally {
      setFetchingAccessToken(false);
    }

  };

  //get activities from congnifit
  const getCognifitBrainGamesList = async () => {
    setIsLoading(true);
    setErrorMessage('');

    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_COGNIFIT_ACTIVITY_LIST +`/?token=${token}`;

      const response = await getData(requestURL);

      console.log('requestURL: ', requestURL);

      console.log('Response for getCognifitBrainGamesList: ',response);

      setIsLoading(false);

      if(response && response.data) {
        setBrainGames(response.data);
        setClientId(response.clientid);
      }
    
    } catch (error) {
      console.log(error.message);
    }
  };


  useEffect(() => {
      getCognifitBrainGamesList();
  }, []);

  useEffect(() => {
    if(clientId && accessToken && selectedGameKey) {
      window.location.href = `${url.COGNIFIT_LAUNCH_URL}/launch?clientId=${clientId}&accessToken=${accessToken}&gameKey=${selectedGameKey}`;
    } 
  }, [clientId, accessToken, selectedGameKey]);

  return (
    <section className="">
      <div className="container-fluid">
        
        <div className="faq_pnl_wrapper">
          
            <div className="shadow-sm p-1 rounded-10 bg-white mb-3">
              {/* title  */}
              <h2 className="fs-xl text-black mb-3 fw-semibold">Cognifit Brain Game</h2>
              {errorMessage && errorMessage.length > 0 && (
                <div className="alert alert-danger" role="alert">
                  {errorMessage}
                </div>
              )}
              {/* Grid of brain games */}
              <div className="row">
                {isLoading ? (
                  // Placeholder cards while loading
                  Array.from({ length: 6 }).map((_, idx) => (
                    <div className="col-md-4 col-sm-6 mb-4" key={idx}>
                      <div className="card h-100 shadow-sm">
                        <div
                          className="card-img-top placeholder-glow"
                          style={{
                            height: "160px",
                            background: "#e0e0e0",
                            borderRadius: "8px"
                          }}
                        />
                        <div className="card-body">
                          <h5 className="card-title placeholder-glow">
                            <span className="placeholder col-8"></span>
                          </h5>
                          <p className="card-text placeholder-glow">
                            <span className="placeholder col-12"></span>
                            <span className="placeholder col-10"></span>
                          </p>
                          <span className="badge bg-secondary placeholder col-4"></span>
                          <div className="mt-3">
                            <span className="btn btn-outline-primary btn-sm disabled placeholder col-6"></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : brainGames && brainGames.length > 0 ? (
                  brainGames.map((game) => (
                    <div className="col-md-4 col-sm-6 mb-4" key={game.key}>
                      <div className="card h-100 shadow-sm">
                        <img
                          src={game.assets?.images?.icon}
                          alt={game.assets?.titles?.es}
                          className="card-img-top"
                          style={{ height: "160px", objectFit: "cover" }}
                        />
                        <div className="card-body">
                          <h5 className="card-title">{game.assets?.titles?.es}</h5>
                          <p className="card-text">
                            {game.assets?.descriptions?.es
                              ? game.assets.descriptions.es.slice(0, 50) + (game.assets.descriptions.es.length > 50 ? "..." : "")
                              : ""}
                          </p>
                          <span className="badge bg-secondary">{game.key}</span>
                          <div className="mt-3 d-flex align-items-center">
                            <Link
                              to="#"
                              className={`btn btn-outline-primary btn-sm`}
                              onClick={() => getCognifitToken(game.key)}
                              disabled={fetchingAccessToken}
                              style={fetchingAccessToken ? { pointerEvents: "none", opacity: 0.7 } : {}}
                            >
                              Jugar ahora
                            </Link>

                            {fetchingAccessToken && selectedGameKey === game.key && (
                              <span
                                className={`spinner-border spinner-border-sm ms-2 ${game.key} `}
                                role="status"
                                aria-hidden="true"
                                style={{ verticalAlign: "middle" }}
                              ></span>
                            )}

                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-12">
                    <p>No activities found.</p>
                  </div>
                )}
              </div>
            </div>
        

      
        </div>
      </div>

   
    </section>
  );
};

export default BrainGamesList; // Exporting the component