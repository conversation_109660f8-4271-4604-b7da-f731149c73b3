/* eslint-disable */
import React, { useContext, useState, useEffect } from "react";
import { useParams } from "react-router-dom";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { postData, getData } from "utils/Gateway";
import {
  getConvertedDateText,
  getNextHourSlot,
} from "helper/EventHelper/EventHelper";

const MeetingDetailsModal = ({
  personName,
  selectedTimeSlot,
  selectedMeetingDate,
  meetingReason,
  comments,
}) => {
  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [meetingDetails, setMeetingDetails] = useState("");
  const [meetingSaving, setMeetingSaving] = useState(false);

  const scheduleMeetingHandler = async () => {
    setMeetingSaving(true);
    try {
      let scheduleData = {
        reciever: params.id,
        meetingdate: selectedMeetingDate,
        starttime: selectedTimeSlot,
        endtime: getNextHourSlot(selectedTimeSlot),
        message: comments,
        meetingdetails: meetingDetails,
        meetingreason: meetingReason,
      };

      let requestUrl =
        url.API_BASE_URL + url.API_SAVE_SCHEDULE_MEETING + `?token=${token}`;

      const response = await postData(requestUrl, scheduleData);

      console.log(response);

      setMeetingSaving(false);

      if (response.status) {
        //hide login modal
        let loginModal = document.querySelector("#meeting_details");
        let modal = bootstrap.Modal.getInstance(loginModal);
        modal.hide();

        setMeetingDetails("");

        //show register modal
        let myModal = new bootstrap.Modal(
          document.getElementById("schedule_confirm")
        );
        myModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  return (
    <div className="post_modal request_modal">
      <div
        className="modal fade"
        id="meeting_details"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-body">
              <div className="row">
                <div className="col-sm-6 mb-4 mb-sm-0">
                  <p className="fw-bold mb-2">{personName}</p>
                  <h4 className="fw-bold mb-2">60 MINUTE MEETING</h4>
                  <p className="d-flex align-items-center gap-1 fw-bold mb-2">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      schedule
                    </span>
                    <span className="d-block">60 min</span>
                  </p>
                  <p className="d-flex align-items-center gap-1 fw-bold mb-2">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      calendar_month
                    </span>
                    <span className="d-block">
                      {selectedTimeSlot} - {getNextHourSlot(selectedTimeSlot)},{" "}
                      {getConvertedDateText(selectedMeetingDate)}
                    </span>
                  </p>
                  <p className="d-flex align-items-center gap-1 fw-bold">
                    <span
                      className="d-block material-icons-outlined text-primary"
                      style={{ fontSize: "18px" }}
                    >
                      language
                    </span>
                    <span className="d-block">India Standard Time</span>
                  </p>
                </div>
                <div className="col-sm-6">
                  <h4 className="fw-bold mb-4">ENTER DETAILS</h4>
                  <div className="form-group mb-3">
                    <label htmlFor="name">
                      {userInfo?.name} {userInfo?.surname}
                    </label>
                  </div>
                  <div className="form-group mb-3">
                    <label htmlFor="email">{userInfo?.email}</label>
                  </div>
                  <div className="form-group">
                    <label htmlFor="meetingDetails">Meeting Details</label>
                    <textarea
                      name="meetingDetails"
                      id="meetingDetails"
                      cols="30"
                      rows="5"
                      className="form-control"
                      placeholder="Enter details"
                      value={meetingDetails}
                      onChange={(e) => setMeetingDetails(e.target.value)}
                    ></textarea>
                  </div>
                </div>
              </div>
              <div className="post_btn d-flex justify-content-between">
                <button
                  className="btn outline w-auto"
                  data-bs-dismiss="modal"
                  data-bs-toggle="modal"
                  data-bs-target="#meeting_setup"
                  onClick={() => setMeetingDetails("")}
                >
                  Back
                </button>
                <button
                  className="btn outline bg-primary text-white w-auto"
                  onClick={scheduleMeetingHandler}
                  disabled={meetingSaving ? true : false}
                  style={{
                    cursor: meetingSaving ? "not-allowed" : "pointer",
                  }}
                >
                  Schedule Meeting
                  {meetingSaving && (
                    <div
                      className="mx-2 spinner-border spinner-border-sm"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MeetingDetailsModal;
