import React, { useState } from "react";

const FilterPopup = ({ cognitiveSkills = [], onFilter }) => {
  const [selectedSkills, setSelectedSkills] = useState([]);

  const handleCheckboxChange = (key) => {
    setSelectedSkills((prev) =>
      prev.includes(key)
        ? prev.filter((k) => k !== key)
        : [...prev, key]
    );
  };

  const handleFilter = () => {
    if (onFilter) onFilter(selectedSkills);
    const offcanvas = window.bootstrap?.Offcanvas?.getOrCreateInstance(document.getElementById("cognifit_filter_offcanvas"));
    if (offcanvas) offcanvas.hide();
  };

  const handleClear = () => {
    setSelectedSkills([]);
    if (onFilter) onFilter([]);
  };

  return (
    <div>
      <div className="offcanvas offcanvas-end" tabIndex="-1" id="cognifit_filter_offcanvas">
        <div className="offcanvas-header">
          <h5 className="offcanvas-title">Filtrar por habilidades</h5>
          <button type="button" className="btn-close" data-bs-dismiss="offcanvas" aria-label="Cerrar"></button>
        </div>
        <div className="offcanvas-body" style={{ paddingBottom: "70px" }}>
          <div>
            {cognitiveSkills.length === 0 && <div>No se encontraron habilidades.</div>}
            <ul className="list-unstyled">
              {cognitiveSkills.map((skill) => (
                <li key={skill.key} className="mb-3">
                  <label style={{ fontSize: "1.1rem", display: "flex", alignItems: "center", cursor: "pointer" }}>
                    <input
                      type="checkbox"
                      checked={selectedSkills.includes(skill.key)}
                      onChange={() => handleCheckboxChange(skill.key)}
                      className="form-check-input me-2"
                      style={{ width: "1.3em", height: "1.3em" }}
                    />
                    {skill.assets?.titles?.es || skill.key}
                  </label>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div
          className="offcanvas-footer bg-white border-top d-flex gap-2 justify-content-end"
          style={{
            position: "absolute",
            bottom: 0,
            left: 0,
            width: "100%",
            padding: "16px",
            zIndex: 10,
          }}
        >
          <button className="btn btn-secondary" type="button" onClick={handleClear}>
            Limpiar
          </button>
          <button className="btn btn-primary" type="button" onClick={handleFilter}>
            Filtrar
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterPopup;
