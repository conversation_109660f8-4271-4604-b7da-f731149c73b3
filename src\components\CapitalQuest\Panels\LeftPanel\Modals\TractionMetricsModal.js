/* eslint-disable */
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";

const TractionMetricsModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });
  const [testimonial, setTestimonial] = useState(""); // For new testimonial input

  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleAddTestimonial = () => {
    if (testimonial.trim()) {
      setFormData((prevData) => ({
        ...prevData,
        customertestimonials: [...(prevData.customertestimonials || []), testimonial]
      }));
      setTestimonial("");
    }
  };

  const handleDeleteTestimonial = (index) => {
    setFormData((prevData) => ({
      ...prevData,
      customertestimonials: prevData.customertestimonials.filter((_, i) => i !== index)
    }));
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
    setTestimonial("");
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      mainkpis: formData.mainkpis,
      customerevolution: formData.customerevolution,
      growthrate: formData.growthrate,
      revenue: formData.revenue,
      cacandcltv: formData.cacandcltv,
      stategicpartnerships: formData.stategicpartnerships,
      customertestimonials: formData.customertestimonials
    }));
    let modal = document.querySelector("#traction_metrics_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="traction_metrics_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Traction and Metrics</h3>
                <h5>Provide details about your traction and metrics</h5>
              </div>
              <button type="button" className="close" data-bs-dismiss="modal" aria-label="Close" onClick={resetHandler}>
                <i className="material-icons-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Main KPIs</label>
                    <textarea
                      className="form-control"
                      name="mainkpis"
                      value={formData.mainkpis || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Main KPIs"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Customer Evolution</label>
                    <textarea
                      className="form-control"
                      name="customerevolution"
                      value={formData.customerevolution || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Customer Evolution"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Growth Rate</label>
                    <textarea
                      className="form-control"
                      name="growthrate"
                      value={formData.growthrate || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Growth Rate"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Revenue</label>
                    <textarea
                      className="form-control"
                      name="revenue"
                      value={formData.revenue || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Revenue"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>CAC and CLTV</label>
                    <textarea
                      className="form-control"
                      name="cacandcltv"
                      value={formData.cacandcltv || ""}
                      onChange={handleInputChange}
                      placeholder="Enter CAC and CLTV"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Strategic Partnerships</label>
                    <textarea
                      className="form-control"
                      name="stategicpartnerships"
                      value={formData.stategicpartnerships || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Strategic Partnerships"
                    ></textarea>
                  </div>
                  
                  {/* Customer Testimonials Section */}
                  <div className="form-group">
                    <label>Customer Testimonials</label>
                    <div className="d-flex gap-2 mb-2">
                      <input
                        type="text"
                        className="form-control"
                        value={testimonial}
                        onChange={(e) => setTestimonial(e.target.value)}
                        placeholder="Enter Customer Testimonial"
                      />
                      <Link
                        to="#"
                        onClick={handleAddTestimonial}
                      >
                        <i className="material-icons">add</i>
                      </Link>
                    </div>
                    
                    {/* Testimonials List */}
                    <div className="testimonials-list">
                      {formData.customertestimonials?.map((item, index) => (
                        <div key={index} className="testimonial-item d-flex justify-content-between align-items-center p-2 mb-2 bg-light">
                          <span>{item}</span>
                          <Link
                            to="#"
                            className="text-danger"
                            onClick={() => handleDeleteTestimonial(index)}
                          >
                            <i className="material-icons">delete</i>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button onClick={resetHandler} data-bs-dismiss="modal" className="btn gray">
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TractionMetricsModal;
