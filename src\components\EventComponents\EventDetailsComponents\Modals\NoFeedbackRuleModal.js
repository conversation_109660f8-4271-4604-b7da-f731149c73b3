import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const NoFeedbackRuleModal = () => {
  const { t } = useTranslation(); // translation marker
  return (
    <div className="digital_success_modal">
      <div
        className="modal fade"
        id="no_feedback_modal"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-body">
              <div className="kit_success">
                <div className="kit_rewards_icon">
                  <img src="/images/empty-graphics.svg" alt="" />
                </div>
                <h2>{t("Sorry")}</h2>
                <p>{t("There is no any feedback rule for this event.")}</p>
                <Link to="#" data-bs-dismiss="modal" className="btn">
                  Close
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoFeedbackRuleModal;
