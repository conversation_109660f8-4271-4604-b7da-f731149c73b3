export const ecosystemSlug = localStorage.getItem("ecosystemslug") || "LOBEES";

export const storeOwnerEmail = "<EMAIL>";

export const stripekey =
  "pk_test_51IJm7YL6PquBzXKuvGXoEkT32UbYTvpafT4Wf6naaoNezsrD9Lt1juEzQd4SJzJWscPci5jr2LRbUcUxu5wUu2Pn00kTokCrRA";

export const ckEditorConfig = (placeholderText = "") => {
  return {
    toolbar: [
      "bold",
      "italic",
      "|",
      "bulletedList",
      "numberedList",
      "|",
      "outdent",
      "indent",
      "|",
      "undo",
      "redo",
    ],
    placeholder: placeholderText,
    shouldNotGroupWhenFull: false,
  };
};

export const reactSelectStyle = (
  padding = "0px",
  borderRadius = "0px",
  bgRequired = false,
  showBorder = true,
  minHeightRequired = false
) => {
  return {
    control: (styles, { isFocused }) => ({
      ...styles,
      backgroundColor: bgRequired ? "var(--bs-color-ED)" : "#ffffff", // Control background
      border: showBorder
        ? "1px solid var(--bs-color-AA)"
        : "1px solid var(--bs-color-DD)",
      borderRadius, // Border radius
      padding,
      minHeight: minHeightRequired ? "48px" : "auto",
      boxShadow: "0 !important",
      "&:hover": {}, // Focus shadow effect
    }),
  };
};
