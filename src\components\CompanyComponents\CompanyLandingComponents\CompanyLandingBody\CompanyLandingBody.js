import React from "react";
import CompanyLandingLeftPanel from "../CompanyLandingLeftPanel/CompanyLandingLeftPanel";
import CompanyLandingMiddlePanel from "../CompanyLandingMiddlePanel/CompanyLandingMiddlePanel";
import CompanyLandingRightPanel from "../CompanyLandingRightPanel/CompanyLandingRightPanel";
import AllFilterModal from "components/common/Modals/AllFilterModal";

const CompanyLandingBody = () => {
  return (
    <section className="feed_pnltotal">
      <div className="container-fluid">
        <div className="pnl_outer">
          {/* --- left panel --- */}
          <CompanyLandingLeftPanel />

          {/* --- Middle panel --- */}
          <CompanyLandingMiddlePanel />

          {/* ---- right panel ---- */}
          <CompanyLandingRightPanel />
        </div>
      </div>

      {/* -------- all filter modal -------- */}
      <AllFilterModal />
    </section>
  );
};

export default CompanyLandingBody;
