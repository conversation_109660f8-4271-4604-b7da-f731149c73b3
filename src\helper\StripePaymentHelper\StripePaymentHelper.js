/* eslint-disable */

import { stripekey } from "Config/Config";
import { loadStripe } from "@stripe/stripe-js";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData, putData } from "utils/Gateway";

// fetch the invoice details, and then redirect to the Stripe checkout page
export const makeStripePayment = async (
  invoiceid,
  token,
  userid,
  redirectUrl = null
) => {
  if (!invoiceid) {
    return;
  }

  try {
    const invoiceResponseGetRequestUrl =
      url.API_BASE_URL +
      url.API_GET_INVOICE_DETAILS +
      `/${invoiceid}?token=${token}`;

    const invoiceGetResponse = await getData(invoiceResponseGetRequestUrl);

    // console.log("make payment");

    let successUrl = url.FRONTEND_BASE_URL + "/payment/success";
    let failureUrl = url.FRONTEND_BASE_URL + "/payment/failure";

    if (invoiceid) {
      successUrl = successUrl + `?ref=${invoiceid}`;
    }

    if (redirectUrl) {
      successUrl = successUrl + `&redirectUrl=${redirectUrl}`;
    }

    const stripe = await loadStripe(stripekey);

    let requestUrl =
      url.API_BASE_URL + url.API_CART_CHECKOUT_SESSION + `?token=${token}`;

    const paymentInfo = {
      cartItems: invoiceGetResponse.data.stripecartitems,
      userId: userid,
      invoiceid: invoiceid,
      successurl: successUrl,
      failureurl: failureUrl,
    };

    const response = await postData(requestUrl, paymentInfo);

    console.log("response", response);

    if (response.id) {
      stripe.redirectToCheckout({
        sessionId: response.id,
      });
    }
  } catch (error) {
    console.log("Error", error.message);
  }
};
