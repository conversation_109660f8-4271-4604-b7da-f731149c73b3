/* eslint-disable */
import React, { useEffect, useContext } from "react";
import AppUserDetailsBody from "components/AppUserComponents/AppUserDetailsComponents/AppUserDetailsBody/AppUserDetailsBody";
import AuthHeader from "components/common/AuthHeader";

const AppUserDetails = () => {
  useEffect(() => {
    document.title = "User Details";
  }, []);

  return (
    <>
      {/* ------- header section ---------- */}
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ------- body section ------------ */}
      <AppUserDetailsBody />
    </>
  );
};

export default AppUserDetails;
