/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

const TeamsTab = ({ tabName = "" }) => {
  const { t } = useTranslation();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const params = useParams();

  const [memberList, setMemberList] = useState([]);
  const [isLoading, setisLoading] = useState(false);

  //function for get all event members
  const getAllChallengeMembers = async () => {
    try {
      setisLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_JOB_MEMBER +
        `?token=${token}&challenge=${params.id}`;

      const response = await getData(requestUrl);

      console.log(response);

      setisLoading(false);

      if (response.status) {
        setMemberList(response.data);
      }
    } catch (error) {
      console.log(error.message);
      setisLoading(false);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "members") {
      getAllChallengeMembers();
    }
  }, [params.id, tabName]);

  // Loading placeholder component
  const LoadingPlaceholder = () => (
    <div className="post_item">
      {[1, 2, 3].map((item, index) => (
        <div
          className="white_shadow_bx people_bx position-relative overflow-hidden"
          key={index}
        >
          <div className="people_row">
            <div className="left_people">
              <div className="people_profile">
                <div
                  className="glow-placeholder"
                  style={{ width: "60px", height: "60px", borderRadius: "50%" }}
                ></div>
              </div>
              <div className="people_profile_info">
                <h3
                  className="glow-placeholder"
                  style={{ width: "160px", height: "20px" }}
                ></h3>
                <h5
                  className="glow-placeholder"
                  style={{ width: "200px", height: "16px", marginTop: "8px" }}
                ></h5>
                <div className="event_tags">
                  <ul className="list_stye_none mb-0">
                    <li>
                      <i className="material-icons-outlined ornge_icon glow-placeholder">
                        star
                      </i>
                      <span
                        className="rating glow-placeholder"
                        style={{ width: "80px", height: "16px" }}
                      ></span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="right_people_btn">
              <div
                className="btn outline glow-placeholder"
                style={{ width: "110px", height: "36px" }}
              ></div>
            </div>
          </div>
          <style jsx>{`
            .glow-placeholder {
              animation: glow 1.5s ease-in-out infinite;
              background: #f6f7f8;
              background: linear-gradient(
                to right,
                #f6f7f8 8%,
                #edeef1 18%,
                #f6f7f8 33%
              );
              background-size: 800px 104px;
              border-radius: 4px;
              display: inline-block;
            }
            @keyframes glow {
              0% {
                background-position: -200px 0;
              }
              100% {
                background-position: 200px 0;
              }
            }
          `}</style>
        </div>
      ))}
    </div>
  );

  return (
    <div className="post_item_outer">
      {isLoading ? (
        <LoadingPlaceholder />
      ) : memberList.length === 0 ? (
        <div className="pnl_mdlpnl w-100">
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-md text-gray fw-semibold">
                Sorry....! No Team member availbale at this moment
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="post_item">
          {memberList.map((member, index) => {
            return (
              <div className="white_shadow_bx people_bx" key={index}>
                <div className="people_row">
                  <div className="left_people">
                    <div className="people_profile">
                      <img
                        src={
                          member.image == ""
                            ? assetImages.defaultUser
                            : url.SERVER_URL + member.image
                        }
                        alt=""
                      />
                    </div>
                    <div className="people_profile_info">
                      <h3>{member.name}</h3>
                      <h5>{member.email}</h5>
                      <div className="event_tags">
                        <ul className="list_stye_none mb-0">
                          <li>
                            <i className="material-icons-outlined ornge_icon">
                              star
                            </i>
                            <span className="rating">
                              {member.totalratings}{" "}
                              <em>({member.totalreviews})</em>
                            </span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div className="right_people_btn">
                    <Link
                      to={`/peoples/details/${member.userid}`}
                      className="btn outline"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default TeamsTab;
