import React from "react";
import { Link } from "react-router-dom";
// import use translation ------------------------------
import { useTranslation } from "react-i18next";
import { assetImages } from "constants";

const ConnectBanner = () => {
  const { t } = useTranslation(); // translation marker
  return (
    <section
      className="banner_area"
      style={{ backgroundImage: `url(${assetImages.connectBannerDotsImg})` }}
    >
      <div className="container">
        <div className="banner_outer">
          <div className="row">
            <div className="col-lg-12">
              <div className="banner_content">
                <h1>
                  <span>{t("Connecting")}</span> <br />{" "}
                  {t("Companies With Professionals")}{" "}
                </h1>
                <p>
                  {t(
                    "Tools that enables an Ecosystem to Digitize your business opportunities and processes"
                  )}
                </p>
                <div className="bnr_btns">
                  <Link
                    to="#"
                    className="btn"
                    data-bs-toggle="modal"
                    data-bs-target="#register_mdl"
                  >
                    {t("Sign up for free")}
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ConnectBanner;
