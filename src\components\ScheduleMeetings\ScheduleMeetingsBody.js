/* eslint-disable  */
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import momentjs from "moment";

import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

import StepOne from "./Steps/StepOne";
import StepTwo from "./Steps/StepTwo";
import StepThree from "./Steps/StepThree";
import StepFour from "./Steps/StepFour";

import { getNextHourSlot } from "helper/EventHelper/EventHelper";

const ScheduleMeetingsBody = () => {
  const params = useParams();
  const todayValue = new Date().toISOString().split("T")[0];

  const [progressBarWidth, setProgressBarWidth] = useState({ width: "20%" });

  const [currentStep, setCurrentStep] = useState(1);

  const [stepOneStyle, setStepOneStyle] = useState({});
  const [stepTwoStyle, setStepTwoStyle] = useState({ display: "none" });
  const [stepThreeStyle, setStepThreeStyle] = useState({ display: "none" });
  const [stepFourStyle, setStepFourStyle] = useState({ display: "none" });

  const [userName, setUserName] = useState("");
  const [userImage, setUserImage] = useState("");
  const [userPosition, setuserPosition] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [userRate, setUserRate] = useState(0);
  const [expertRating, setExpertRating] = useState("");
  const [totalNoReviews, setTotalNoReviews] = useState("");

  //meeting requirements step 1
  const [comments, setcomments] = useState("");
  const [meetingReason, setmeetingReason] = useState("");

  const [reasonError, setreasonError] = useState(false);
  const [commentError, setcommentError] = useState(false);

  // step 2
  const [timeSlotList, setTimeSlotList] = useState([
    {
      slot: "09:00",
      availability: false,
    },
    {
      slot: "10:00",
      availability: false,
    },
    {
      slot: "11:00",
      availability: false,
    },
    {
      slot: "12:00",
      availability: false,
    },
    {
      slot: "13:00",
      availability: false,
    },
    {
      slot: "14:00",
      availability: false,
    },
    {
      slot: "15:00",
      availability: false,
    },
    {
      slot: "16:00",
      availability: false,
    },
    {
      slot: "17:00",
      availability: false,
    },
    {
      slot: "18:00",
      availability: false,
    },
    {
      slot: "19:00",
      availability: false,
    },
    {
      slot: "20:00",
      availability: false,
    },
    {
      slot: "21:00",
      availability: false,
    },
  ]);

  const [dateValue, setDateValue] = useState(momentjs(todayValue));

  const [selectedMeetingDate, setSelectedMeetingDate] = useState("");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [resetAvailibilty, setResetAvailibilty] = useState(false);

  //step 3
  const [userInputName, setuserInputName] = useState("");
  const [userEmail, setuserEmail] = useState("");
  const [meetingDetails, setMeetingDetails] = useState("");
  const [emailError, setemailError] = useState(false);

  const [meetingSaving, setMeetingSaving] = useState(false);

  //function for get user details
  const getUserDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL + url.API_GET_USER_DETAILS_BY_ID + `/${params.id}`;

      const response = await getData(requestUrl);

      console.log("details resp======>", response);

      if (response.status && response.data) {
        const data = response.data;
        // setUserAccountId(data._id);
        setUserImage(data.userimage); // Ensure default value if undefined

        const userFullName = data.name
          ? `${data.name} ${data.surname ?? ""}`
          : data.email;
        setUserName(userFullName);
        setuserPosition(data.position || "");
        setCompanyName(data.company || "");
        setUserRate(data.costperhour || 0);
        setExpertRating(data.totalratings || "0.0");
        setTotalNoReviews(data.totalreviews || "0");
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const nextStepHandler = () => {
    if (currentStep === 1) {
      if (comments === "" || meetingReason === "") {
        comments === "" && setcommentError(true);
        meetingReason === "" && setreasonError(true);
      } else {
        setcommentError(false);
        setreasonError(false);
        setCurrentStep(2);
        setStepOneStyle({ display: "none" });
        setStepTwoStyle({ display: "block" });
        setStepThreeStyle({ display: "none" });
        setStepFourStyle({ display: "none" });

        setProgressBarWidth({ width: "50%" });
      }
    }

    if (currentStep === 2) {
      if (selectedTimeSlot !== "") {
        setCurrentStep(3);

        setStepOneStyle({ display: "none" });
        setStepTwoStyle({ display: "none" });
        setStepThreeStyle({ display: "block" });
        setStepFourStyle({ display: "none" });

        setProgressBarWidth({ width: "75%" });
      } else {
        const hasAvailableSlot = timeSlotList.some(
          (slot) => slot.availability === true
        );

        if (hasAvailableSlot) {
          setErrorMessage("Please select a time slot");
        } else {
          setErrorMessage(
            "Sorry! No slots available in this date.Try a different date. "
          );
        }
      }
    }

    if (currentStep === 3) {
      const mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
      if (userEmail === "" || !userEmail.match(mailformat)) {
        setemailError(true);
      } else {
        scheduleMeetingHandler();
      }
    }
  };

  const prevResetStepHandler = () => {
    if (currentStep === 2 || currentStep === 4) {
      setCurrentStep(1);
      setStepOneStyle({ display: "block" });
      setStepTwoStyle({ display: "none" });
      resetScheduleMeet();
      resetMeetingForm();
      setErrorMessage("");
      setResetAvailibilty(true);
      setStepFourStyle({ display: "none" });
      setProgressBarWidth({ width: "25%" });
    }

    if (currentStep === 3) {
      resetMeetingForm();
      setCurrentStep(2);
      setStepOneStyle({ display: "none" });
      setStepTwoStyle({ display: "block" });
      setStepThreeStyle({ display: "none" });
      setProgressBarWidth({ width: "50%" });
    }
  };

  const scheduleMeetingHandler = async () => {
    setMeetingSaving(true);
    try {
      let scheduleData = {
        name: userInputName,
        senderemail: userEmail,
        reciever: params.id,
        meetingdate: selectedMeetingDate,
        starttime: selectedTimeSlot,
        endtime: getNextHourSlot(selectedTimeSlot),
        message: comments,
        meetingdetails: meetingDetails,
        meetingreason: meetingReason,
      };

      let requestUrl =
        url.API_BASE_URL + url.API_SAVE_SCHEDULE_MEETING_BY_GUEST;

      const response = await postData(requestUrl, scheduleData);

      console.log(response);

      setMeetingSaving(false);

      if (response.status) {
        setCurrentStep(4);
        setStepOneStyle({ display: "none" });
        setStepTwoStyle({ display: "none" });
        setStepThreeStyle({ display: "none" });
        setStepFourStyle({ display: "block" });
        setProgressBarWidth({ width: "100%" });
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //resetScheduleMeet
  const resetScheduleMeet = () => {
    setmeetingReason("");
    setcomments("");
    setSelectedMeetingDate("");
    setSelectedTimeSlot("");
    setDateValue(momentjs(todayValue));
  };

  //reset meeting form
  const resetMeetingForm = () => {
    setuserInputName("");
    setuserEmail("");
    setMeetingDetails("");
    setemailError(false);
  };

  useEffect(() => {
    if (params.id) {
      getUserDetails();
    }
  }, [params.id]);

  return (
    <section className="schedule-meeting py-5">
      <div className="container">
        <div className="bg-white rounded-4 shadow-sm overflow-hidden">
          <div
            className="progress"
            role="progressbar"
            aria-label="Example 1px high"
            aria-valuenow="20"
            aria-valuemin="0"
            aria-valuemax="100"
            style={{ height: "8px" }}
          >
            <div className="progress-bar" style={progressBarWidth}></div>
          </div>
          <div className="p-4">
            <StepOne
              componentStyle={stepOneStyle}
              personName={userName}
              userImage={userImage}
              userPosition={userPosition}
              companyName={companyName}
              totalNoReviews={totalNoReviews}
              expertRating={expertRating}
              totalratings={expertRating}
              userRate={userRate}
              comments={comments}
              setcomments={setcomments}
              meetingReason={meetingReason}
              setmeetingReason={setmeetingReason}
              commentError={commentError}
              setcommentError={setcommentError}
              reasonError={reasonError}
              setreasonError={setreasonError}
            />

            <StepTwo
              componentStyle={stepTwoStyle}
              personName={userName}
              userImage={userImage}
              userPosition={userPosition}
              companyName={companyName}
              totalNoReviews={totalNoReviews}
              expertRating={expertRating}
              totalratings={expertRating}
              timeSlotList={timeSlotList}
              setTimeSlotList={setTimeSlotList}
              userRate={userRate}
              dateValue={dateValue}
              setSelectedMeetingDate={setSelectedMeetingDate}
              setDateValue={setDateValue}
              errorMessage={errorMessage}
              selectedTimeSlot={selectedTimeSlot}
              setSelectedTimeSlot={setSelectedTimeSlot}
              setErrorMessage={setErrorMessage}
              resetAvailibilty={resetAvailibilty}
              setResetAvailibilty={setResetAvailibilty}
            />

            <StepThree
              componentStyle={stepThreeStyle}
              personName={userName}
              userImage={userImage}
              userPosition={userPosition}
              companyName={companyName}
              totalNoReviews={totalNoReviews}
              expertRating={expertRating}
              totalratings={expertRating}
              userRate={userRate}
              selectedMeetingDate={selectedMeetingDate}
              selectedTimeSlot={selectedTimeSlot}
              userInputName={userInputName}
              setuserInputName={setuserInputName}
              userEmail={userEmail}
              setuserEmail={setuserEmail}
              meetingDetails={meetingDetails}
              setMeetingDetails={setMeetingDetails}
              emailError={emailError}
              setemailError={setemailError}
            />

            <StepFour
              componentStyle={stepFourStyle}
              personName={userName}
              selectedMeetingDate={selectedMeetingDate}
              selectedTimeSlot={selectedTimeSlot}
            />
          </div>
        </div>
        <div
          className={`process_btns_outer d-flex align-items-center mt-5 ${
            currentStep === 4 ? "justify-content-center" : ""
          }`}
        >
          <button
            className={
              currentStep === 1
                ? "d-none"
                : `${
                    currentStep === 4
                      ? "btn btn-primary next-btn"
                      : "btn bg-gray-light back-btn"
                  }`
            }
            onClick={prevResetStepHandler}
          >
            {currentStep === 4 ? "Create another meeting" : "Back"}
          </button>
          <button
            className={
              currentStep === 4 ? "d-none" : "btn btn-primary next-btn ms-auto"
            }
            onClick={nextStepHandler}
            disabled={meetingSaving && currentStep === 3 ? true : false}
            style={{
              cursor:
                meetingSaving && currentStep === 3 ? "not-allowed" : "pointer",
            }}
          >
            {currentStep === 3 ? "Schedule Meeting" : "Next"}

            <div
              className={
                meetingSaving && currentStep === 3
                  ? "mx-2 spinner-border spinner-border-sm"
                  : "d-none"
              }
              role="status"
            >
              <span className="visually-hidden">Loading...</span>
            </div>
          </button>
        </div>
      </div>
    </section>
  );
};

export default ScheduleMeetingsBody;
