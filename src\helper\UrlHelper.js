//Web server base url
export const API_BASE_URL = "https://dev.api.lobees.com/api";

export const SERVER_URL = "https://dev.api.lobees.com";

export const BACKEND_BASE_URL = "https://admin.dev.lobees.com"; // Backend url

export const FRONTEND_BASE_URL = "https://dev.lobees.com"; // For frontend

//Local Urls
// export const API_BASE_URL = "http://localhost:4006/api"; // NODE SERVER URL FOR API

// export const SERVER_URL = "http://localhost:4006";

// export const BACKEND_BASE_URL = "http://localhost:4600"; // Backend OR ADMIN PANEL url

//export const FRONTEND_BASE_URL = "http://localhost:4204"; // frontend

// Cognifit Launch Url
export const COGNIFIT_LAUNCH_URL = "https://cognifit.lobees.com";
//export const COGNIFIT_LAUNCH_URL = "http://localhost:5173";

/*=========== all Demo Data API URL start here ===========*/
export const DEMO_GET = "/demo/demoget"; // for get demo data

export const DEMO_POST = "/demo/demopost"; // for testing post data
/*=========== all Demo Data API URL end here ===========*/

/*======= login and registration url start =======*/
export const API_LOGIN = "/auth/login"; //login url

export const API_REGISTRATION = "/auth/register"; //registration url

export const API_GET_OTP = "/auth/getotp"; //for getting otp

export const API_VALIDATE_OTP = "/auth/validateotp"; //for validating otp

export const API_UPDATE_PASSSWORD = "/auth/updatepassword"; //for update passowrd

export const API_AUTH_VALIDATE = "/auth/validateuser"; //validation url

/*======= login and registration url end =======*/

/*======= File upload url start =======*/
export const API_SINGLE_FILE_UPLOAD = "/upload/singleupload"; //for single file upload

export const API_MULTIPLE_FILE_UPLOAD = "/upload/multiupload"; //for multiple file upload
/*======= File upload url end =======*/

/*======= URL to upload Image in Text Editor start =======*/
export const URL_TEXT_EDITOR_IMAGE_UPLOAD = "";

export const URL_TEXT_EDITOR_IMAGE_UPLOAD_SECOND = "";
/*======= URL to upload Image in Text Editor end =======*/

//get all ecosystem
export const API_GET_ALL_ECOSYSTEM = "/ecosystem/getecosystems";

//get all module access
export const API_GET_ALL_MODULE_ACCESS = "/moduleroleprevilege/getmoduleaccess";

//API Categories
export const API_GET_CATEGORIES = "/category/getcategories";

/*----- tag/label url start ------*/
//create tag
export const API_CREATE_NEW_TAG = "/tag/createtag";

//get all tag
export const API_GET_ALL_TAGS = "/tag/getalltags";
/*----- tag/label url end ------*/

/*====== lead url start ======*/
//create lead
export const API_SAVE_GUEST_LEAD = "/lead/saveguestlead";

/*====== lead url end ======*/

/*====== blog url start ======*/
//get all blog with category
export const API_GET_ALL_BLOG_WITH_CATEGORY = "/blog/getblogswithcategory";

//get all blog by category
export const API_GET_ALL_BLOG_BY_CATEGORY = "/blog/getallblogsbycategory";

//get blog details
export const API_GET_BLOG_DETAILS = "/blog/getblogdetails";

//get more bloglist
export const API_GET_MORE_BLOGS = "/blog/getmorebloglist";

/*====== blog url end ======*/

/*====== success story url start ======*/
//get all success story
export const API_GET_ALL_SUCCESS_STORIES =
  "/successstories/getallsuccessstories";

//get success story details
export const API_GET_SUCCESS_STORY_DETAILS =
  "/successstories/getsuccessstoriesdetails";

/*====== success story url end ======*/

/*====== event url start ======*/
//get all event type
export const API_GET_ALL_EVENT_TYPES = "/eventtype/getalleventtypes";

//get all event
export const API_GET_ALL_EVENTS = "/event/getallevents";

//get event list for select
export const API_GET_EVENT_LIST_OPTIONS = "/event/geteventlistforoptions";

//get event details
export const API_GET_EVENT_DETAILS = "/event/geteventdetails";

//get all event member
export const API_GET_ALL_EVENT_MEMBER = "/event/getallmembers";

//update event
export const API_UPDATE_EVENT = "/event/updateevent";

//update event member
export const API_JOIN_EVENT_BY_MAIL = "/event/joineventbymail";

//join event
export const API_JOIN_EVENT = "/event/joinevent";

//add/remove tag
export const API_ADD_REMOVE_TAG_EVENT = "/event/addremovetagevent";

//get all event options
export const API_GET_EVENT_PRODUCTS = "/event/geteventproducts";

/*====== event url end ======*/

/*====== chat conversation begins ======*/
//get  GPT AI ------
// -------------------------------------------------------------
export const API_CHAT_GPT_RESPONSE = "/chat/gpt"; // basic gpt

export const API_CHAT_GPT_PROMPTS = "/gptprompt/getprompts"; // gpt prompts for ecosystem

export const API_CHAT_GPT_ASSISTANT = "/chat/gpt/assistant"; // with assistant

export const API_CHAT_GPT_CV_ASSISTANT = "/chat/gpt/cvassistant"; // with CV assistant

export const API_CHAT_GPT_CQ_ASSISTANT = "/chat/gpt/cqassistant"; // with Capital Quest assistant

export const API_CHAT_GPT_ASSISTANT_FILEUPLOAD = "/chat/gpt/fileassistant";

// ------------- GPT AI ends ------------------------------------
//translatedchat
export const API_TRANSLATED_CHAT = "/chat/translatedchat";

//send chat
export const API_SEND_CHAT = "/chat/sendchat";

//get all chat list by user for chat left panel
export const API_GET_ALL_CHAT_LIST = "/chat/getchatlist";

//get all chats
export const API_GET_ALL_CHAT_MESSAGES = "/chat/getallchat";

//update chat
export const API_UPDATE_CHAT = "/chat/updatechat";

//updatetrackers
export const API_UPDATE_TRACKERS = "/chat/updatetrackers";

//deletechat
export const API_DELETE_CHAT = "/chat/deletechat";

//removerequireresponse
export const API_REMOVE_REQUIRE_RESPONSE = "/chat/removerequireresponse";

/*====== chat conversation ends ======*/
/*====== company url start ======*/
//get all company type
export const API_GET_ALL_COMPANY_TYPE = "/companytype/getallcompanytype";

//get all company
export const API_GET_ALL_COMPANY = "/company/getallcompanylist";

//getcompany by input search
export const API_GET_ALL_COMPANY_BY_SEARCH = "/company/getcompanyselectlist";

//get company details
export const API_GET_COMPANY_DETAILS = "/company/getcompanydetails";

//add/update tag company
export const API_ADD_REMOVE_TAG_COMPANY = "/company/addtagtocompany";

//get all member
export const API_GET_ALL_COMPANY_MEMBERS = "/company/getallcompanymemberlist";

/*====== company url end ======*/

/*===== experts/appuser url start ======*/

//get all app users
export const API_GET_APP_USERS = "/account/getallappusers";

//get app user details
export const API_GET_USER_DETAILS = "/account/userdetails";

//get user details by id
export const API_GET_USER_DETAILS_BY_ID = "/account/guestuserdetails";

//add/remove tag to app user
export const API_ADD_REMOVE_TAG_APP_USER = "/account/addtagtouser";

//send connect request
export const API_SEND_REQUEST_USER = "/contact/sendinvite";

//searchcontact
export const API_SEARCH_CONTACT = "/contact/searchcontact";

//updatecontact
export const API_UPDATE_CONTACT = "/contact/updatecontact";

/*===== experts/appuser url end ======*/

/*====== contact url start ======*/
//get all contact
export const API_GET_ALL_CONTACTS = "/contact/getallcontacts";

//get all connect invites
export const API_GET_ALL_RECIEVED_INVITE = "/contact/getallrecievedinvites";

//get all send invite list
export const API_GET_ALL_SEND_INVITE = "/contact/getallsendinvites";

//accept/reject invite
export const API_ACCEPT_INVITE = "/contact/updatecontact";

//cancel/reject request
export const API_REJECT_CONTACT_REQUEST = "/contact/rejectrequest";

//unfriend contact
export const API_UNFRIEND_CONTACT = "/contact/unfriendcontact";

//resume contact
export const API_RESUME_CONTACT = "/contact/resumecontact";

//get contact details
export const API_GET_CONTACT_DETAILS = "/contact/getcontactdetails";

/*====== contact url end ======*/

/*==== group url start =====*/
//create group
export const API_CREATE_GROUP = "/group/creategroup";

//creatgroupbychat
export const API_CREATE_GROUP_BY_CHAT = "/group/creatgroupbychat";

//add new member in group from chat
export const API_ADD_NEW_GROUP_MEMBER = "/group/addnewgroupmember";

//get all group
export const API_GET_ALL_GROUPS = "/group/getallgroups";

//get group by user
export const API_GET_GROUP_LIST_FOR_CHAT = "/group/getgrouplistforchat";

//get group list for select
export const API_GET_GROUP_LIST = "/group/getgrouplist";

//get group details
export const API_GET_GROUP_DETAILS = "/group/getgroupdetails";

//update group
export const API_UPDATE_GROUP = "/group/updategroup";

//add/remove tag to group
export const API_ADD_REMOVE_TAG_GROUP = "/group/addremovetaggroup";

//join group
export const API_JOIN_GROUP = "/group/joingroup";

//join group by mail
export const API_JOIN_GROUP_BY_MAIL = "/group/joingroupbymail";

//get all members
export const API_GET_ALL_GROUP_MEMBER = "/group/getallmembers";

//update group member
export const API_LEAVE_GROUP = "/group/leavegroup";

/*==== group url end =====*/

//search user for add member in event,group and activity
export const API_GET_USERLIST = "/account/getuserlist";

/*=== forum url start ==== */
//create forum
export const API_CREATE_FORUM = "/activityforum/createforum";

//like/dislike post
export const API_TOGGLE_LIKE_FORUM_POST = "/activityforum/togglelikepost";

//add comment
export const API_ADD_COMMENT = "/activityforum/addcomment";

//toggle like comment
export const API_TOGGLE_LIKE_FORUM_COMMENT =
  "/activityforum/togglelikeoncomment";

//get all forum
export const API_GET_ALL_FORUM = "/activityforum/getallforum";

//get details of forum
export const API_GET_FORUM_DETAILS = "/activityforum/forumdetails";

//get comment details
export const API_GET_COMMENT_DETAILS = "/activityforum/getcommentdetails";

//update forum
export const API_UPDATE_FORUM = "/activityforum/updateforum";

//update comment
export const API_UPDATE_COMMENT = "/activityforum/updatecomment";

//add tag forum
export const API_ADD_REMOVE_TAG_FORUM = "/activityforum/addremovetagforum";

/*=== forum url end ==== */

//Cart Checkout Session Url, return session id for payment
export const API_CART_CHECKOUT_SESSION = "/checkout/session";

// Plans
export const API_GET_ALL_PLANS = "/plan/getall"; //get all plans

export const API_GET_PLAN_DETAILS = "/plan/getdetails";

export const API_CREATE_PLAN_SUBSCRIPTION = "/plan/createsubscription"; // create invoice by plan

// invoice
export const API_CREATE_INVOICE_BYPLAN = "/invoice/createbyplan"; // create invoice by plan

//createcartinvoice
export const API_CREATE_INVOICE_BY_CART = "/invoice/createcartinvoice";

export const API_GET_INVOICE_DETAILS = "/invoice/getinvoicedetails";

// Payments
export const API_CREATE_PAYMENT_BY_INVOICE = "/payment/createbyinvoice"; // create invoice by plan
/*=== forum url end ==== */

/* challenge url start */
//create challenge
export const API_ADD_CHALLENGE = "/challenge/createchallenge";

//get all challenge
export const API_GET_ALL_CHALLENGE = "/challenge/getallchallenges";

//get details of challenge
export const API_GET_CHALLENGE_DETAILS = "/challenge/getchallengedetails";

//get all response
export const API_GET_ALL_CHALLENGE_RESPONSE =
  "/challenge/getallchallengeresponses";

//getchallengeresponsedetails
export const API_GET_CHALLENGE_RESPONSE_DETAILS = "/challenge/responsedetails";

// save customer feedback on challenge responses
export const API_ADD_CHALLENGE_RESPONSE_CUSTOMER_FEEDBACK =
  "/challenge/response/customerfeedback";
// get customer feedbacks on challenge responses
export const API_GET_CHALLENGE_RESPONSE_CUSTOMER_FEEDBACK =
  "/challenge/response/getfeedbacks";

//update challenge
export const API_UPDATE_CHALLENGE = "/challenge/updatechallenge";

//add label
export const API_ADD_LABEL_CHALLENGE = "/challenge/addremovetagchallenge";

//add new process
export const API_ADD_NEW_JOB_PROCESS = "/challenge/addnewjobprocess";

//getalljobprocess
export const API_GET_ALL_JOB_PROCESS = "/challenge/getalljobprocess";

//get process details
export const API_GET_JOB_PROCESS_DETAILS = "/challenge/getprocessdetails";

//update process
export const API_UPDATE_JOB_PROCESS = "/challenge/updatejobprocess";

//add new member
export const API_ADD_NEW_JOB_MEMBER = "/challenge/addchallengemember";

//get all member
export const API_GET_ALL_JOB_MEMBER = "/challenge/getallmembers";

//getjobmemberdetails
export const API_GET_JOB_MEMBER_DETAILS = "/challenge/getjobmemberdetails";

//updatechallengemember
export const API_UPDATE_JOB_MEMBER = "/challenge/updatechallengemember";

//addremovetagjobmemember
export const API_ADD_REMOVE_TAG_JOB_MEMBER =
  "/challenge/addremovetagjobmemember";

// get feedback components
export const API_CHALLENGE_FEEDBACK_COMPONENTS =
  "/challenge/feedbackcomponents";

/* challenge url end */

//cvroutes

//create cv
export const API_CREATE_CV = "/cv/createcv";

//getallcv
export const API_GET_ALL_CV = "/cv/getallcv";

//getcvdetails
export const API_GET_CV_DETAILS = "/cv/getcvdetails";

//getcvdetailsbyquery
export const API_GET_CV_DETAILS_BY_QUERY = "/cv/getcvdetailsbyquery";

//updatecv
export const API_UPDATE_CV = "/cv/updatecv";

// save capital quest response
export const API_SAVE_CAPITALQUEST_RESPONSE = "/challenge/capitalquestresponse";

export const API_CAPITALQUEST_DETAILS = "/capitalquest/getcapitalquestdetails";

//api get survey details
export const API_GET_SURVEY_INVITE_DETAILS = "/survey/getsurveyinvitedetails";

export const API_SAVE_RESPONSE_SURVEY = "/survey/addresponsetosurvey";

export const API_GET_SURVEY_RESPONSES =
  "/survey/getallsurveyinvitewithresponses";

//savesurveytoinvite
export const API_SAVE_SURVEY_FEEDBACK = "/survey/savesurveyfeedback";

export const API_CREATE_RECOMENDATION = "/invoice/createrecomandation";

export const API_GROUP_OPPORTUNITIES = "/invoice/getgroupopportunities";

//course url start

//get all course
export const API_GET_ALL_AVAILABLE_COURSES = "/course/getallcourse";

//get course details
export const API_GET_COURSE_DETAILS = "/course/getcoursedetails";

//getcourseinformation
export const API_GET_COURSE_INFORMATION = "/course/getcourseinformation";

//getallactivecoursemembers
export const API_GET_ALL_ACTIVE_COURSE_MEMBERS = "/course/getallcoursemembers";

//addremovetagcourse
export const API_ADD_REMOVE_TAG_COURSE = "/course/addremovetagcourse";

//getcoursedetailswithlessons
export const API_GET_COURSE_DETAILS_WITH_LESSONS =
  "/course/getcoursedetailswithlessons";

//getcoursewithreviews
export const API_GET_COURSE_DETAILS_WITH_REVIEWS =
  "/course/getcoursewithreviews";

//getcourselessondetails
export const API_GET_COURSE_LESSON_DETAILS = "/course/getcourselessondetails";

//join course
export const API_JOIN_COURSE = "/course/joincourse";

//getexistinglessontask
export const API_GET_EXISTING_LESSON_TASK = "/course/getexistinglessontask";

//createlessonassignment
export const API_CREATE_LESSON_ASSIGNMENT = "/course/createlessonassignment";

//submitlessontask
export const API_SUBMIT_LESSON_TASK = "/course/submitlessontask";

//get all logs/answers
export const API_GET_COURSE_TASK_ANSWERS = "/course/getallsubmittedtasklogs";

//getallcourselessonfaqs
export const API_GET_ALL_COURSE_LESSON_FAQS = "/course/getallcourselessonfaqs";

//complete lesson
export const API_COMPLETE_COURSE_LESSON = "/course/completecourselesson";

//getcertificatedetails
export const API_GET_CERTIFICATE_INFORMATION =
  "/course/getcertificateinformation";

//review url
//submit review
export const API_SUBMIT_REVIEW = "/review/givereview";

//get all reviews
export const API_GET_ALL_REVIEWS = "/review/getallreviews";

/*====== skillpoint url start ======*/
//add new skillpoint
export const API_CREATE_NEW_SKILLPOINT = "/skillpoint/addskillpoint";

//get skillpoint details
export const API_GET_SKILLPOINT_DETAILS = "/skillpoint/getskillpointdetails";

//update skillpoint
export const API_UPDATE_SKILLPOINT = "/skillpoint/updateskillpoint";

//delete skillpoint
export const API_DELETE_SKILLPOINT = "/skillpoint/deleteskillpoint";

/*====== skillpoint url end ======*/

/*===== task url ======*/

//gettasklistforchat
export const API_GET_TASK_LIST_FOR_CHAT = "/project/gettasklistforchat";

//get task details
export const API_GET_TASK_DETAILS = "/project/gettaskdetails";

//note url start
//add note
export const API_ADD_NEW_NOTE = "/note/createnote";

//get all note
export const API_GET_ALL_NOTE = "/note/getallnotes";

//get note details
export const API_GET_NOTE_DETAILS = "/note/getnotedetails";

//update note
export const API_UPDATE_NOTE = "/note/updatenote";

//delete note
export const API_DELETE_NOTE = "/note/deletenote";
//note url end

//alert url start
//add alert
export const API_ADD_NEW_ALERT = "/alert/createalert";

//get all alert
export const API_GET_ALL_TODAYS_ALERT = "/alert/gettodaysalerts";

//get all other days alert
export const API_GET_ALL_OTHERDAYS_ALERT = "/alert/getotherdaysalerts";

//get alert details
export const API_GET_ALERT_DETAILS = "/alert/getalert";

//update alert
export const API_UPDATE_ALERT = "/alert/updatealert";

//delete alert
export const API_DELETE_ALERT = "/alert/deletealert";

//alert url end

// idea Url for add, list, details and update
export const API_ADD_IDEA = "/idea/createidea";

export const API_LIST_IDEA = "/idea/getideas";

export const API_DETAILS_IDEA = "/idea/getideadetails";

export const API_UPDATE_IDEA = "/idea/updateidea";

export const API_DELETE_IDEA = "/idea/deleteidea";

// invoice
export const API_CREATE_INVOICE = "/invoice/createinvoice";

export const API_UPDATE_INVOICE = "/invoice/updateinvoice";
/*------ product url start ------*/
//get all product type
export const API_GET_ALL_PRODUCT_TYPE = "/product/getallproducttype";

// get all products
export const API_GET_ALL_PRODUCTS = "/product/getallproducts";

//getallproductslist
export const API_GET_ALL_PRODUCTS_LIST = "/product/getproductlists";

//getallproductpricings
export const API_GET_ALL_PRODUCT_PRICINGS = "/product/getallproductpricings";

//get product details
export const API_GET_PRODUCT_DETAILS = "/product/getproductdetails";

//getproductpricingdetails
export const API_GET_PRODUCT_PRICING_DETAILS =
  "/product/getproductpricingdetails";

//update product
export const API_UPDATE_PRODUCT = "/product/updateproduct";

//updateproductpricing
export const API_UPDATE_PRODUCT_PRICING = "/product/updateproductpricing";

//deletebulkproduct
export const API_DELETE_BULK_PRODUCT = "/product/deletebulkproduct";

//addremovetagproduct
export const API_ADD_REMOVE_TAG_PRODUCT = "/product/addremovetagproduct";

/*------ product url end ------*/

/*--------- diagnosis start ---------- */
export const API_GET_MEDICINES_BY_NAME = "/diagnosis/getmedicinedetails";

export const API_GET_DISEASE_BY_NAME = "/diagnosis/getdiseasedetails";

/*--------- diagnosis end ---------- */
/*------ address url start ------*/
export const API_GET_ALL_ADDRESS = "/address/getall";
/*------ address url end ------*/

/* --------------------------------- FAQ APIs --------------------------------- */
export const API_FAQ_LIST = "/faq/getallfaqs"; // url for get list of faq

export const API_FAQ_DETAILS = "/faq/detailsfaq"; // url for get faq details

export const API_CREATE_FAQ = "/faq/createfaq"; // url for save question

export const API_UPDATE_FAQ = "/faq/updatefaq"; // url for save answer

export const API_DELETE_FAQ = "/faq/deletefaq"; // url for delete faq

// search faq by index
export const API_SEARCH_FAQ_BY_INDEX = "/faq/searchfaqbyindex";

/* --------------------------------- AI ROUTE --------------------------------- */
export const API_AI_CONVERSATION_TASK = "/ai/conversation/task"; // url for getting task and summary from conversation

export const API_AI_FOLLOWUP_MEDICAL_QUESTIONS =
  "/ai/conversation/medicalfollowup"; // url for getting follow-up questions from conversation

export const API_AI_GENERATE_PRESCRIPTION =
  "/ai/conversation/medicalprescription"; // url for generating prescription from conversation

/*===== mail template url start ======*/
//get all template
export const API_GET_ALL_MAIL_TEMPLATES = "/messagetemplate/getall";

/*===== mail template url end ======*/

/*==== mail conversation url start ====*/
//create
export const API_ADD_MESSAGE = "/message/savemail";

// create task from chat
export const API_CREATE_TASKS_FROM_CHAT = "/project/createtaskfromai";

// get project list by user
export const API_GET_PROJECTLIST_BY_SEARCH = "/project/getprojectlsitbysearch";

/*==== mail conversation url end ====*/

//agenda url
//get all agenda
export const API_GET_ALL_AGENDA = "/agenda/getallagenda";

//get details
export const API_GET_DETAILS_AGENDA = "/agenda/getagendadetails";

//gettimelistagenda
export const API_GET_USER_TIMES_BY_DATE = "/agenda/gettimelistagenda";

//save meeting schedule
export const API_SAVE_SCHEDULE_MEETING = "/agenda/sendrequestmeeting";

//sendrequestmeetingagendabyguest
export const API_SAVE_SCHEDULE_MEETING_BY_GUEST =
  "/agenda/sendrequestmeetingagendabyguest";

//getmymeetings
export const API_GET_ALL_MY_MEETINGS = "/agenda/getmymeetings";

//meetingaction
export const API_ACCPET_REJECT_MEETING = "/agenda/meetingaction";

// request payment
export const API_REQUEST_PAYMENT = "/invoice/requestpaymentinvoice";

// request quotation invoice
export const API_REQUEST_QUOTATION_INVOICE = "/invoice/requestquotationinvoice";

// save quotation
export const API_SAVE_QUOTATION = "/quotation/createquotation";

// save quotation
export const API_UPDATE_QUOTATION = "/quotation/updatequotation";

// get all quotation
export const API_GET_ALL_QUOTATIONS = "/quotation/getquotations";

//getagendadetails
export const API_GET_AGENDA_DETAILS = "/agenda/getagendadetails";

//feedback rule
export const API_GET_SURVEY_BY_FEEDBACK_RULE =
  "/feedbackrules/getsurveybyfeedbackrule";

// get cognifit user token
export const API_COGNIFIT_CONNECT = "/cognifit/connect";

// get cognifit access token
export const API_COGNIFIT_ACCESS_TOKEN = "/cognifit/accesstoken";

// get cognifit skills
export const API_COGNIFIT_SKILLS = "/cognifit/skills";

// get cognifit assessment list
export const API_COGNIFIT_ACTIVITY_LIST = "/cognifit/activities/list";

// get cognifit activity details as report
export const API_COGNIFIT_ASSESSMENT_REPORT = "/cognifit/assessment/report";
