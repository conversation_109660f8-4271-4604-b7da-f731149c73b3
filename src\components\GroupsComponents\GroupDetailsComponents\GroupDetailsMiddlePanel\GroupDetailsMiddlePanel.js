/* eslint-disable */
import { useState } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

import AboutTab from "./Tabs/AboutTab";
import MemberTab from "./Tabs/MemberTab";
// import ForumTab from "./Tabs/ForumTab";
// import EventsTab from "./Tabs/EventTab";
import GalleryTab from "./Tabs/GalleryTab";
import ReviewsTab from "./Tabs/ReviewsTab";
import OpportunityTab from "./Tabs/OpportunityTab";

const GroupDetailsMiddlePanel = ({
  isApproved = false,
  isModerator = false,
  isLoading = false,
  isJoined = false,
  refreshGroup = false,
  setRefreshGroup = () => {},
}) => {
  const { t } = useTranslation(); // translation marker

  // Define all possible tabs
  const groupDetailsTabs = [
    { id: "about", label: t("About"), component: AboutTab },
    { id: "members", label: t("Members"), component: MemberTab },
    { id: "gallery", label: t("Gallery"), component: GalleryTab },
    {
      id: "opportunities",
      label: t("Opportunities"),
      component: OpportunityTab,
    },
    { id: "review", label: t("Review"), component: ReviewsTab },
  ];

  // Active tab state with 'about' as default
  const [activeTab, setActiveTab] = useState("about");

  /**
   * Handle tab selection
   * @param {string} tabId - ID of the selected tab
   */
  const tabSelectionHandler = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="pnl_mdlpnl" style={{ width: "70%" }}>
      <div className="people_item">
        <div className="tab_links people_tablinks">
          {isLoading ? (
            <ul className="nav nav-tabs list_stye_none">
              {Array.from({ length: 4 }).map((_, index) => (
                <li className="nav-item" key={index}>
                  <div
                    className="nav-link placeholder"
                    style={{
                      width: "100px",
                      height: "38px",
                    }}
                  ></div>
                </li>
              ))}
            </ul>
          ) : (
            <ul className="nav nav-tabs list_stye_none">
              {groupDetailsTabs.map((tab) => (
                <li className="nav-item" key={tab.id}>
                  <Link
                    className={`nav-link ${
                      activeTab === tab.id ? "active show" : ""
                    }`}
                    to="#"
                    onClick={() => {
                      tabSelectionHandler(tab.id);
                    }}
                  >
                    {tab.label}
                  </Link>
                </li>
              ))}
            </ul>
          )}
        </div>

        <div className={isLoading ? "d-none" : "tab-content"}>
          {groupDetailsTabs.map((tab) => {
            const TabComponent = tab.component;
            return (
              <div
                key={tab.id}
                className={`tab-pane fade ${
                  activeTab === tab.id ? "show active" : ""
                }`}
              >
                <TabComponent
                  tabName={activeTab === tab.id ? tab.id : ""}
                  isApproved={isApproved}
                  isModerator={isModerator}
                  isJoined={isJoined}
                  refreshGroup={refreshGroup}
                  setRefreshGroup={setRefreshGroup}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default GroupDetailsMiddlePanel;
