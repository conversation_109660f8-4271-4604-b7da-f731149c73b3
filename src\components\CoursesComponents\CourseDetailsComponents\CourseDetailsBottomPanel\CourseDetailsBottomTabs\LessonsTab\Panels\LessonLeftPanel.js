import React from "react";
import { Link } from "react-router-dom";

const LessonLeftPanel = ({
  userCourseRole = "",
  lessonList = [],
  selectedLessonId,
  setSelectedLessonId,
}) => {
  return (
    <div className="lessons_leftpnl">
      <div className="lesson_rating_outer">
        {lessonList.length > 0
          ? lessonList.map((lessonData, index) => {
              return (
                <Link
                  to="#"
                  className={`lesson_rating_row ${
                    selectedLessonId &&
                    selectedLessonId.toString() === lessonData._id.toString()
                      ? "active_lessons"
                      : ""
                  }`}
                  key={index}
                  onClick={() => {
                    document.body.classList.add("course_open");
                    setSelectedLessonId(lessonData._id);
                  }}
                >
                  <div className="lesson_statusOuter">
                    <div className="lesson_time">
                      <ul className="list_stye_none">
                        <li>{lessonData?.duration} min</li>
                        <li>{lessonData?.membernames}</li>
                      </ul>
                    </div>
                    {userCourseRole !== "" ? null : (
                      <div
                        className={`lesson_status ${
                          lessonData?.statusstring === "0"
                            ? "pending_status"
                            : "success_status"
                        } `}
                      >
                        <i className="material-icons-outlined">
                          {lessonData?.statusstring === "0"
                            ? "info"
                            : "check_circle"}
                        </i>{" "}
                        <span>{lessonData?.completestatus}</span>
                      </div>
                    )}
                  </div>
                  <div className="lesson_hdng">
                    <h3>{lessonData?.title}</h3>
                  </div>
                  <div className="lesson_time rating_status">
                    <ul className="list_stye_none">
                      <li>{lessonData?.chaptername}</li>
                    </ul>
                  </div>
                </Link>
              );
            })
          : null}
      </div>
    </div>
  );
};

export default LessonLeftPanel;
