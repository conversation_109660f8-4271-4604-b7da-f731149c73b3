
/*******************Reset Css****************/

*{margin:0;padding:0;border:none;outline:none !important;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;-ms-box-sizing:border-box;-o-box-sizing:border-box;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;box-shadow:none;-webkit-box-shadow:none;-ms-box-shadow:none;-o-box-shadow:none}

html,body{padding:0;margin:0;border-radius:0;overflow-x:hidden;font-family:var(--bs-body-font-family);font-size:14px;font-weight:400;color:var(--bs-color-00)}
em {font-style: normal;}

a,button,input,input[type=buttton],input[type=submit],.btn{transition:all .1s ease-in-out;-webkit-transition:all .1s ease-in-out;-moz-transition:all .1s ease-in-out;-ms-transition:all .1s ease-in-out;-o-transition:all .1s ease-in-out;outline:none !important;text-decoration:none;border:none;font-style:normal;text-decoration:none}
input {width: 100%;}
img,figure {max-width: 100%;max-height: 100%;margin: 0;padding: 0;}

ul,ol,li{margin:0;padding: 0;list-style: none;}.list_stye_none{list-style:none;padding:0}
a,a:hover,a:focus{text-decoration:none}
h1,h2,h3,h4,h5,h6,p{font-weight:400;margin:0}

input,textarea{padding:5px 10px;transition:all .1s ease;-webkit-transition:all .1s ease;-moz-transition:all .1s ease;-ms-transition:all .1s ease;-o-transition:all .1s ease}input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{opacity:1;color:var(--bs-placeholder-color)}input:-moz-placeholder,textarea:-moz-placeholder{opacity:1;color:var(--bs-placeholder-color)}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color: var(--bs-placeholder-color)}input:-ms-input-placeholder,textarea:-ms-input-placeholder{opacity:1;color:var(--bs-placeholder-color)}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.noradius{border-radius:0;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;-o-border-radius:0}.pointerNone{pointer-events:none;-webkit-pointer-events:none;-moz-pointer-events:none}.relative{position:relative}.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn){width:100%}.row{margin:0 -10px}.modal{padding:0 !important}
.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto{padding: 0 10px;}

  /* ::-webkit-scrollbar-track {border: 1px solid transparent;padding: 2px 0;background-color: transparent;opacity: 0;}
  ::-webkit-scrollbar{opacity: 0;}
  ::-webkit-scrollbar-thumb{opacity: 0;} */
  input[type=file]::-webkit-file-upload-button {display: none;}
::-webkit-calendar-picker-indicator {
    cursor: pointer;
}
h1,h2{font-family: 'Tilt Warp', cursive;}

/*******************Reset Css****************/