/* eslint-disable */
import { useMemo, useState, useEffect } from "react";
import { useParams } from "react-router-dom";

// Material UI table
import { MaterialReactTable } from "material-react-table";

/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import { useTranslation } from "react-i18next";
import { formatNumber } from "helper/Common/CommonHelper";

const OpportunityTab = ({ tabName = "" }) => {
  const { t } = useTranslation(); // for translations

  const params = useParams();
  const token = localStorage.getItem("token");

  const [isLoading, setIsLoading] = useState(false);
  const [totalGroupUser, settotalGroupUser] = useState("");
  const [totalGroupOpportunitySent, settotalGroupOpportunitySent] =
    useState("");
  const [groupTotalAmountOfSent, setgroupTotalAmountOfSent] = useState("");
  const [totalGroupopportunityRecived, settotalGroupopportunityRecived] =
    useState("");
  const [groupTotalAmountOfRecived, setgroupTotalAmountOfRecived] =
    useState("");
  const [groupOpportunityList, setgroupOpportunityList] = useState([]);

  //get all group opportunties
  const getAllGroupOpportunities = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_GROUP_OPPORTUNITIES +
        `?token=${token}&group=${params.id}`;

      const response = await getData(requestURL);

      setIsLoading(false);

      console.log(response);

      if (response.status) {
        settotalGroupUser(response.data.totalgroupuser);
        settotalGroupOpportunitySent(response.data.totalgroupopportunitysent);
        setgroupTotalAmountOfSent(response.data.grouptotalamountofsent);
        settotalGroupopportunityRecived(
          response.data.totalgroupopportunityrecived
        );
        setgroupTotalAmountOfRecived(response.data.grouptotalamountofrecived);
        setgroupOpportunityList(response.data.opportunitiesofgroup);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const columns = useMemo(
    () => [
      {
        accessorKey: "membername",
        header: t("User"),
        size: 200,
      },
      {
        accessorKey: "opportunitysent",
        header: t("Recommended to"),
      },
      {
        accessorKey: "totalamountofsent",
        header: t("Amount"),
        Cell: ({ row }) => (
          <p>{formatNumber(row.original.totalamountofsent)} €</p>
        ),
      },
      {
        accessorKey: "totallostsentcount",
        header: t("Lost"),
      },
      {
        accessorKey: "totalownsentcount",
        header: t("Own"),
      },
      {
        accessorKey: "totalpendingsentcount",
        header: t("Pending"),
      },
      {
        accessorKey: "opportunityrecived",
        header: t("Recommended by"),
      },
      {
        accessorKey: "totalamountofrecived",
        header: t("Amount"),
        Cell: ({ row }) => (
          <p>{formatNumber(row.original.totalamountofrecived)} €</p>
        ),
      },
      {
        accessorKey: "totallostrecivedcount",
        header: t("Lost"),
      },
      {
        accessorKey: "totalownrecivedcount",
        header: t("Own"),
      },
      {
        accessorKey: "totalpendingrecivedcount",
        header: t("Pending"),
      },
    ],
    []
  );

  useEffect(() => {
    if (params.id && tabName === "opportunities") {
      getAllGroupOpportunities();
    }
  }, [params.id, tabName]);

  if (isLoading) {
    return (
      <>
        {" "}
        <div className="row gap-2 my-4">
          <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
            <div className="placeholder-glow">
              <h6 className="fw-semibold placeholder col-6"></h6>
              <p className="placeholder col-4"></p>
            </div>
          </div>
          <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
            <div className="placeholder-glow">
              <h6 className="fw-semibold placeholder col-7"></h6>
              <p className="placeholder col-5"></p>
            </div>
          </div>
          <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
            <div className="placeholder-glow">
              <h6 className="fw-semibold placeholder col-6"></h6>
              <p className="placeholder col-4"></p>
            </div>
          </div>
        </div>
        <div className="opportunities_table">
          <div className="placeholder-glow">
            {/* Table header placeholder */}
            <div className="d-flex justify-content-between align-items-center mb-3">
              <div className="placeholder col-3"></div>
              <div className="placeholder col-2"></div>
            </div>

            {/* Table content placeholder */}
            <div className="border rounded">
              {/* Header row */}
              <div className="d-flex p-3 border-bottom bg-light">
                <div className="placeholder col-1 me-3"></div>
                <div className="placeholder col-2 me-3"></div>
                <div className="placeholder col-2 me-3"></div>
                <div className="placeholder col-2 me-3"></div>
                <div className="placeholder col-2 me-3"></div>
                <div className="placeholder col-1"></div>
              </div>

              {/* Data rows */}
              {[...Array(5)].map((_, index) => (
                <div key={index} className="d-flex p-3 border-bottom">
                  <div className="placeholder col-1 me-3"></div>
                  <div className="placeholder col-2 me-3"></div>
                  <div className="placeholder col-2 me-3"></div>
                  <div className="placeholder col-2 me-3"></div>
                  <div className="placeholder col-2 me-3"></div>
                  <div className="placeholder col-1"></div>
                </div>
              ))}
            </div>

            {/* Pagination placeholder */}
            <div className="d-flex justify-content-between align-items-center mt-3">
              <div className="placeholder col-2"></div>
              <div className="d-flex gap-2">
                <div
                  className="placeholder"
                  style={{ width: "40px", height: "40px" }}
                ></div>
                <div
                  className="placeholder"
                  style={{ width: "40px", height: "40px" }}
                ></div>
                <div
                  className="placeholder"
                  style={{ width: "40px", height: "40px" }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="row gap-2 my-4">
        <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
          <h6 className="fw-semibold">Total User</h6>
          <p>{totalGroupUser}</p>
        </div>
        <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
          <h6 className="fw-semibold">Total Recomended</h6>
          <p>{totalGroupOpportunitySent}</p>
        </div>
        <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
          <h6 className="fw-semibold">Total Amount</h6>
          <p>{formatNumber(groupTotalAmountOfSent)} €</p>
        </div>
        {/* <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
                <h6 className="fw-semibold">Recomended By</h6>
                <p>{totalGroupopportunityRecived}</p>
              </div>
              <div className="col p-3 p-md-4 border border-gray-300 rounded-10 shadow-sm">
                <h6 className="fw-semibold">Recomended Amount By</h6>
                <p>{groupTotalAmountOfRecived}</p>
              </div> */}
      </div>

      <div className="opportunities_table">
        <MaterialReactTable
          columns={columns} // map columns to be displayed with api data,
          data={groupOpportunityList} // data from api to be displayed
          enableGrouping
          // enableRowSelection // enable showing checkbox
          enableColumnOrdering={true}
          getRowId={(row) => row._id} // map which value to select with row checkbox
          defaultColumn={{
            minSize: 20,
            maxSize: 300,
            size: 50, //make columns wider by default
          }}
        />
      </div>
    </>
  );
};

export default OpportunityTab;
