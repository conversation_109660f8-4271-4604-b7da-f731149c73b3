//function for get 7 upcoming dates of given date
export const getUpComingDates = (dateValue) => {
  const upcomingDates = [];

  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(dateValue);
    currentDate.setDate(currentDate.getDate() + i);

    const day = currentDate.getDate().toString();
    const month = currentDate
      .toLocaleString("default", { month: "short" })
      .toUpperCase();
    const year = currentDate.getFullYear().toString();
    const fulldate = currentDate.toISOString().slice(0, 10);

    const dateInfo = { day, month, year, fulldate };
    upcomingDates.push(dateInfo);
  }

  return upcomingDates;
};

//convert calendar date into string with full text
export const getFullTextDateString = (dateValue) => {
  const dateObj = new Date(dateValue);

  // Define arrays for day names and month names
  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const monthsOfYear = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Extract day of the week, month, and day from the date object
  const dayOfWeek = daysOfWeek[dateObj.getDay()];
  const month = monthsOfYear[dateObj.getMonth()];
  const day = dateObj.getDate();

  // Construct the formatted date string
  const formattedDate = `${dayOfWeek},  ${month} ${day}`;

  return formattedDate; // Output: "Thursday,February 01"
};

//convert calander date to string
export const getCalenderDateInString = (dateValue) => {
  const dateObj = new Date(dateValue);

  // Extract year, month, and day from the date object
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Month is zero-based
  const day = String(dateObj.getDate()).padStart(2, "0");

  // Construct the formatted date string
  const formattedDate = `${year}-${month}-${day}`;

  return formattedDate;
};

//convert date into string text
export const getConvertedDateText = (dateString) => {
  const dateObj = new Date(dateString);

  // Define arrays for day names and month names
  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const monthsOfYear = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // Extract day of the week, month, and day from the date object
  const dayOfWeek = daysOfWeek[dateObj.getDay()];
  const month = monthsOfYear[dateObj.getMonth()];
  const day = dateObj.getDate();
  const year = dateObj.getFullYear();

  // Construct the formatted date string
  const formattedDate = `${dayOfWeek}, ${month} ${day}, ${year}`;

  return formattedDate;
};

//get net 60 min slot
export const getNextHourSlot = (timeString) => {
  const [hours, minutes] = timeString.split(":").map(Number);

  // Add 60 minutes
  const newMinutes = (minutes + 60) % 60;
  const newHours = hours + Math.floor((minutes + 60) / 60);

  // Format the resulting time
  const formattedTime = `${String(newHours).padStart(2, "0")}:${String(
    newMinutes
  ).padStart(2, "0")}`;

  return formattedTime; // Output: "14:00"
};

//get current date in string
export const getCurrentDateInString = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");

  const formattedDate = `${year}-${month}-${day}`;

  return formattedDate;
};

//get formated date in dd/mm/yyyy
export const getFormatedDateInString = (dateString = "") => {
  if (dateString !== "") {
    const today = new Date(dateString);
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");

    const formattedDate = `${day}/${month}/${year}`;

    return formattedDate;
  } else {
    return dateString;
  }
};
