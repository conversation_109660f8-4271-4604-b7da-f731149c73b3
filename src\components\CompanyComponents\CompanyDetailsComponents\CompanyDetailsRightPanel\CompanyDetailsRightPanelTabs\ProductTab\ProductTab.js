/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";

/*------ Helper imports ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

const ProductTab = ({ tabName = "" }) => {
  const { id: companyId } = useParams(); // Renamed for clarity
  const token = localStorage.getItem("token");

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [productList, setProductList] = useState([]);

  //function for get all products
  const getAllProducts = async () => {
    setIsLoading(true);
    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_ALL_PRODUCTS +
        `?token=${token}&companies=${companyId}`;

      console.log("requestURL --------> ", requestURL);

      const response = await getData(requestURL);

      setIsLoading(false);

      if (response.status) {
        setProductList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (companyId && tabName === "products") {
      getAllProducts();
    }
  }, [companyId, tabName]);

  if (isLoading) {
    return (
      <div className="product_innr">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            className="product_row white_shadow_bx placeholder-glow"
            key={index}
          >
            <div className="product_pic">
              <span
                className="placeholder col-12"
                style={{ height: "150px", display: "block" }}
              ></span>
            </div>
            <div className="product_info">
              <h3 className="placeholder col-6"></h3>

              <div className="event_tags">
                <ul className="list_stye_none">
                  <li>
                    <span className="placeholder col-4"></span>
                  </li>
                </ul>
              </div>

              <div className="product_list">
                <ul className="list_stye_none">
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                </ul>
              </div>

              <div className="product_price">
                <h3 className="placeholder col-4"></h3>
              </div>

              <div className="d-grid gap-2 mt-3">
                <span className="placeholder col-6 btn btn-sm"></span>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="product_innr">
      {productList.map((product, index) => {
        return (
          <div className="product_row white_shadow_bx" key={index}>
            <div className="product_pic">
              <img src={url.SERVER_URL + product.productimagepath} alt="" />
            </div>
            <div className="product_info">
              <h3>{product.name}</h3>

              <div className="event_tags">
                <ul className="list_stye_none">
                  <li>
                    <i className="material-icons-outlined ornge_icon">star</i>
                    <span className="rating">
                      {product.totalratings} <em>({product.totalreviews})</em>
                    </span>
                  </li>
                </ul>
              </div>
              <div className="product_list">
                <ul className="list_stye_none">
                  <li>
                    <span>By</span>
                    <b>{product.companyname}</b>
                  </li>
                </ul>
              </div>
              <div className="product_price">
                <h3>€ {product.price}</h3>
              </div>
              <div className="d-grid gap-2 mt-3">
                <Link
                  to={`/marketplace/details/${product._id}`}
                  className="btn btn-sm outline"
                >
                  View Details
                </Link>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ProductTab;
