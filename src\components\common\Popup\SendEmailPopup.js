/* eslint-disable */
import React, { useState, useEffect, useContext, useRef, useMemo } from "react";
import { Link, useParams } from "react-router-dom";
// import { Editor } from "@tinymce/tinymce-react";
import JoditEditor from "jodit-react";
import Select from "react-select";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

//import context
import { GlobalProvider } from "context/GlobalContext";

/*import url and gateway methods */
import { getData, postData, uploadMultipleFile } from "utils/Gateway";
import * as url from "helper/UrlHelper";

// import mail variables from json file
import mailVariables from "Data/MailVariables.json";

// clipboardCopy for mail variables
import clipboardCopy from "clipboard-copy";

const SendEmailPopup = ({ emails = "", setEmails = () => {} }) => {
  const { t } = useTranslation();
  const token = localStorage.getItem("token"); // token

  // loggedin user information
  const currentUser = JSON.parse(localStorage.getItem("userInfo"));

  // ------------ hooks for user Inputs --------------------------------
  const [mailTemplateList, setMailTemplateList] = useState([]);

  /* ------------- Language translation imports starts here ------------- */
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [receivermails, setReceivermails] = useState("");
  const [mailSubject, setMailSubject] = useState("");
  const [mailDescription, setMailDescription] = useState(""); //for ck editor
  const [isUploading, setIsUploading] = useState(false);
  const [fileUploadIds, setFileUploadIds] = useState([]);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [mailSending, setMailSending] = useState(false);
  const [linkTrackerBlock, setlinkTrackerBlock] = useState([]);

  //link tracker block
  const linkTrackerTemplate = {
    link: "",
    label: "",
  };

  /* hook for validation */
  const [validation, setValidation] = useState({
    emailWarning: false,
    subjectWarning: false,
  });

  const [warningMessage, setWarningMessage] = useState("");

  const editor = useRef(null);

  // const editorCustomConfig = joditEditorConfig();
  const editorCustomConfig = {
    readonly: false,
    placeholder: "",
    minHeight: 300,
    uploader: {
      insertImageAsBase64URI: false,
    },
    events: {
      afterPaste: async (event) => {
        // take the saved clipboard data
        const clipboardData = event.clipboardData || window.clipboardData;
        const items = clipboardData.items;

        for (let i = 0; i < items.length; i++) {
          if (items[i].type.indexOf("image") !== -1) {
            const file = items[i].getAsFile();

            // Rename the file using a timestamp
            const timestamp = Date.now();
            const fileExtension = file.type.split("/")[1]; // Get the file extension (e.g., png, jpeg)
            const newFileName = `image-${timestamp}.${fileExtension}`;

            const renamedFile = new File([file], newFileName, {
              type: file.type,
            });

            try {
              // upload the file data in server
              const requestUrl =
                url.API_BASE_URL +
                url.API_SINGLE_FILE_UPLOAD +
                `?token=${token}`;

              const response = await uploadSingleFile(requestUrl, renamedFile);

              if (response.status) {
                const imageUrl = url.SERVER_URL + response.data.path;
                // console.log("imageUrl", imageUrl);

                // Replace blob URL with the server URL
                const editorInstance = editor.current;

                if (editorInstance) {
                  const content = editorInstance.value;
                  const regex = /<img[^>]+src="data:image\/[^">]+"/g;
                  const newContent = content.replace(regex, (match) => {
                    const base64DataMatch = match.match(/src="([^"]+)"/);
                    if (
                      base64DataMatch &&
                      base64DataMatch[1].startsWith("data:image/")
                    ) {
                      return match.replace(base64DataMatch[1], imageUrl);
                    }
                    return match;
                  });

                  // add in editor content
                  editorInstance.value = newContent;

                  // add in state content
                  setMailDescription(newContent);
                }
              }
            } catch (error) {
              console.error("Image upload failed:", error);
            }
          }
        }
      },
    },
    defaultActionOnPaste: "insert_as_html",
    defaultLineHeight: 1.5,
    enter: "div",
    statusbar: false,
    sizeLG: 900,
    sizeMD: 700,
    sizeSM: 400,
    toolbarAdaptive: false,
    disablePlugins: [
      "image",
      "video",
      "about",
      "classSpan",
      "file",
      "speechRecognize",
      "copyformat",
      "spellcheck",
    ],
    removeButtons: ["link"],
  };

  const editorConfig = useMemo(() => editorCustomConfig, []);

  //function for get all mail templates
  const getAllMailTemplates = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_MAIL_TEMPLATES +
        `?token=${token}&isselect=true`;

      const response = await getData(requestUrl);

      // console.log("response in mail template list------->", response);

      if (response.status) {
        setMailTemplateList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //for adding a new link tracker block
  const onAddLinkTrackerBlock = () => {
    //console.log('linkTrackerBlock >> ', linkTrackerBlock)

    setlinkTrackerBlock([...linkTrackerBlock, linkTrackerTemplate]);
  };

  //** Function for delete link tracker block */
  const onDeleteLinkTrackerBlock = (index) => {
    const filteredLinkTrackerBlock = [...linkTrackerBlock];
    filteredLinkTrackerBlock.splice(index, 1);
    setlinkTrackerBlock(filteredLinkTrackerBlock);
  };

  //** Function for change link tracker value */
  const changeLinkTrackerValue = (index, field, value) => {
    const updatedFormValues = linkTrackerBlock.map((block, i) =>
      index == i ? Object.assign(block, { [field]: value }) : block
    );

    //console.log('updatedFormValues', updatedFormValues);
    setlinkTrackerBlock(updatedFormValues);
  };

  //** Function for mapping form repeater data */
  const updatedLinkTrackerBlock = (linkTracker = []) => {
    const linkTrackerArr = linkTracker.map((linkTrackerData) => {
      let linkTrackerSavedData = {
        link: linkTrackerData?.link,
        label: linkTrackerData?.label,
      };

      return linkTrackerSavedData;
    });
    setlinkTrackerBlock(linkTrackerArr);
  };

  //function for file upload
  const uploadHandler = async (e) => {
    try {
      setIsUploading(true);
      let requestUrl =
        url.API_BASE_URL + url.API_MULTIPLE_FILE_UPLOAD + `?token=${token}`;
      const response = await uploadMultipleFile(requestUrl, e.target.files);
      if (response.status) {
        /*----- assign uploaded files with path for display start ------*/
        const uploadedFileValues = response.data.map(
          ({ name, path, _id, ...other }) => ({
            name,
            path: url.SERVER_URL + path,
            _id,
          })
        );
        //check old file exist or not if exist then new file will concat with old file
        if (uploadedFiles.length == 0) {
          setUploadedFiles(uploadedFileValues);
        } else {
          let fileArr = uploadedFiles.concat(uploadedFileValues);
          setUploadedFiles(fileArr);
        }
        /*----- assign uploaded files with path for display end ------*/
        setIsUploading(false);
        /*----- assign uploaded file ids start ------*/
        const fileIds = response.data.map(({ _id, ...other }) => ({ _id }));
        //check old file ids are exist or not.If exist then new id will concat with old id
        if (fileUploadIds.length == 0) {
          setFileUploadIds(fileIds);
        } else {
          let fileArr = fileUploadIds.concat(fileIds);
          setFileUploadIds(fileArr);
        }
        /*----- assign uploaded file ids end ------*/
        resetFile();
      }
    } catch (error) {
      console.error(error.message);
    }
  };

  //function for delete file from upload stack
  const closeFileHandler = (index) => {
    const closedFile = [...uploadedFiles];
    const removedFileId = [...fileUploadIds];
    closedFile.splice(index, 1);
    removedFileId.splice(index, 1);
    setUploadedFiles(closedFile);
    setFileUploadIds(removedFileId);
    resetFile();
  };

  // function for clear file value
  const resetFile = () => {
    const file = document.getElementById("uploadedMailUploadFile");
    if (file) {
      file.value = null;
    } else {
      return;
    }
  };

  //function for select template
  const templateSelectionHandler = (val) => {
    //assign template value for select
    setSelectedTemplate(val);

    //set value for send mail
    setMailSubject(val.subject);

    // setEditorContent(val.description);
    setMailDescription(val.description);

    //assing link text

    if (val.linktrackerids && val.linktrackerids.length > 0) {
      updatedLinkTrackerBlock(val.linktrackerids);
    }

    //assign uploaded files from mail template
    const uploadedFileValues = val.uploads.map(({ name, path, _id }) => ({
      name,
      path: url.SERVER_URL + path,
      _id,
    }));
    if (uploadedFiles.length == 0) {
      setUploadedFiles(uploadedFileValues);
    } else {
      let fileArr = uploadedFiles.concat(uploadedFileValues);
      setUploadedFiles(fileArr);
    }
    //assign uploaded file id for sending mail
    const fileIds = val.uploads.map(({ _id, ...other }) => ({ _id }));
    if (fileUploadIds.length == 0) {
      setFileUploadIds(fileIds);
    } else {
      let fileArr = fileUploadIds.concat(fileIds);
      setFileUploadIds(fileArr);
    }
    setValidation((prevState) => ({
      ...prevState,
      subjectWarning: false,
    }));
  };

  // validate =========================
  const validate = () => {
    let isValid = true;
    if (receivermails == "") {
      if (emails == "") {
        setValidation((prevState) => ({ ...prevState, emailWarning: true }));
        isValid = false;
      }
    }
    if (mailSubject === "") {
      setValidation((prevState) => ({ ...prevState, subjectWarning: true }));
      isValid = false;
    }
    return isValid;
  };

  /* function to add label */
  const saveMailHandler = async () => {
    if (validate()) {
      let templateId = selectedTemplate ? selectedTemplate.value : null;

      let mailData = {
        receivermails: receivermails,
        subject: mailSubject,
        sender: currentUser._id,
        description: mailDescription,
        messagetemplate: templateId,
        linktrackers: linkTrackerBlock,
        uploads: fileUploadIds,
      };

      console.log("mailData=======>", mailData);

      try {
        setMailSending(true);

        let requestUrl =
          url.API_BASE_URL + url.API_ADD_MESSAGE + `?token=${token}`;

        const response = await postData(requestUrl, mailData);

        console.log("Mail Response", response);

        setMailSending(false);

        if (response.status) {
          resetHandler();
        }
      } catch (error) {}
    } else {
      setWarningMessage("Please fill up all required fields");
    }
  };

  /* reset */
  const resetHandler = () => {
    resetFile();
    setMailDescription("");
    setSelectedTemplate(null);
    setMailSubject("");
    setUploadedFiles([]);
    setlinkTrackerBlock([]);
    setFileUploadIds([]);
    setValidation({
      emailWarning: false,
      subjectWarning: false,
    });
    setWarningMessage("");

    setEmails("");

    document.body.classList.remove("emailpopup_open");
  };

  useEffect(() => {
    if (emails !== "") {
      setReceivermails(emails);
    }
  }, [emails]);

  useEffect(() => {
    getAllMailTemplates();
  }, []);

  return (
    <div className="email_poupPnl email_popup_view">
      <div className="email_topPnl">
        {/* popup header start  */}
        <div className="emailtop_row">
          <div className="emailuser_outer">
            <div className="email_cross">
              <Link
                to="#"
                onClick={() => {
                  resetHandler();
                }}
              >
                <i className="material-icons-round"> close</i>
              </Link>
            </div>
            <div className="email_userInfo">
              <h3>{t("Send Mail")}</h3>
              {/* <Link to="#">{receivermails}</Link> */}
            </div>
          </div>
        </div>
        {/* popup header end  */}

        <div className="input_fill subject_fill">
          <div className="filter_hdng d-flex align-items-center justify-content-between mb-2">
            <h4>{t("Email Address")}</h4>
          </div>
          <input
            type="text"
            className="form-control mt-2"
            placeholder={t("Enter mail address")}
            value={receivermails}
            onChange={(e) => {
              setReceivermails(e.target.value);
              setValidation((prevState) => ({
                ...prevState,
                emailWarning: false,
              }));
              setWarningMessage("");
            }}
          />
        </div>

        {/* <!-- ========== Start mail template Section ========== --> */}
        <div className="input_fill subject_fill form-group">
          <div className="filter_hdng d-flex align-items-center justify-content-between mb-2">
            <h4>{t("Mail Template")}</h4>
          </div>
          <Select
            className=""
            placeholder={t("Select Mail Template")}
            options={mailTemplateList}
            value={selectedTemplate}
            onChange={(val) => {
              templateSelectionHandler(val);
            }}
          />
        </div>
        {/* <!-- ========== End mail template Section ========== --> */}

        {/* <!-- ========== Start mail variable Section ========== --> */}
        <div className="input_fill subject_fill form-group">
          <div className="filter_hdng d-flex align-items-center justify-content-between mb-2">
            <h4>{t("Mail Variable")}</h4>
          </div>
          <div className="row">
            <div className="col-lg-6 mb-lg-0">
              <Select
                className=""
                placeholder={t("Select Mail Variable")}
                options={mailVariables}
                onChange={(event) => {
                  clipboardCopy(event.value);
                }}
              />
            </div>
            <div className="col-lg-6">
              <div className="field-container d-flex flex-column flex-lg-row gap-3 gap-lg-0 align-items-lg-center  body-lg-bg rounded-10">
                {t(
                  "Select the mail variable you want to add. Next just paste where you want it use it."
                )}
              </div>
            </div>
          </div>
        </div>
        {/* <!-- ========== End mail variable Section ========== --> */}

        <div className="input_fill subject_fill">
          <div className="filter_hdng d-flex align-items-center justify-content-between mb-2">
            <h4>{t("Subject")}</h4>
          </div>
          <input
            type="text"
            className="form-control mt-2"
            placeholder="Subject"
            value={mailSubject}
            onChange={(e) => {
              setMailSubject(e.target.value);
              setValidation((prevState) => ({
                ...prevState,
                subjectWarning: false,
              }));
              setWarningMessage("");
            }}
          />
          {/* ---------- subject warning --------- */}
          {validation.subjectWarning && (
            <div className="error-message mt-2">
              <p className="d-flex align-items-center gap-1 text-danger">
                <span className="material-symbols-outlined">warning</span>
                <span>{t("Please enter a subject!")}</span>
              </p>
            </div>
          )}
        </div>
      </div>
      <div className="email_editorupload_pnl">
        <div className="edior_textPnl">
          <div className="editor_msgpnl">
            <JoditEditor
              ref={editor}
              value={mailDescription}
              config={editorConfig}
              onBlur={(newContent) => {
                // console.log("New content in on blur----->", newContent);
                setMailDescription(newContent);
              }}
              onChange={(newContent, event) => {
                // setMailDescription(newContent);
              }}
            />
          </div>
        </div>

        {/* ========= file upload start ========== */}
        <div className="input_fill subject_fill">
          <div
            className={
              isUploading ? "d-flex align-items-center gap-1 mb-1" : "d-none"
            }
          >
            <p className="fw-bold">{t("Please wait while uploading")}</p>{" "}
            {[1, 2, 3, 4, 5, 6, 7, 8].map((count, index) => {
              return (
                <div
                  key={index}
                  className="spinner-grow spinner-grow-sm"
                  role="status"
                  style={{ height: "6px", width: "6px" }}
                >
                  <span className="visually-hidden">Loading...</span>
                </div>
              );
            })}
          </div>

          <div className="input_fill subject_fill">
            <div className="filter_hdng d-flex align-items-center justify-content-between mb-2">
              <h4>Attachments</h4>
            </div>
            <input
              type="file"
              className="form-control pt-2"
              id="uploadedMailUploadFile"
              multiple
              onChange={uploadHandler}
            />
          </div>

          {uploadedFiles.length > 0 && (
            <ul className="uploaded-files d-flex flex-wrap gap-3 p-0 m-0 mt-3">
              {uploadedFiles.map((file, index) => {
                return (
                  <li
                    className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3"
                    key={index}
                  >
                    <Link to="#" onClick={() => downloadFileHandler(file)}>
                      <span className="d-block material-symbols-outlined">
                        download
                      </span>
                    </Link>
                    <p className="fw-medium">{file.name}</p>
                    <Link to="#" onClick={() => closeFileHandler(index)}>
                      <span className="d-block material-symbols-outlined">
                        close
                      </span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        {/* ========= file upload end ===========*/}

        {/* <!-- ========== Start list of link tracker Section ========== --> */}

        {linkTrackerBlock && linkTrackerBlock.length > 0 && (
          <div className="form-group input_fill subject_fill">
            <div className="row">
              <div className="col-lg-6 mb-4 mb-lg-0">
                <label htmlFor="" className="text-nowrap me-lg-3">
                  {t("Link Tracker")}{" "}
                </label>
              </div>
            </div>
          </div>
        )}

        {linkTrackerBlock &&
          linkTrackerBlock.map((trackerBlock, index) => {
            return (
              <div
                className="input_fill subject_fill form-group p-3 bg-gray-500 mb-3 action_link_block_bg"
                key={index}
              >
                <div className="row">
                  {/* ------ action link name start ------ */}
                  <div className="col-lg-6 mb-1">
                    <label>{t("Add url to track")}</label>
                    <input
                      type="text"
                      name="link"
                      className="form-control bg-white"
                      placeholder="https://lobees.com"
                      value={trackerBlock.link}
                      onChange={(e) => {
                        changeLinkTrackerValue(index, "link", e.target.value);
                      }}
                    />
                  </div>
                  {/* ------ action link name end ------ */}

                  {/* ------ action link external start ------ */}
                  <div className="col-lg-5 mb-1">
                    <label>{t("Label to display if link clicked")}</label>
                    <input
                      type="text"
                      name="label"
                      className="form-control bg-white"
                      placeholder={t("visited lobees")}
                      value={trackerBlock.label}
                      onChange={(e) => {
                        changeLinkTrackerValue(index, "label", e.target.value);
                      }}
                    />
                  </div>
                  {/* ------ action link external end ------ */}

                  <div className="col-lg-1 mb-1 pt-5">
                    <Link
                      to="#"
                      className="text-danger mt-2"
                      onClick={() => {
                        onDeleteLinkTrackerBlock(index);
                      }}
                    >
                      <span className="material-symbols-outlined">close</span>
                    </Link>
                  </div>
                </div>
              </div>
            );
          })}

        {/* <!-- ========== End list of link tracker Section ========== --> */}
      </div>

      <div className="bttm_send_pnl ">
        <div className="upload_sendOuter">
          <div className="dlete_sendBtnOuter ">
            <button
              type="button"
              className="btn btn-primary"
              onClick={saveMailHandler}
              style={{ cursor: mailSending ? "not-allowed" : "pointer" }}
              disabled={mailSending ? true : false}
            >
              {t("Send Mail")}
              {mailSending ? (
                <div
                  className="mx-2 spinner-border spinner-border-sm"
                  role="status"
                >
                  <span className="visually-hidden">Loading...</span>
                </div>
              ) : (
                <i className="material-icons-round"> send</i>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SendEmailPopup;
