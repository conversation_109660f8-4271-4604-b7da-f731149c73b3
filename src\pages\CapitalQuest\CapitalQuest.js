import { useEffect } from "react";
import AuthHeader from "components/common/AuthHeader";
import CapitalQuestMainPanel from "components/CapitalQuest/CapitalQuestMainPanel/CapitalQuestMainPanel";

const CapitalQuest = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      <CapitalQuestMainPanel />
    </>
  );
};

export default CapitalQuest;
