
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
/* font-family: 'Inter', sans-serif;*/
@import url('https://fonts.googleapis.com/css2?family=Tilt+Warp&display=swap');
 /* font-family: 'Tilt Warp', cursive; */
 @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap');
 /* font-family: 'Roboto', sans-serif; */

.material-icons-outlined, .material-icons, .material-icons-round{vertical-align: middle;}
/*******************Reset Css****************/

*{margin:0;padding:0;border:none;outline:none !important;box-sizing:border-box;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;-ms-box-sizing:border-box;-o-box-sizing:border-box;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;box-shadow:none;-webkit-box-shadow:none;-ms-box-shadow:none;-o-box-shadow:none}

html,body{padding:0;margin:0;border-radius:0;overflow-x:hidden;font-family:var(--bs-body-font-family);font-size:14px;font-weight:400;color:var(--bs-color-00)}
em {font-style: normal;}

a,button,input,input[type=buttton],input[type=submit],.btn{transition:all .1s ease-in-out;-webkit-transition:all .1s ease-in-out;-moz-transition:all .1s ease-in-out;-ms-transition:all .1s ease-in-out;-o-transition:all .1s ease-in-out;outline:none !important;text-decoration:none;border:none;font-style:normal;text-decoration:none}
input {width: 100%;}
img,figure {max-width: 100%;max-height: 100%;margin: 0;padding: 0;}

ul,ol,li{margin:0;padding: 0;}.list_stye_none, .list-style-none{list-style:none;padding:0}
a,a:hover,a:focus{text-decoration:none}
h1,h2,h3,h4,h5,h6,p{font-weight:400;margin:0}

input,textarea{padding:5px 10px;transition:all .1s ease;-webkit-transition:all .1s ease;-moz-transition:all .1s ease;-ms-transition:all .1s ease;-o-transition:all .1s ease}input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{opacity:1;color:var(--bs-placeholder-color)}input:-moz-placeholder,textarea:-moz-placeholder{opacity:1;color:var(--bs-placeholder-color)}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color: var(--bs-placeholder-color)}input:-ms-input-placeholder,textarea:-ms-input-placeholder{opacity:1;color:var(--bs-placeholder-color)}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.noradius{border-radius:0;-webkit-border-radius:0;-moz-border-radius:0;-ms-border-radius:0;-o-border-radius:0}.pointerNone{pointer-events:none;-webkit-pointer-events:none;-moz-pointer-events:none}.relative{position:relative}.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn){width:100%}.row{margin:0 -10px}.modal{padding:0 !important}
.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto{padding: 0 10px;}

  /* ::-webkit-scrollbar-track {border: 1px solid transparent;padding: 2px 0;background-color: transparent;opacity: 0;}
  ::-webkit-scrollbar{opacity: 0;}
  ::-webkit-scrollbar-thumb{opacity: 0;} */
  input[type=file]::-webkit-file-upload-button {display: none;}
::-webkit-calendar-picker-indicator {
    cursor: pointer;
}
h1,h2{font-family: 'Roboto', sans-serif;}

/*******************Reset Css****************/
:root{
	--bs-body-font-family: 'Inter', sans-serif;
	--bs-color-4E: #4EFFDF;
	--bs-color-ED: #EDF4FF;
	--bs-color-00: #000000;
	--bs-color-FF: #FFFFFF;
	--bs-color-D3: #D3D9E9;
	--bs-color-38: #384CFF;
	--bs-color-D6: #D6E1F3;
	--bs-color-E3: #E3E3EE;
	--bs-color-E8: #E8604D;
	--bs-color-E9: #E9AD00;
	--bs-color-F8: #f8fbff;
	--bs-color-5A: #5A6471;
	--bs-color-EA: #EAF1FB;
	--bs-color-31: #31A61E;
	--bs-color-37: #373744;
	--bs-color-AA: #AAB3BD;
	--bs-color-F1: #F1F1F1;
	--bs-color-75: #757B7F;
	--bs-color-9A: #9AFFED;
	--bs-color-DC: #DCDFFF;
	--bs-color-BB: #BBC2FF;
	--bs-color-E5: #E5E9EF;
	--bs-color-F2: #F2F6FD;
	--bs-color-DD: #DDDDDD;
  --bs-color-9A: #99A1AA;
  --bs-color-6A: #6A7687;
  --bs-color-ED: #EDEDF1;
  --bs-dark: #263077;
	--bs-light-gray: #D3E3FD;
  --premium-gradient: linear-gradient(90deg, #FFB760 0%, #FFE818 100%);
  --premium-shadow: 0px 1px 4px 0px rgba(180, 142, 25, 0.56);
  --gredient-light: linear-gradient(90deg, #F7E6E4 0%, #E1E3FA 100%);
  --bs-body-bg: #F2F4F6;
}
.bg-f2{background-color: var(--bs-color-F2);}.bg-ed{background-color: var(--bs-color-ED);}
.text-9a{color: var(--bs-color-9A);}.text-6a{color: var(--bs-color-6A);}.text-dark{color: var(--bs-dark);}.text-f2{color: var(--bs-color-F2);}
/* strat-component-css */

/* ===common-css---- */
/*----------buttons--------------*/
.btn-check:focus+.btn, .btn:focus{box-shadow: none;}
.btn {display: inline-flex;align-items: center;justify-content: center;height: 60px;padding: 0 30px;border-radius: 90px;color: var(--bs-color-00);background-color: var(--bs-color-4E);border: 1px solid transparent;box-shadow: none;outline: none;font-weight: 600;    font-size: 18px;line-height: 28px;position: relative;min-width: 90px;gap: 5px;}
.btn:hover{background-color: var(--bs-color-9A);}
.btn.btn-primary{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.btn.outline{border: 1px solid var(--bs-color-38);color: var(--bs-color-38);background-color: transparent;height: 36px;font-size: 14px;padding: 0 15px;}
.btn.outline span{display: inline-block;vertical-align: middle;}
.btn.outline:hover{border-color: var(--bs-primary);background-color: var(--bs-primary);color: var(--bs-color-FF);}
.btn.outline:hover span img{filter: brightness(100);}
.btn.black_btn{background-color: var(--bs-color-00);color: var(--bs-color-FF);}
.btn.black_btn:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.btn.white_btn{background-color: var(--bs-color-FF);color: var(--bs-color-38);}
.btn.white_btn:hover{background-color: var(--bs-color-DC);}
.btn.gray_btn{border: 1px solid var(--bs-color-5A);color: var(--bs-color-5A);background-color: transparent;height: 36px;font-size: 14px;padding: 0 15px;}
.btn.gray_btn:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);border-color: var(--bs-color-38);}
.btn.gray{background-color: var(--bs-color-ED);color: var(--bs-color-5A);}
.btn.gray:hover{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
.requested_btn span{opacity: 0.6;font-weight: 400;}
.btn .material-icons-outlined, .btn .material-icons{font-size: 20px;}
.btn.btn-danger{background-color: var(--bs-color-E8);color: var(--bs-white);}
.btn.btn-sm{height: 36px;font-size: 14px;padding: 0 15px;}
.btn.btn-blue{background-color: var(--bs-color-38);color: #fff;}
.btn.btn-blue:hover{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
.btn-gray-f2{background-color: var(--bs-color-F2);color: var(--bs-black);}
/*----------buttons--------------*/
/* =============selectbox============== */

.select2-container{width: 100% !important;display: block;}
.select2-container--open{z-index: 9999;}
.select2-container--default .select2-selection--single {background-color: var(--bs-color-ED);border: 1px solid transparent;border-radius: 5px;font-size: 16px;color: var(--bs-color-8E);height: 50px;display: flex;align-items: center; font-weight: 400; text-transform: capitalize;padding: 0 20px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{padding: 0;color: var(--bs-color-00);}
.select2-container--default .select2-selection--single .select2-selection__arrow b{border: 0;}
.select2-container--default .select2-selection--single .select2-selection__arrow{height: 12px;width: 12px;background-image: url(../images/select-arw.svg);background-repeat: no-repeat;background-position: center;margin: 0;top: 50%;transform: translateY(-50%);right: 15px;}

.select2-dropdown {background-color: var(--bs-color-FF);border: 1px solid transparent !important;border-radius: 10px;box-shadow: 0 0 20px rgba(0,0,0,.15);padding: 10px 0;}

.select2-container--default .select2-search--dropdown .select2-search__field {border: 1px solid var(--bs-color-ED);border-radius: 5px;font-weight: 500;
    height: 35px;}
.select2-container--default .select2-search--dropdown .select2-search__field:focus{border-color: var(--bs-color-4E);}
.select2-results__option{padding: 8px 20px;color: var(--bs-body-color);transition: 0.3s;font-weight: 500;font-size: 14px;}
.select2-container--default .select2-results__option--selected {background: var();color: var(--bs-color-24);font-size: 14px;
    transition: 0.3s;font-weight: 500;}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable{background-color: transparent;color: var(--bs-body-color);}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable:hover{background-color: var(--bs-color-ED);color: var(--bs-color-00);}

/* =============selectbox============== */

/* ====toggle====default */
.toggle_switch{display: flex;align-items: center;gap: 10px;margin-bottom: 24px;}
.toggle_switch span{font-size: 14px;line-height: 18px;color: var(--bs-color-00);}
.toggle_switch label:after{display: flex;align-items: center;}
.toggle_switch input[type=checkbox]{height: 0;width: 0;visibility: hidden;display: none;}
.toggle_switch label {cursor: pointer;text-indent: -9999px;width: 31px;height: 19px;background: var(--bs-color-ED);display: block;border-radius: 100px;position: relative;}
.toggle_switch label:after {content: '';position: absolute;top: 2.5px;left: 3px;width: 14px;height: 14px;background: var(--bs-color-FF);border-radius: 90px;transition: 0.3s;}
.toggle_switch input:checked + label {background: var(--bs-color-F0);}
.toggle_switch input:checked + label:after {left: calc(100% - 2px);transform: translateX(-100%);}

/* ====toggle====default */

/* Header==start */

/* =====================start--home--header =====*/
.overlay{position: fixed;left: 0;right: 0;top: 0;bottom: 0;background-color: rgba(0, 0, 0, 0.8);z-index: 100;display: none;}
.hamberger_menu, .menu_cross{display: none;}
.home_header{position: absolute;left: 0;right: 0;top: 0;transition: 0.5s;z-index: 99;}
.header_top{background: var(--bs-color-2B);padding: 10px 0;text-align: center;}
.top_text p, .top_text p a{font-weight: 500;font-size: 14px;line-height: 14px;color: var(--bs-color-FF);}
.top_text p a{text-decoration: underline;}
.top_text p a:hover{color: var(--bs-color-F0);}
.header_bttm{padding: 12px 0;background-color: transparent;transition: 0.4s;}
.bttm_innr{display: flex;align-items: center;justify-content: space-between;}
.home_hdrlogo a{display: inline-block;}
.header_right{display: flex;gap: 30px;align-items: center;}
.menu_list ul{display: flex;align-items: center;gap: 30px;}
.menu_list ul li a{font-weight: 500;font-size: 16px;line-height: 28px;color: var(--bs-color-FF);}
.menu_list ul li.active a{color: var(--bs-color-4E);}
.login_btns{display: flex;gap: 10px;align-items: center;}
.login_btns .btn{height: 34px;}
.shrink{position: fixed;background-color:var(--bs-color-38);left: 0;right: 0;top: 0;z-index: 100;}
.menu_list ul li a:hover{color: var(--bs-color-4E);}
.my_accountbtn .btn{padding: 0 20px;height: 50px;}

.news_header{background-position: center;background-repeat: no-repeat;background-size: cover;}
.news_bottm_menu{background-color: rgba(0,0,0,0.1);padding: 24px 0;}
.news_bottm_menu ul{display: flex;align-items: center;gap: 40px;}
.news_bottm_menu ul li a{font-size: 18px;color: var(--bs-color-FF);font-weight: 600;}
/* =====================end--home--header =====*/

.language_row{border-radius: 8px;overflow: hidden;}
.language_row a{width: 33.33%;text-align: center;height: 40px;padding: 0 8px;background-color: var(--bs-color-E5);border-right: 2px solid var(--bs-color-FF);display: flex;align-items: center;gap: 4px;justify-content: center;font-size: 14px;font-weight: 600;color: var(--bs-color-5A);text-transform: uppercase;}
.language_row a:last-child{border-right: 0;}
.language_row a.active{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
/* ====end--header--part====== */


/* =====start---footer==== */

.footer_outer{background-position: center;background-size: cover;background-repeat: no-repeat;background-color: var(--bs-color-00);padding: 100px 0;position: relative;}
.copyright p{font-weight: 400;font-size: 18px;line-height: 28px;color: var(--bs-color-FF);}
.ftr_item h3{font-weight: 600; font-size: 26px;line-height: 38px;margin-bottom: 29px;color: var(--bs-color-FF);}
.ftr_links li{margin-bottom: 15px;width: 50%;}
.ftr_links li:last-child{margin: 0;}
.ftr_links li a{font-weight: 400;font-size: 18px;line-height: 28px;color: var(--bs-color-FF);}
.ftr_links li a:hover{color: var(--bs-color-4E);}
.footer_top{padding-bottom: 100px;}
.ftr_socialpnl{display:flex;align-items:center;justify-content: flex-end;gap: 20px;}
.ftr_social ul{display:flex;align-items:center}
.ftr_social ul li{margin-right:15px}
.ftr_social ul li a{border-radius:6px;height:60px;width:60px;display:flex;align-items:center;justify-content:center;background: rgba(255, 255, 255, 0.1);border-radius: 50%;}
.ftr_social ul li a:hover{background-color: var(--bs-color-4E);border-radius: 50% 0;}
.ftr_social ul li a:hover img{filter: invert(1);}

.flg_selctBtn .select2-container--default .select2-selection--single{min-width:153px;background-color: rgba(255, 255, 255, 0.10);height: 40px;border-radius: 10px;}
.flg_selctBtn .select2-container--default .select2-selection--single .select2-selection__rendered{color: var(--bs-color-FF);}
.flg_selctBtn .select2-container--default .select2-selection--single .select2-selection__arrow{background-image: url(../images/select-arw-w.svg);}
.footer_bttm .row{align-items: center;}
.browse_text{text-align: right;}
.browse_text p, .browse_text p a{font-weight: 500;font-size: 14px;line-height: 18px;color: var(--bs-color-FF);}
.browse_text p a{text-decoration: underline;display: inline-block;}
.browse_text p a:hover{color: var(--bs-color-4E);}
.footer_logo{margin-bottom: 40px;}
.ftr_dots1{left: 0;}
.ftr_dots2{right: 0;}
.footer_outer .help_dots {bottom: 0;top: inherit;display: none;}

/* =end--footer---- */


/*===checkbox--radio-button--======*/
.radio-check{margin:0;cursor:pointer;display:flex;align-items:center}
.radio-check input[type='checkbox'], .radio-check input[type='radio'] {display: none;}
.radio-check input[type=checkbox]+span{width:19px;min-width:19px;height:19px;border:2px solid var(--bs-color-8E);border-radius:4px;background:var(--bs-color-FF);position:relative;margin-right:10px}
.radio-check input[type=checkbox]:checked+span:after{content:"";position:absolute;left:1px;top:1px;right:1px;bottom:1px;background:url(../images/check.svg) no-repeat center center;background-size:15px}
.radio-check input[type=radio]+span {width: 16px;min-width: 16px;height: 16px;border: 2px solid var(--bs-body-color);border-radius: 50%;    background: var(--bs-color-FF);position: relative;margin-right: 10px;background-color: transparent;}
.radio-check input[type=radio]:checked+span:after {content: "";position: absolute; left: 50%;top: 50%; background-color: var(--bs-color-F0);transform: translate(-50%, -50%);border-radius: 50%;height: 8px;width: 8px; 
}
.radio-check input[type=radio]:checked+span{border-color: var(--bs-color-F0);}

/*===checkbox-radio-button-======*/


/* ===common-css---- */

/* -------pagination----- */
.job_pagination {padding: 30px 0 0;}
.job_pagination ul {display: flex;align-items: center;justify-content: center;gap: 20px;}
.job_pagination ul li.prev_pagnation a, .job_pagination ul li.next_pagination a {background: var(--bs-color-FF);box-shadow: 0px 2px 12px rgb(29 28 76 / 12%);border-radius: 50%;height: 45px;width: 45px;}
.job_pagination ul li a {height: 35px;width: 38px; border-radius: 50%;font-size: 16px; color: var(--bs-color-00); display: flex;align-items: center;justify-content: center;border: 1px solid transparent;}
.job_pagination ul li.active a {background: var(--bs-color-38); color: var(--bs-color-FF);font-weight: 600;border-color: var(--bs-color-38);}

/* end-pagination */


@media(min-width:1600px){
    .footer_outer .help_dots{display: block;}
}
@media(max-width:1600px){
    .btn{font-size: 16px;height: 55px;}
    .ftr_social ul li a{height: 44px;width: 44px;}
    .ftr_social ul li a img{transform: scale(0.7);}
    .ftr_item h3 {font-size: 18px;line-height: 28px;margin-bottom: 20px;}
    .ftr_links li a{font-size: 15px;line-height: 24px;}
    .copyright p{font-size: 16px;}
    .select2-container--default .select2-selection--single{font-size: 14px;}
    .footer_outer{padding: 70px 0;}
}
@media(max-width:1199px){
    .menu_list ul, .header_right {gap: 16px;}
    .menu_list ul li a{font-size: 16px;line-height: 24px;}
    .footer_top {padding-bottom: 40px;}
}

@media(max-width:991px){
    .menu_list{position: fixed;background-color: var(--bs-color-38);box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);width: 260px;transition: 0.4s;transform: translateX(100%); top: 0;bottom: 0;right: 0;padding: 60px 0 15px;z-index: 101;}
    .hamberger_menu a{display: inline-block;}
    .header_right{gap: 15px;}
    .menu_list ul{display: block;gap: 0;}
    .menu_list ul li{display: block;}
    .hamberger_menu, .menu_cross{display: flex;}
    .add_menupnl .overlay{display: block;}
    .add_menupnl .menu_list{transform: translateX(0);}
    .add_menupnl{height: 100vh;overflow: hidden;}
    .menu_cross{background-color: var(--bs-color-4E);color: var(--bs-color-24);height: 35px;width: 35px;display: flex;align-items: center;justify-content: center;border-radius: 50%;position: absolute;right: 15px;top: 15px;}
    .menu_list ul li a{padding: 8px 20px;display: block;color: var(--bs-color-FF);}
    .menu_list ul li a:hover, .menu_list ul li.active a{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
    .addons_tabLinks ul li a{padding: 0 16px;}
    .footer_outer{padding: 40px 0;}
    .btn {padding: 0 20px;height: 44px;font-size: 14px;}
    .footer_bttm .col-lg-6:first-child{order: 2;}
    .footer_bttm .col-lg-6:last-child{order: 1;}
    .ftr_socialpnl{justify-content: center;margin-bottom: 20px;}
    .copyright{text-align: center;}
    .ftr_logopnl{text-align: center;margin-bottom: 30px;}
    .ftr_social ul{justify-content: center;}
    .news_bottm_menu {padding: 12px 0;}
    .news_bottm_menu ul li a {font-size: 14px;}
    .news_bottm_menu ul {overflow-x: auto;overflow-y: hidden;}
}

@media(max-width:767px){
    .job_pagination ul{gap: 6px;}
    .job_pagination ul li.prev_pagnation a, .job_pagination ul li.next_pagination a {height: 36px; width: 36px;}
    .job_pagination ul li a {height: 36px; width: 36px;font-size: 14px;}
    .ftr_socialpnl{justify-content: center;}
    .hdr_srchbar{display: none;}
    .srch_icon{display: block;margin-left: auto;color: var(--bs-color-FF);}
    .loginuser_area{margin: 0 0 0 15px;}
    .srch_icon button i{color: var(--bs-color-FF);}
    .hdr_logo {margin-left: 5px;}
    .addons_tabLinks ul li a{padding: 0 15px;white-space: nowrap;}
    .addons_tabLinks ul{flex-wrap: nowrap;overflow-x: auto;}
    .border_btn, .basic_btn{height: 40px;}
    .menu_bar a span {font-size: 26px;}
    .header_top{display: none;}
    .ftr_links li {margin-bottom: 20px;}
    .ftr_item h3{margin-bottom: 20px;}
    .ftr_item{margin-bottom: 20px}
    .footer_top {padding-bottom: 0;}
    .browse_text {text-align: center;}
    .btn {font-size: 14px;}
    .news_bottm_menu ul {gap: 24px;}
    .top_filter_row{flex-wrap: wrap;gap: 16px 0;}
    .left_text{width: 100%;}
    .filter_right{min-width: 100%;width: 100%;}
    .filter_select {min-width: auto;width: 100%;}
}
@media(max-width:575px){
    .inner_tab_links li a span em{display: none;}
    .login_btns .btn { height: 34px; padding: 0 10px; font-size: 12px;}
    .home_hdrlogo a{max-width: 175px;}
    .btn{padding: 0 20px;font-size: 14px;height: 42px;min-width: 85px;}
    .ftr_socialpnl{flex-wrap: wrap;}
    .flg_selctBtn {width: 100%;}
}


/* end-component-css--- */


/* ==banner--start */

.banner_area{background-color: var(--bs-color-38);background-position: top center;background-repeat: no-repeat;background-size: cover;padding: 165px 0 100px;}
.banner_content h1{font-weight: 400;font-size: 120px;line-height: 92%;text-transform: uppercase;color: var(--bs-color-FF);margin-bottom: 20px;}
.banner_content p{font-weight: 500;font-size: 18px;line-height: 28px;color: var(--bs-color-FF);max-width: 853px;}
.bnr_btns{margin: 40px 0 0;}

/* ==banner--end */
/* brand_company_sec */
.brand_company_sec{background-color: var(--bs-color-ED);padding: 50px 0;}
.brand_logo_inner{display: flex;align-items: center;flex-wrap: wrap;}
.left_info{width: 15%;border-right: 1px solid rgba(0,0,0,0.2);padding-right: 20px;}
.brand_logo_outer{display: flex;align-items: center;justify-content: space-between;}
.left_info h5{max-width: 162px;font-weight: 500; font-size: 18px;line-height: 24px;}
/* brand_company_sec */
/* how_works_area */
.page_hdng h5{font-weight: 500;font-size: 18px;line-height: 28px;margin-bottom: 10px;}
.page_hdng h2{font-size: 90px;line-height: 80px;text-transform: uppercase;}
.page_hdng{margin-bottom: 60px;}
.tab_links .nav-tabs{display: block;border: 0;}
.tab_links .nav-tabs .nav-link{border: 0;margin: 0;display: flex;align-items: center;padding: 15px 25px 15px 15px;border-radius: 90px;background-color: var(--bs-color-E3);font-weight: 500;font-size: 18px;line-height: 32px;color: var(--bs-color-00);width: 100%;gap: 6px;text-transform: capitalize;}
.tab_links .nav-tabs .nav-link span{height: 40px;width: 40px;min-width: 40px;display: flex;align-items: center;justify-content: center;background-color: var(--bs-color-FF);border-radius: 50%;}
.tab_links .nav-tabs .nav-item{position: relative;padding-bottom: 10px;}
.tab_links .nav-tabs .nav-item::after{position: absolute;content: ""; height: 10px;width: 1px;background-color: var(--bs-color-D6);bottom: 0;left: 35px;}
.tab_links .nav-tabs .nav-item:last-child::after{display: none;}
.tab_links .nav-tabs .nav-link.active{background-color: var(--bs-color-4E);}
.tab_pic{max-width: 646px;margin: 0 auto;text-align: center;}
.howwrks_outer .row{align-items: center;}
.tab_links .tab-pane{display: none;}

/* how_works_area */
/* help_system_area */
.pd_130{padding: 130px 0;}
.help_system_area{position: relative;background-color: var(--bs-color-4E);}
.help_dots{position: absolute;top: 0;pointer-events: none;}
.help_dots1{left: 0;}
.help_dots2{right: 0;}
.dark_grnheading h5, .dark_grnheading h2{color: var(--bs-color-00);}
.help_item{text-align: center;margin-bottom: 60px;}
.circle_icon{height: 82px;width: 82px;border-radius: 50%;display: flex;align-items: center;justify-content: center;
  background-color: rgba(0, 0, 0, 0.1);margin: 0 auto 20px;color: var(--bs-color-00);}
.circle_icon span{font-size: 30px;}
.help_text h3{font-weight: 600;font-size: 26px;line-height: 38px;margin-bottom: 15px;color: var(--bs-color-00);}
.help_text p{font-weight: 400;font-size: 20px;line-height: 32px;color: var(--bs-color-24);}
/* help_system_area */
/* blue_ecosystem_sec */
.blue_ecosystem_sec{background-position: top center;background-repeat: no-repeat;background-size: cover;}
.white_hdng h5, .white_hdng h2{color: var(--bs-color-FF);}
/* blue_ecosystem_sec */

/* contact_sec */
.contact_sec{background-color: var(--bs-color-38);}
.contact_sec .container{position: relative;}
.contact_dots{position: absolute;right: 0;top: 0;pointer-events: none;z-index: 1;}
.contact_sec .page_hdng{max-width: 1024px;}
.contact_form_area .form-group{margin-bottom: 16px;width: 100%;}
.contact_form_area .form-group .form-control{font-weight: 400;font-size: 18px;line-height: 28px;height: 60px;border-radius: 90px;background-color: transparent;border: 1px solid var(--bs-color-FF);border-radius: 10px;color: var(--bs-color-FF);box-shadow: none;padding: 0 30px;}
.input_filOuter{display: flex;gap: 10px;}
.contact_form_area .form-group.description_fill .form-control{padding: 16px 30px;border-radius: 12px;height: 157px;resize: none;}
.submit_btn{padding: 16px 0 0;}
.contact_form_area{position: relative;z-index: 2;}
.contact_form_area .form-control::placeholder{color: var(--bs-color-FF);}
.contact_form_area .form-control::-webkit-placeholder{color: var(--bs-color-FF);}
.contact_form_area .form-control::-moz-placeholder{color: var(--bs-color-FF);}
.contact_form_area .form-control:focus{border-color: var(--bs-color-4E);}

/* contact_sec */

.review_area{overflow-x: hidden;}
.rvw_outer{position: relative;}
.review_slideInner{padding: 40px;background-color: var(--bs-color-E3);border-radius: 30px;}
.koma{max-width: 40px;margin-bottom: 20px;}
.rvw_para p{font-weight: 600;font-size: 26px;line-height: 38px;}
.rvw_para{margin-bottom: 40px;}
.user_rvwpic{height: 70px;width: 70px;border-radius: 50%;overflow: hidden;margin-bottom: 5px;}
.user_name h4{font-weight: 600;font-size: 26px;line-height: 38px;margin-bottom: 5px;}
.user_name h6{font-weight: 400;font-size: 18px;line-height: 28px;}
.review_slideItem {padding: 0 10px;width: 24vw;}
.review_slider .slick-list{width: 100vw;}
.inBtns .slick-arrow{height: 60px;width: 60px;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: var(--bs-color-D3);cursor: pointer;background-repeat: no-repeat;background-position: center;opacity: 0.3;transition: 0.3s;font-size: 0;background-size: 24px;;}
.inBtns .slick-next{background-image: url(../images/arw-right.svg);}
.inBtns .slick-prev{background-image: url(../images/arw-left.svg);}
.inBtns .slick-arrow:hover{opacity: 1;}
.inBtns{position: absolute;left: 0;bottom: 0;display: flex;gap: 8px;}

.why_lobess_sec{background-color: var(--bs-color-38);}
.lobees_outer .row{align-items: center;}
.why_leftpnl{padding-right: 75px;}
.why_leftpnl .page_hdng{margin-bottom: 30px;}
.lobees_para p{font-weight: 400;font-size: 20px;line-height: 32px;color: var(--bs-color-FF);}
.lobees_para{margin-bottom: 30px;}
.user_comments h5{font-weight: 600;font-size: 26px;line-height: 38px;color: var(--bs-color-4E);margin-bottom: 20px;}
.user_comments h6{color: var(--bs-color-FF);font-size: 18px;line-height: 28px;}
.why_rightpnl{position: relative;}
.play_btn a{height: 95px;width: 95px;background-color: var(--bs-color-4E);border-radius: 50%;display: flex;align-items: center;justify-content: center;color: var(--bs-color-FF);position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);}
.play_btn a span{font-size: 30px;color: var(--bs-color-00);}
.video_bx {height: 350px;border-radius: 10px;overflow: hidden;}
.vdo_mdl.registration_modal .modal-header{padding-bottom: 0;}
.vdo_mdl .modal-dialog{max-width: 650px;}

/* ======================end---home--page--------------======= */

/* Start Inner pages */
.banner_area.inner_bnr{position: relative;content: ""; background-color: transparent;padding: 80px 0 0;}
.inner_bnr::after{position: absolute;left: 0;right: 0;top: 0;height: 80%;background-color: var(--bs-color-38);z-index: -1;pointer-events: none;content: "";}
.inner_bnr .banner_content{padding-top: 100px;}
.business_crm_sec{padding: 80px 0 0;}
.crm_business_outer .row{align-items: center;}
.business_rightpnl p{font-size: 20px;color: var(--bs-color-00);line-height: 30px;margin-bottom: 30px;}
.business_rightpnl p:last-child{margin-bottom: 0;}
.crm_business_outer{margin-bottom: 70px;}
.busniess_graph {margin-bottom: -350px;}
.iner_ofr_modules{background-color: var(--bs-color-4E);padding: 350px 0 140px;}
.ofr_row .row{align-items: center;}
.ofr_row{margin-bottom: 50px;}
.ofr_row:last-child{margin-bottom: 0;}
.ofr_row:nth-child(odd) .col-lg-6 .ofr_textpnl{padding-left: 50px;}
.ofr_row:nth-child(even) .col-lg-6 .ofr_textpnl{padding-right: 50px;}
.ofr_row:nth-child(even) .col-lg-6:first-child{order: 2;}
.ofr_row:nth-child(even) .col-lg-6:last-child{order: 1;}
.ofr_icon{height: 65px;width: 65px;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: var(--bs-color-24);color: var(--bs-color-FF);margin-bottom: 25px;}
.ofr_icon span{font-size: 34px;}
.bottom_hdng_area .page_hdng{margin-bottom: 0;}
.ofr_hdng h3{color: var(--bs-color-00);font-weight: 700;font-size: 48px;line-height: 48px;}
.ofr_hdng{margin-bottom: 25px;}
.ofr_para p{line-height: 32px;font-size: 20px;color: var(--bs-color-24);margin-bottom: 30px;}
.ofr_para p:last-child{margin-bottom: 0;}
.banner_content h1 span{color: var(--bs-color-4E);}
.talent_outer .page_hdng{margin-bottom: 30px;}
.talent_para p{font-size: 20px; line-height: 32px;}
.talent_btn{margin-top: 40px;}
.searching_sec{background-color: var(--bs-color-4E);}
.orange_circle{background-color: var(--bs-color-E8) !important;}
.blue_circle{background-color: var(--bs-color-38) !important;}
.ylw_circle{background-color: var(--bs-color-E9);}
.connect_link p{text-align: center;color: var(--bs-color-FF);font-size: 16px;line-height: 32px;margin-top: 20px;}
.connect_link p a{display: inline-block;color: var(--bs-color-4E);}
.colaboarte_plansec{background-color: var(--bs-color-4E);}
.searching_sec .tab_pic{max-width: 424px;}

/* Start Inner pages */

/* after-login pages */

/* start--after-login-header */
.hdr_lftmenu{display: none;}
.aftre_login_header{position: fixed;left: 0;right: 0;top: 0;z-index: 110;background: linear-gradient(90deg, #384CFF 0%, #7534FF 100%);
  box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);padding: 13px 15px;}
.header_inner{display: flex;align-items: center;justify-content: space-between;}
.left_serch_logo{display: flex;align-items: center;gap: 20px;width: 50%;}
.small_logo{min-width: 45px;display: inline-block;}
.header_serach{position: relative;}
.header_serach .form-control{height: 44px;background: var(--bs-color-FF); border: 1px solid rgba(255, 255, 255, 0.2);border-radius: 90px;box-shadow: none;color: var(--bs-color-5A);font-size: 16px;padding: 0 15px 0 41px;width: 500px;}
.srch_btn{position: absolute;left: 10px;top: 50%;transform: translateY(-50%);background-color: transparent;cursor: pointer;color: var(--bs-color-5A);}
.header_serach .form-control::placeholder{color: var(--bs-color-5A);}
.header_serach .form-control::-webkit-placeholder{color: var(--bs-color-5A);}
.right_user_icon > ul{display: flex;align-items: center;gap: 20px;}
.right_user_icon > ul > li > a{color: var(--bs-color-FF);position: relative;display: inline-block;}
.notify_nmbr{padding: 0 4px;background-color: var(--bs-color-4E);border-radius: 90px;font-size: 12px;line-height: 14px;color: var(--bs-color-00);position: absolute;right: -5px;top: -5px;}
.user_profile_img{height: 36px;width: 36px;min-width: 36px;border-radius: 50%;overflow: hidden;display: block;}
.right_user_icon .dropdown .dropdown-menu{padding: 8px;border: 0;background-color: var(--bs-color-ED);min-width: 262px;border-radius: 20px;}
.right_user_icon .dropdown .dropdown-menu li a{display: flex;align-items: center;padding: 12px 8px;font-size: 16px;color: var(--bs-color-5A);gap: 10px;border-radius: 10px;}
.right_user_icon .dropdown .dropdown-menu li a:hover{color: var(--bs-color-38);}
.user_profile_outer{background-color: var(--bs-color-FF);padding: 8px;border-radius: 90px;margin-bottom: 16px;}
.menu_usr_profile{height: 30px;width: 30px;border-radius: 50%;overflow: hidden;display: block;min-width: 30px;}
.left_usr_info a{font-size: 16px;color: var(--bs-color-00);font-weight: 500;}
.left_usr_info a:hover{color: var(--bs-color-38);}
.right_usr_logout a{color: var(--bs-color-00);}
.right_usr_logout a i{font-size: 20px;}
.right_usr_logout a:hover{color: var(--bs-color-38);}

.bottom_menu_list > ul{display: flex;gap: 5px;}
.bottom_menu_list > ul > li > a{display: flex;align-items: center;padding: 0 10px;background: rgba(255, 255, 255, 0.15);border: 1px solid rgba(255, 255, 255, 0.2);border-radius: 90px;color: var(--bs-color-FF);gap: 10px;height: 40px;font-size: 16px;}
.bottom_menu_list > ul > li > a i{font-size: 22px;color: var(--bs-color-4E);}
.header_bottom_menu{padding-top: 20px;}
.feed_pnltotal.padding_130{padding-top: 160px;}
.blue_tags{color: var(--bs-color-38) !important;}
.bottom_menu_list > ul > li > a.active_menu{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
.activity_top_menu > ul > li> a i{color: var(--bs-color-FF);}
.bottom_menu_list > ul > li > a.active_menu .down_arw{color: var(--bs-color-00);}
.bottom_menu_list .dropdown-menu{padding: 10px 0;background-color: var(--bs-color-ED);box-shadow: 0px 15px 50px rgba(0, 0, 0, 0.2);
  border-radius: 10px;border: 0;min-width: 180px;max-height: 400px;overflow-y: auto;}
.bottom_menu_list .dropdown-menu li a{display: block;padding: 6px 16px;font-size: 16px;color: var(--bs-color-00);}
.bottom_menu_list .dropdown-menu li a:hover{color: var(--bs-color-38);}
.hdr_hamberger{display: none;}
.search_btn{display: none;}
.mobile_srch_filter{display: none;}

/* end--after-login-header */

.feed_pnltotal{padding: 100px 15px 40px;background-color: var(--bs-color-F8);position: relative;font-family: 'Roboto', sans-serif;}
.pnl_outer{display: flex;flex-wrap: wrap;margin: 0 -12px;}
.pnl_leftpnl, .pnl_rightpnl{width: 25%;padding: 0 12px;}
.pnl_mdlpnl{width: 50%;padding: 0 12px;}
.pnl_list_wrap .pnl_mdlpnl{width: 75%;}
.white_shadow_bx{background-color: var(--bs-color-FF);box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 10px;padding: 16px;}
.usr_left_top{margin-bottom: 10px;padding: 16px;display: flex;align-items: center;justify-content: space-between;}
.user_left{display: flex;align-items: center;gap: 8px;}
.pofile_pic{height: 36px;width: 36px;border-radius: 50%;overflow: hidden;}
.pofile_info h3{font-size: 16px;font-weight: 600;color: var(--bs-color-00);}
.pofile_info h5{color: var(--bs-color-DA);font-size: 16px;}
.user_right ul > li > a{color: var(--bs-color-5A);}
.user_right .dropdown-menu{padding: 5px 12px;right: 0 !important;left: inherit !important;transform: none !important;top: 25px !important;box-shadow: 0px 15px 50px 0px rgba(0, 0, 0, 0.20);border-radius: 10px;}
.user_right .dropdown-menu li a{padding: 7px 0px;display: flex; align-items: center; font-size: 16px;gap: 8px;}
.user_right .dropdown-menu li a i{font-size: 18px;}
.user_right .dropdown-menu li a:hover{background: linear-gradient(180deg, #384CFF 0%, #9441FF 100%); -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;}
  .left_menulist{padding: 16px;}
.left_menulist li a{display: flex;align-items: center;padding: 10px 0;gap: 15px;font-size: 16px;font-weight: 600;color: var(--bs-color-00);justify-content: space-between;}
.left_menulist li a i{background: linear-gradient(180deg, #384CFF 0%, #9441FF 100%); -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;}
.feed_menu_left{display: flex;align-items: center;gap: 15px;}
.feed_count{padding: 5px;display: inline-block;color: var(--bs-color-00);background-color: var(--bs-color-4E);font-size: 12px; line-height: 14px;border-radius: 90px;}
.left_menulist li a:hover, .left_menulist li.active a{color: var(--bs-color-38);}
.post_item{margin-bottom: 20px;}
.post_item:last-child{margin-bottom: 0;}
.top_usr_srch{display: flex;align-items: center;gap: 8px;margin-bottom: 16px;padding-left: 50px;}
.search_profile{height: 42px;width: 42px;min-width: 42px;border-radius: 50%;overflow: hidden;}
.search_fill{width: 100%;}
.search_fill .form-control{height: 58px;background-color: var(--bs-color-EA);padding: 0 25px;border-radius: 90px;font-size: 16px;color: var(--bs-color-5A);border: 0;box-shadow: none;display: flex;align-items: center;}
.blue_icon{background: linear-gradient(180deg, #384CFF 0%, #9441FF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.grn_icon{background: linear-gradient(180deg, #5F9B41 0%, #49BF95 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.ylw_icon{background: linear-gradient(180deg, #EFB153 0%, #FFEC44 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.ornge_icon{background: linear-gradient(180deg, #E8604D 0%, #FF961B 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.post_bttm_menu ul{display: flex;align-items: center;}
.post_bttm_menu ul li{width: 25%;text-align: center;}
.post_bttm_menu ul li a{font-size: 14px;color: var(--bs-color-00);}
.post_bttm_menu li + li{border-left: 1px solid var(--bs-color-E3);}
.user_post_top{display: flex;align-items: center;justify-content: space-between;margin-bottom: 10px;}
.post_left{display: flex;align-items: center;gap: 15px;}
.pofile_info h3{font-size: 16px;font-weight: 500;color: var(--bs-color-00);}
.pofile_info ul{display: flex;align-items: center;}
.pofile_info ul li{font-size: 12px;line-height: 16px;letter-spacing: 0.002em;color: var(--bs-color-5A);padding-right: 5px;margin-right: 5px;position: relative;}
.pofile_info ul li i{font-size: 14px;}
.pofile_info ul li:last-child{margin: 0;padding: 0;}
.pofile_info ul li + li::after{position: absolute;content: "";height: 6px;width: 6px;background-color: var(--bs-color-E3);border-radius: 50%;left: -8px;top: 50%;transform: translateY(-50%);}
.user_post_paragraph p{font-size: 16px;color: var(--bs-color-00);line-height: 22px;}
.user_post_paragraph span{display: block;margin-top: 10px;}
.user_post_paragraph span a{display: inline-block;font-size: 16px;color: var(--bs-color-00);font-weight: 600;}
.user_post_paragraph span a:hover{color: var(--bs-color-38);}
.user_post_icons{margin: 24px 0 0;}
.user_post_icons ul{display: flex;align-items: center;gap: 5px;}
.user_post_icons li{width: 100%;}
.user_post_icons li a{height: 36px;padding: 0 15px;background-color: var(--bs-color-ED);border-radius: 90px;display: flex;align-items: center;justify-content: center;gap: 3px;font-size: 14px;color: var(--bs-color-5A);}
.user_post_icons li a i{font-size: 20px;}
.postuser_detailsbx{margin-bottom: 10px;}
.postuser_detailsbx:last-child{margin-bottom: 0;}
.post_hdng h4{font-size: 14px;text-transform: uppercase;color: var(--bs-color-5A);font-weight: 700;}
.post_hdng{margin-bottom: 16px;}
.event_bx{padding: 20px 16px;}
.event_slideInnr{background-color: var(--bs-color-FF);padding: 20px 16px;border-radius: 10px;border: 1px solid var(--bs-color-E3);font-family: 'Roboto', sans-serif;}
.event_hdng h3{font-size: 16px;color: var(--bs-color-00);margin-bottom: 10px;font-weight: 500;}
.event_hdng h5{display: flex;align-items: center;color: var(--bs-color-5A);font-size: 14px;gap: 5px;}
.event_hdng h5 i {font-size: 17px;margin-top: 3px;}
.event_hdng{margin-bottom: 16px;}
.event_tags ul{display: flex;gap: 5px;margin-bottom: 16px;flex-wrap: wrap;}
.event_tags li{height: 34px;background-color: var(--bs-color-ED);padding: 0 12px;border-radius: 90px;display: flex;align-items: center;gap: 3px;}
.event_tags li i{font-size: 18px;min-width: 18px;}
.event_para p{font-size: 16px;color: var(--bs-color-5A);line-height: 24px;}
.event_user{margin-top: 30px;}
.event_user ul{display: flex;align-items: center;}
.event_user ul li a {height: 40px;width: 40px;background-color: var(--bs-color-E3);border: 0;display: block;border-radius: 50%;}
.event_user ul li {margin: 0 3px 0 0;}
.event_slideItem{padding: 0 8px;width: 25vw;}
.event_slideOuter, .people_slide_outer{position: relative;}
.inBtns.common_arws .slick-arrow{height: 24px;width: 24px;background-color: transparent;opacity: 1;}
.inBtns.common_arws{left: inherit;top: -40px;bottom: inherit;right: 0;position: absolute;margin: 0;}
.show_more{margin-top: 20px;text-align: center;}
.show_more a{display: inline-flex;align-items: center;justify-content: center;font-size: 16px;font-weight: 500;color: var(--bs-color-00);gap: 5px;}
.show_more a i{font-size: 20px;}
.show_more a:hover{color: var(--bs-color-38);}
.event_tags li .rating{font-weight: 600;}
.event_tags li .rating em{font-style: normal;font-weight: 400;}
.people_slideItem{padding: 0 8px;width: 14vw;font-family: 'Roboto', sans-serif;}
.people_slideInnr{padding: 16px;border: 1px solid var(--bs-color-E3);border-radius: 10px;text-align: center;}
.people_slideInnr .people_profile{height: 64px;width: 64px;margin: 0 auto 13px;overflow: hidden;border-radius: 50%;}
.people_slideInnr .people_profile img{width: 100%;height: 100%;}
.people_profile_info h3{font-size: 16px;color: var(--bs-color-00);font-weight: 500;margin-bottom: 5px;}
.people_profile_info h5{font-size: 14px;color: var(--bs-color-5A);margin-bottom: 15px;}
.people_profile_info .event_tags ul{justify-content: center;}
.contact_header{padding: 22px 16px;}
.pnl_rightpnl .white_shadow_bx{padding: 0;}
/* .contacts_srch_right ul{display: flex;align-items: center;gap: 15px;} */
.contact_header .post_hdng{margin: 0;}
.contacts_srch_right ul li a{color: var(--bs-color-5A);}
.cross_btn{display: none;}
.empty_usrpnl .chat_innrScroll{display: flex;align-items: center;justify-content: center;}
.empty_connection{padding: 16px;}
.empty_pic{margin-bottom: 10px;}
.empty_text h5{font-size: 14px;color: var(--bs-color-5A);}
.contact_srch{margin-top: 16px;position: relative;}
.contact_srch .form-control{height: 42px;background-color: var(--bs-color-ED);border-radius: 90px;padding: 0 16px 0 38px;background-image: url(../images/search.svg);background-position: left 16px center;background-repeat: no-repeat;border: 0;box-shadow: none;}
.search_list{position: absolute;left: 0;right: 0;top: 100%;background-color: var(--bs-color-ED);z-index: 9;display: none;border-radius: 10px;}
.srch_row{padding: 12px 16px;}
.srch_name_left{gap: 8px;font-size: 14px;color: var(--bs-color-5A);font-weight: 400;}
.srch_name_left i{font-size: 20px;}
.srch_name_cross i{font-size: 20px;color: var(--bs-color-5A);}

.contacts_srch_right .dropdown-menu{padding: 5px 12px;right: 0 !important; left: inherit !important; transform: none !important; top: 25px !important;box-shadow: 0px 15px 50px 0px rgba(0, 0, 0, 0.20);border-radius: 10px;border: 0;min-width: 240px;}
.contacts_srch_right .dropdown-menu li a{display: flex;align-items: center;gap: 8px;padding: 10px 0;font-size: 14px;color: var(--bs-color-00);}
.contacts_srch_right .dropdown-menu li a i{font-size: 20px;}
.contacts_srch_right .dropdown-menu li a:hover{color: var(--bs-color-38);}
.search_post_pnl .top_usr_srch{padding-left: 0;}

/* =====end====-feed-landing=========================== */

/* =======start--feed-landing-search------------------ */
.activity_hdng h3{font-size: 16px;color: var(--bs-color-00);font-weight: 500;margin-bottom: 10px;font-family: 'Roboto', sans-serif;}
.activity_bx{padding: 20px 16px;background-color: var(--bs-color-FF);box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1); border-radius: 10px;}
.people_row{display: flex;align-items: center;justify-content: space-between;padding: 15px 0;border-bottom: 1px solid var(--bs-color-E3);}
.left_people{display: flex;gap: 15px;}
.people_profile{height: 64px;width: 64px;border-radius: 50%;overflow: hidden;}
.similar_post_row .people_profile{height: 36px;width: 36px;}
.left_people .event_tags ul{justify-content: flex-start;}
.groups_item{padding: 20px 16px;border: 1px solid var(--bs-color-E3);border-radius: 10px;margin-bottom: 15px;font-family: 'Roboto', sans-serif;}
.groups_item:last-child{margin-bottom: 0;}
.bookmark_hdng{display: flex;align-items: flex-start;justify-content: space-between;margin-bottom: 8px;}
.bookmarks_grps a{display: inline-block;color: var(--bs-color-5A);}
.bookmark_hdng h3{margin-bottom: 0;}
.event_user.grps_user li:last-child a{height: auto;width: auto;background-color: transparent;color: var(--bs-color-5A);font-size: 16px;font-weight: 500;}
.grp_pic_txtInnr{display: flex;justify-content: space-between;}
.group_pic{height: 130px;width: 130px;min-width: 130px;background-size: cover;background-position: center;display: block;}
.top_company_row{display: flex;align-items: center;justify-content: space-between;margin-bottom: 15px;gap: 10px;flex-wrap: wrap;}
.company_logotxt span{color: var(--bs-color-5A);}
.jb_rating{height: 34px;background-color: var(--bs-color-ED);border-radius: 90px;padding: 0 10px;color: var(--bs-color-5A);font-weight: 700;display: flex;align-items: center;gap: 5px;font-size: 15px;}
.jb_rating i{font-size: 20px;}
.jb_rating span em{font-weight: 400;font-size: 13px;}
.rating_tags{gap: 15px;}
.tags_icon a{display: inline-block;color: var(--bs-color-5A);}
.tags_icon a:hover{color: var(--bs-color-38);}
.event_hdng h4{font-weight: 500;font-size: 15px;line-height: 18px;color: var(--bs-color-31);margin-bottom: 10px;}
.event_tags li a{color: var(--bs-color-5A);}
.people_row.course_row{justify-content: flex-start;gap: 20px;}
.course_row:last-child{border-bottom: 0;}
.course_left{width: 128px;min-width: 128px;}
.course_right{width: 100%;}

.notication_bx  .contact_header{padding: 0;margin-bottom: 16px;font-family: 'Roboto', sans-serif;}
.notication_bx{max-width: 912px;margin: 0 auto;}
.notify_innrbx > h3{font-size: 14px;color: var(--bs-color-5A);font-weight: 700;text-transform: uppercase;margin-bottom: 15px;}
.notify_row{display: flex;align-items: center;justify-content: space-between;padding: 10px 0;border-bottom: 1px solid var(--bs-color-E3);}
.notify_row:nth-child(even){background-color: var(--bs-color-F2);}
.notify_left{display: flex;align-items: center;gap: 10px;}
.notify_icon{height: 30px;width: 30px;background-color: var(--bs-color-EA);display: flex;align-items: center;justify-content: center;border-radius: 50%;color: var(--bs-color-38);}
.notify_icon i{font-size: 16px;}
.notify_status h6{font-size: 12px;color: var(--bs-color-5A);font-weight: 500;}
.notify_innrbx{margin-bottom: 30px;}
.notify_innrbx:last-child{margin-bottom: 0;}
.notication_bx .post_hdng h4{font-size: 18px;}

.bottom_menu_list .feed_menu_left{gap: 6px;}
/* =======end--feed-landing-search------------------ */


/* ====activities--page--start====== */
.user_post_paragraph{margin-bottom: 10px;}
.postpic_outer{display: flex;gap: 10px;}
.post_pic{margin-bottom: 10px;position: relative;}
.post_pic:last-child{margin-bottom: 0;}
.more_img{position: absolute;left: 0;right: 0;top: 0;bottom: 0;background-color: rgba(0,0,0,0.6);display: flex;align-items: center;justify-content: center;font-size: 16px;color: var(--bs-color-FF);font-weight: 500;}
.more_img:hover{color: var(--bs-color-4E);}
.user_post_paragraph p{margin-bottom: 20px;}
.user_post_paragraph p:last-child{margin-bottom: 0;}
.back_btn a{display: inline-flex;align-items: center;height: 40px;padding: 0 13px;background-color: var(--bs-color-ED);border-radius: 90px;color: var(--bs-color-00);font-size: 16px;color: var(--bs-color-00);font-weight: 600;gap: 3px;}
.back_btn a i{font-size: 20px;}
.back_btn{margin-bottom: 16px;}
.post_comment_area{margin-top: 24px;}
.search_fill.comment_search{position: relative;}
.search_fill.comment_search .form-control{height: 44px;background-color: transparent;box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);padding-right: 50px;background-color: var(--bs-color-FF);}
.emoji_btn{position: absolute;right: 15px;top: 50%;transform: translateY(-50%);color: var(--bs-color-00);}
.commnt_hdng{margin-bottom: 20px;}
.commnt_hdng h3{display: inline-flex;align-items: center;gap: 10px;font-size: 16px;cursor: pointer;}
.comments_row{display: flex;gap: 16px;margin-bottom: 24px;}
.comments_row:last-child{margin-bottom: 0;}
.comnts_right{width: 100%;}
.comment_bx{background-color: var(--bs-color-ED);padding: 10px;border-radius: 10px;}
.comnts_right .user_post_icons li{width: auto;}
.comnts_right .user_post_icons{margin-top: 10px;}
.comnts_right .user_post_icons ul{gap: 20px;}
.comnts_right .user_post_icons li a{height: auto;background-color: transparent;padding: 0;}
.similar_postpnl{padding: 0 16px;}
.similar_post_row{padding: 16px 0;border-bottom: 1px solid var(--bs-color-E3);}
.user_post_icons li.active a i, .user_post_icons li.active a span{color: var(--bs-color-E8);}
.comments_row.selected .comment_bx{background-color: var(--bs-color-38);}

.comments_row.selected .pofile_info h3, .comments_row.selected .pofile_info ul li, .comments_row.selected .user_post_paragraph p, .comments_row.selected .user_right ul > li.dropdown > a{color: var(--bs-color-FF);}

.filter_canvas .offcanvas-header h3{color: var(--bs-color-5A);font-size: 14px;text-transform: uppercase;font-weight: 700;}
.filter_canvas .offcanvas-header a{color: var(--bs-color-5A);}
.filter_canvas .offcanvas-body{padding: 0;}
.filter_item{padding: 12px 16px;border-bottom: 1px solid #DDEAFF;}
.filter_item:last-child{border-bottom: 0;}
.filter_hdng  h4{font-size: 16px;color: var(--bs-color-00);font-weight: 600;}
.filter_information .radio-check{gap: 8px;}
.filter_information .radio-check input[type=radio]+span{border-color: var(--bs-color-5A);margin: 0;}
.filter_information .radio-check p{font-size: 14px;color: var(--bs-color-5A);font-weight: 400;}
.filter_information .radio-check input[type=radio]:checked+span{background-color: var(--bs-color-31);border-color: var(--bs-color-31);}
.filter_information .radio-check input[type=radio]:checked+span:after{background-color: var(--bs-color-FF);}
.filter_rating li{margin-bottom: 12px;}
.filter_information .radio-check p i{font-size: 18px;display: inline-block;vertical-align: middle;}
.filter_information .radio-check input[type='checkbox'] + span{margin: 0;}
.filter_information .radio-check input[type='checkbox'] + span{border-color: var(--bs-color-5A);}
.filter_information .radio-check input[type='checkbox']:checked + span{background-color: var(--bs-color-31);border-color: var(--bs-color-31);}
.filter_information .radio-check input[type=checkbox]:checked+span:after{background: url(../images/check_w.svg) no-repeat;background-size: 14px;}
.filter_information .form-control{height: 48px;padding: 16px;background-color: var(--bs-color-ED);border-radius: 8px;color: var(--bs-color-5A);box-shadow: none;border-color: transparent;}
.filter_canvas .process_btns_outer{padding: 16px;}
.filter_canvas .process_btns_outer .btn{height: 48px;}
.post_comment_area .top_usr_srch{padding-left: 0;}
.copy_links_popup{position: absolute;left: 20px;bottom: 20px;}
.copy_links_popup a{border-radius: 90px;border: 1px solid var(--bs-color-E3);background-color: var(--bs-color-FF);box-shadow: 0px 15px 30px 0px rgba(29, 28, 76, 0.20);padding: 12px 16px;color: var(--bs-color-00);font-size: 14px;}
.copy_links_popup a i{color: var(--bs-color-31);font-size: 18px;}
.filter_canvas .offcanvas{max-width: 326px;}



/* ====activities--page--end====== */

/* people--page-strt */
.people_bx{margin-bottom: 10px;}
.people_bx:last-child{margin-bottom: 0;}
.people_bx .people_row{padding: 0;border: 0;}
.filter_search_wrap{background-color: var(--bs-color-F2);border: 1px solid var(--bs-light-gray);border-radius: 90px;padding-right: 16px;}
.filter_search_wrap .select2-container{max-width: 115px;}
.search_filter_form .form-control{border: 0;background-color: transparent;box-shadow: none;}
.filter_srch_btn{background-color: transparent;cursor: pointer;font-size: 18px;}
.filter_refresh_btn a{color: var(--bs-color-5A);}
.filter_refresh_btn a:hover{color: var(--bs-color-38);}

.header_inner li.dropdown.notification .dropdown-menu {min-width: 380px; max-height: 30rem; overflow-y: auto;transform: none !important;margin-top: 30px !important; left: auto !important;right: 0 !important;} 
/* ---notification-dropdown--- */
.notification-dropdown{max-width: 350px;}
.tabs_links_wrap{padding: 8px;}
.tabs_links_wrap .nav-tabs{padding: 4px;border-radius: 12px;border: 0;background-color: var(--bs-color-F2);}
.tabs_links_wrap .nav-tabs .nav-link{width: 50%;text-align: center;border-radius: 12px;color: var(--bs-black);}
.tabs_links_wrap .nav-tabs .nav-link.active{background-color: var( --bs-white);color: var(--bs-primary);}
.notify_row{padding: 10px 16px;border-bottom: 1px solid #DCE5F2;}
.notify_icon{min-width: 30px;height: 30px;width: 30px;}
.notify_cross a{color: #C4CED3;}
.notify_cross a:hover{color: var(--bs-primary);}
.notify_text{margin-right: auto;}
.bg-white{background-color: var(--bs-white);}
.notify_wrapper{position: relative;}
.notify_innrscroll { max-height: 300px; overflow-y: auto; }
.refresh{position: absolute;left: 50%;transform: translateX(-50%);bottom: 10px;z-index: 99;}
.refresh a{box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);height: 32px;width: 32px; }
.date_fill_outer{box-shadow: 0px 1px 0px 0px #DCE5F2; padding: 8px 16px;}
.date_fill_outer  .form-control{height: auto;box-shadow: none;padding: 0 0px 0 30px;background-image: url(../img/calendar.svg);background-position: left 6px top 50%;background-size: 15px;background-repeat: no-repeat;}
.apply_btn{background-color: transparent;border: 0;min-width: 70px;box-shadow: none;outline: none;}
.empty_today img{max-width: 100%;max-height: 100%;}
.notify_status .btn{height: 34px;font-size: 14px;}
input[type="date"]::placeholder { color: var(--bs-gray); }

.notify_status .btn{padding: 0 15px;}
.notify_status .btn:hover{background-color: var(--bs-color-38);}

/* --------------------------------------------- */

.search_result{left: 0;right: 0;top: 100%;z-index: 9;opacity: 0; -webkit-transform: translate(0, 10px); -moz-transform: translate(0, 10px); transform: translate(0, 10px); transition: opacity .2s ease-in, transform .2s ease-in, visibility .2s ease-in;visibility: hidden;margin-top: 15px;}
.search_result.show_result{opacity: 1;visibility: visible;        -webkit-transform: translate(0, 0); -moz-transform: translate(0, 0); transform: translate(0, 0); }



/* people--page-strt */

/* ----people--details----- */

.tab_links.people_tablinks ul{display: flex;gap: 5px;overflow-x: auto;overflow-y: hidden;flex-wrap: nowrap;}
.tab_links.people_tablinks .nav-tabs .nav-item::after{display: none;}
.tab_links.people_tablinks .nav-tabs .nav-link{padding: 0 18px;height: 43px;background-color: var(--bs-color-ED);font-size: 14px;}
.tab_links.people_tablinks .nav-tabs .nav-link.active{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.people_details_bx.white_shadow_bx {margin-bottom: 16px;padding: 20px;}
.people_details_bx:last-child{margin-bottom: 0;}
.details_hdng{margin-bottom: 16px;}
.details_hdng h4{font-size: 14px;font-weight: 700;color: var(--bs-color-5A);text-transform: uppercase;}
.results_para p{font-size: 16px;color: var(--bs-color-00);line-height: 24px;margin-bottom: 20px;}
.results_para p:last-child{margin-bottom: 0;}
.dates_pnlItem {padding-left: 55px;position: relative;padding-bottom: 30px;}
.dates_pnlItem:last-child{padding-bottom: 0;}
.dates_pnlItem:before {top: 0;left: 22px;width: 2px;background-color: var(--bs-color-ED);bottom: 0;position: absolute;content: "";}
.dates_pnlItem:last-child:before{display: none;}
.exprince_logo {height: 44px;width: 44px;left: 0;border: 2px solid var(--bs-color-ED);border-radius: 10px;font-size: 16px;color: var(--bs-color-8E);font-weight: 600;display: flex;align-items: center;justify-content: center;position: absolute;background: var(--bs-color-FF);}
.experience_hdng h3 {font-size: 16px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 12px;}
.experience_hdng h3 span {display: inline-block;font-size: 13px;color: var(--bs-color-5A);font-weight: 500;margin-left: 10px;}
.experience_hdng h5 {font-size: 15px;color: var(--bs-color-5A);font-weight: 500;margin-bottom: 10px;}
.recomend_row {position: relative;margin-bottom: 16px;}
.team_userOuter {display: flex;}
.team_usr_pic {height: 30px;width: 30px;min-width: 30px;border-radius: 50%;overflow: hidden;display: flex;align-items: center;justify-content: center;background-color: var(--bs-color-ED);}
.team_usrTxt {padding-left: 15px;}
.team_usrTxt h3 {font-size: 16px;color: var(--bs-color-00);font-weight: 600;line-height: 26px;}
.team_usrTxt h5 {font-size: 13px;color: var(--bs-color-5A);font-weight: 500;}
.team_usrTxt h6{font-size: 14px;color: var(--bs-color-5A);font-style: italic;}
.forum_para {margin: 15px 0;}
.forum_para p {color: var(--bs-color-00);font-size: 15px;line-height: 26px;margin-bottom: 15px;}
.forum_para p:last-child {margin-bottom: 0;}
.post_time { position: absolute;right: 0;top: 10px;font-size: 13px;color: var(--bs-color-5A);font-weight: 500;}
.recomend_row:last-child{margin-bottom: 0;}
.experts_skill ul {display: flex;flex-wrap: wrap;gap: 5px;}
.experts_skill li a {background: var(--bs-color-FF); border: 1px solid var(--bs-color-E3); border-radius: 90px;color: var(--bs-color-5A);
  font-weight: 500; font-size: 14px;display: inline-flex;  align-items: center;padding: 0 12px;height: 36px;}
.experts_skill li a:hover{background-color: var(--bs-color-4E);border-color: var(--bs-color-4E);}
.additional_info {margin-bottom: 20px;}
.additional_info h4 {font-size: 15px;color: var(--bs-color-5A);font-weight: 500;display: block;margin-bottom: 10px;}
.additional_info h4 i {margin-right: 3px;vertical-align: middle;display: inline-block;  font-size: 18px;}
.additional_info a {display: inline-block;font-size: 16px;color: var(--bs-color-38);font-weight: 600;}
.additional_info h3 {font-size: 16px;color: var(--bs-color-00);font-weight: 600;}
.portfolio_pic{margin-bottom: 10px;}
.service_row {margin-bottom: 16px;}
.service_row:last-child{margin-bottom: 0;}
.srvc_top{display: flex;align-items: center;justify-content: space-between;margin-bottom: 24px;}
.srvc_top h3{font-size: 16px;color: var(--bs-color-00);font-weight: 500;}
.srvc_top h3 span{color: var(--bs-color-31);font-size: 20px;}
.survey_btn{margin-top: 10px;}
.survey_btn a{height: 38px;border: 1px solid transparent;display: inline-flex;align-items: center;justify-content: center;padding: 0 15px;border-radius: 90px;font-size: 14px;font-weight: 600;gap: 5px;}
.survey_btn a i{font-size: 18px;}
.survey_btn .cmplete_btn{border-color: var(--bs-color-31);color: var(--bs-color-31);}
.survey_btn .start_outline_btn{border-color: var(--bs-color-38);color: var(--bs-color-38);}

.rating_star ul {display: flex;align-items: center;margin-top: 10px;}
.rating_star li a {display: inline-block;font-size: 14px;color: var(--bs-color-D3);}
.rating_star li.active a {background: linear-gradient(180deg, #E8604D 0%, #FF961B 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.feedback_share ul {display: flex; align-items: center;gap: 5px;}
.feedback_share li a {background: var(--bs-color-ED);border-radius: 8px;height: 35px;width: 35px;font-size: 16px;color: var(--bs-color-5A);
  display: flex;align-items: center;justify-content: center;}
.feedback_share li a i{font-size: 18px;}
.customer_feedbacks .post_time{right: 20px;top: 20px;}
.experts_holderPic{height: 125px;width: 125px;margin: 0 auto 10px;border-radius: 50%;}
.experts_holdertext{text-align: center;}
.experts_holdertext h3{font-size: 22px;color: var(--bs-color-00);font-weight: 500;margin-bottom: 10px;}
.experts_holdertext h3 span{display: block;font-size: 14px;line-height: 16px;letter-spacing: 0.002em;color: var(--bs-color-5A);font-weight: 400;}
.experts_holdertext .jb_rating {display: inline-flex;}
.expert_details{margin-top: 20px;}
.address{display: flex;justify-content: center;align-items: center;font-size: 14px;line-height: 16px;letter-spacing: 0.002em;gap: 5px;color: var(--bs-color-5A);margin-bottom: 10px;}
.address i{font-size: 18px;}
.address:last-child{margin-bottom: 0;}
.expert_social{margin: 24px 0 0;}
.expert_social ul{display: flex;gap: 10px;justify-content: center;}
.expert_social li a { background: var(--bs-color-FF);border: 1px solid var(--bs-color-E3);border-radius: 8px; height: 40px;width: 40px;  display: flex; align-items: center;justify-content: center;}
.feedback_jointbtnOuter{margin: 30px 0 0;display: flex;align-items: center;gap: 10px;justify-content: center;}
.feedback_jointbtnOuter flex-wrap.btn{height: 48px !important;min-width: auto;}
.request_modal .post_description{margin-top: 0;}
.details_apply .dropdown-menu{background-color: var(--bs-color-ED);box-shadow: 0px 15px 50px rgba(0, 0, 0, 0.2);border-radius: 10px;min-width: 185px;padding: 10px 0;border: 0;}
.details_apply .dropdown-menu li a{display: flex;align-items: center;font-weight: 400;font-size: 16px;line-height: 19px;gap: 10px;padding: 8px 16px;color: var(--bs-color-00);}
.details_apply .dropdown-menu li a i{font-size: 20px;}
.red_icon{color: var(--bs-color-E8) !important;}
.details_apply .dropdown-menu li a:hover{color: var(--bs-color-38);}
.connected_text p{margin: 20px 0 0;gap: 6px;color: var(--bs-color-5A);font-size: 14px;}
.connected_text p i{font-size: 18px;color: var(--bs-color-31);}
.tag_btn .btn i{color: var(--bs-color-38);}
.tag_btn .btn:hover i{color: var(--bs-color-FF);}
.tags_links li a{background-color: var(--bs-color-ED);border-radius: 90px;padding: 0 12px;height: 32px;font-size: 14px;color: var(--bs-color-5A);}
.tags_links li a i{min-width: 16px;font-size: 16px;}
/* .tags_links li a span{text-overflow: ellipsis;overflow: hidden;white-space: nowrap;max-width: 72px;} */


/* ----people--details----- */
/* ---start--grups and group-details--page----- */
.tab-pane .groups_item, .post_item_outer .groups_item{background: var(--bs-color-FF);box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);border-radius: 10px;}
.top_grp_info {display: flex;justify-content: space-between;}
.share_btn a{color: var(--bs-color-00);}
.experts_holdertext h5{font-size: 14px;color: var(--bs-color-5A);margin-bottom: 18px;}
.experts_holdertext h5 span{position: relative;padding-left: 10px;}
.experts_holdertext h5 span::after{position: absolute;content: ""; left: 2px;height: 4px;width: 4px;background-color: var(--bs-color-5A);top: 8px;border-radius: 50%;}
.groups_info .address, .groups_info .expert_social ul, .groups_info .feedback_jointbtnOuter{justify-content: flex-start;}
.pnl_mdlpnl .tab-content{padding-top: 10px;}
.people_details_bx  .accordion-button{padding: 15px 0;box-shadow: none;font-size: 14px;color: var(--bs-color-5A);font-weight: 500;}
.people_details_bx h2{font-family: 'Roboto', sans-serif;}
.accordion-button:focus, .accordion-button:not(.collapsed){background-color: transparent !important;}
.people_details_bx .accordion-body{padding: 0 0 10px;}
.sponsor_cmpny ul {display: flex;align-items: center;}
.sponsor_cmpny ul li {margin-right: 20px;padding-right: 20px;border-right: 1px solid var(--bs-color-ED);}
.sponsor_cmpny ul li a {display: block;text-align: center;}
.sponsor_cmpny ul li a i {max-width: 42px;margin: 0 auto 10px;}
.sponsor_cmpny ul li a span, .sponsor_cmpny ul li a i{display: block;}
.sponsor_cmpny ul li:last-child {margin-right: 0;padding-right: 0;border-right: 0;}
.upload_area {display: flex;flex-wrap: wrap;margin: 0 -5px;}
.upload_item {padding: 0 5px;}
.uploaded_innrBx, .upload_inner_btn {height: 135px;width: 135px;border: 2px solid var(--bs-color-ED);border-radius: 8px; position: relative;  padding: 20px 10px;text-align: center;}
.upload_icon {margin-bottom: 15px;height: 40px;}
.upload_txt h4 {font-size: 13px; color: var(--bs-color-00);font-weight: 500; margin-bottom: 10px;}
.upload_txt h5 {font-size: 10px;color: var(--bs-color-5A);}
.form_commnts_area {background-color: var(--bs-color-FF);border-radius: 8px;overflow: hidden;border: 1px solid var(--bs-color-E3);margin-bottom: 16px;}
.form_commnts_area .form-control {resize: none;height: 76px;width: 100%;box-shadow: none;padding: 16px 20px;background-color: transparent;
  border: 0;border-radius: 0;}
.furmcmnt_btn {display: flex;align-items: center;justify-content: flex-end;padding: 0 20px 15px; gap: 15px;}
.cmnt_btn { cursor: pointer; color: var(--bs-color-5A); background-color: transparent;position: relative;}
.file_btn {position: absolute;left: 0;right: 0;top: 0;bottom: 0;height: 100%;width: 100%;cursor: pointer;opacity: 0;}
.forum_shreBtn a {background: var(--bs-color-ED); border-radius: 50%;height: 34px;width: 34px;display: flex;align-items: center;justify-content: center;float: right;color: var(--bs-color-5A);}
.forum_shreBtn a span{font-size: 17px;}
.forumpic_outer {margin: 0 0 15px;display: flex; gap: 10px;}
.forum_pic{width: 130px;}
.form_share {display: flex; align-items: center; gap: 10px;}
.form_share a {display: inline-flex; align-items: center; justify-content: center; padding: 10px;background-color: var(--bs-color-ED);  border-radius: 8px;color: var(--bs-color-5A);font-size: 18px;}
.form_share a i {font-size: 18px;}
.form_share a em { font-style: normal;font-size: 13px;color: var(--bs-color-5A);font-weight: 500;margin-left: 5px;line-height: 100%;}
.form_share a span {height: 18px;width: 18px;border-radius: 50%; min-width: 18px;background-color: var(--bs-color-38);border-radius: 50%;
  color: var(--bs-color-FF);font-size: 13px;font-weight: 500;display: flex;align-items: center;justify-content: center;margin-left: 6px;}
.active_forum{background-color: var(--bs-color-ED);padding: 16px;border-radius: 8px;}
.commnts_user_fill{margin-top: 30px;}
.commnets_reply{margin-top: 30px;padding-left: 50px;}
.reply_row{margin-bottom: 24px;}
.active_forum .form_share a{background-color: var(--bs-color-FF);}
.forum_row{margin-bottom: 30px;}
.active_forum .forum_shreBtn a{background-color: var(--bs-color-FF);}
.similar_post_row .groups_info .experts_holdertext h3{font-size: 16px;line-height: 19px;text-align: left;}
.similar_post_row .share_btn a span{font-size: 18px;}
.commnts_user_fill .emoji_btn{background-color: transparent;color: var(--bs-color-38);cursor: pointer;}
.details_btn{margin-top: 20px;}
.groups_item.course_row {padding-left: 16px;padding-right: 16px;}
.frst_user_text{margin: 30px 0 0;}
.frst_user_text p{color: var(--bs-color-38);font-size: 15px;font-weight: 400;line-height: normal;}
.contact_rightpnl .user_name p{white-space: nowrap;overflow: hidden;max-width: 170px;text-overflow: ellipsis;}
.share_social_row li{width: 16.66%;text-align: center;}
.share_social_row{padding-top: 16px;margin-bottom: 32px;padding-bottom: 32px;border-bottom: 1px solid var(--bs-color-DC);}
.share_social_row li a{color: var(--bs-color-00);font-size: 12px;font-style: normal;font-weight: 500;line-height: normal;}
.share_copy_link .copy_fill .form-control{background-color: var(--bs-color-F2);font-size: 15px;color: var(--bs-color-5A);padding-right: 50px;border-radius: 10px;border-color: transparent;}
.share_copy_link .copy_fill .form-control:focus{border-color: var(--bs-color-38);}
.copy_fill{position: relative;}
.link_copy{position: absolute;right: 16px;top: 50%;transform: translateY(-50%);color: var(--bs-color-5A);}
.post_modal.share_modal .modal-dialog{max-width: 620px;}
.link_copy span{font-size: 18px;}
.opportunities_table{overflow-x: auto;}
.opportunities_table table{width: 100%;box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.25), 0px 2px 4px 0px rgba(0, 0, 0, 0.10);}
.opportunities_table thead th{padding: 16px;font-weight: 700;text-transform: uppercase;}
.opportunities_table tr th, .opportunities_table tr td{color: var(--bs-color-5A);font-size: 14px;white-space: nowrap;}
.opportunities_table tr td{padding: 12px 16px;}
.opportunities_table tr:nth-child(odd) td{background-color: var(--bs-color-FF);}
.opportunities_table tr:nth-child(even) td{background-color: var(--bs-color-F2);}

/* ---end--grups and group-details--page----- */
/* ------event & events-details-------- */
.product_row.white_shadow_bx{padding: 20px;}
.product_row{margin-bottom: 20px;display: flex;align-items: center;gap: 30px;}
.product_row:last-child{margin-bottom: 0;}
.product_pic{width: 135px;min-width: 135px;}
.product_info > h3{font-weight: 500;font-size: 16px;line-height: 19px;color: var(--bs-color-00);margin-bottom: 10px;}
.product_list ul{display: flex;align-items: center;}
.product_list ul li{padding-right: 10px;margin-right: 10px;border-right: 1px solid var(--bs-color-E3);display: flex;align-items: center;gap: 5px;font-size: 14px;color: var(--bs-color-5A);}
.product_list ul li:last-child{margin-right: 0;padding-right: 0;border: 0;}
.product_list ul li b{color: var(--bs-color-00);}
.product_price{margin-top: 20px;}
.product_price h3{color: var(--bs-color-31);font-weight: 600;font-size: 16px;line-height: 19px;}
.similar_post_row .ai_product_row {padding: 0;border: 0;}
.ai_links li{cursor: pointer;}
/* ------event & events-details-------- */

/* job-& job-details */

.pnl_mdlpnl.job_rightpnl{width: 75%;}
.pnl_list_wrap .pnl_mdlpnl.job_rightpnl{width: 100%;}
.job_outer{display: flex;gap: 10px;}
.job_leftpnl{width: 325px;min-width: 325px;max-height: 550px;overflow-y: auto;}
.results_rightPnl{width: 100%;}
.progres_tagsouter{display: flex;align-items: center;justify-content: space-between;margin-bottom: 15px;}
.tags_lftpnl .event_tags ul{margin-bottom: 0;}
.tags_prgrsspnl {display: flex;align-items: center;}
.tags_prgrsspnl h5 {font-size: 15px; color: var(--bs-color-5A);font-weight: 500;margin-right: 10px;}
.tags_prgrsspnl h5 span {color: var(--bs-color-00);font-weight: 600;}
.progess_outer {min-width: 100px;background-color: var(--bs-color-ED);height: 10px;border-radius: 90px; position: relative;}
.progress_bar {position: absolute;top: 0;left: 0;bottom: 0;background-color: var(--bs-color-38);border-radius: 90px;}
.job_hdng h3 {font-size: 22px;color: var(--bs-color-00);font-weight: 700;margin-bottom: 10px;}
.job_hdng h5 {font-size: 15px; color: var(--bs-color-31);font-weight: 500;}
.result_btnArea {margin: 25px 0 0;display: flex;align-items: center;gap: 5px;}
.result_btnArea .btn{height: 48px;padding: 0 20px;}
.results_innr {padding: 30px 0 0;}
.results_para p, .results_lising li {font-size: 15px;color: var(--bs-color-5A);line-height: 24px;margin-bottom: 15px;font-weight: 400;}
.results_item {margin-bottom: 30px;}
.results_item h3 {font-size: 16px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 15px;}
.results_lising ol {padding-left: 15px;}
.results_lising li {margin-bottom: 10px;list-style: auto;}
.job_modal .modal-dialog {max-width: 996px;margin: 1.75rem auto;}
.job_modal .modal-header {background: var(--bs-color-ED);border-radius: 10px 10px 0px 0px;position: relative;display: block;padding: 40px 30px 25px;border: 0;}
.job_modal .event_tags li{background-color: var(--bs-color-FF);}
.job_modal .company_logotxt {margin-bottom: 10px;}
.job_modal .company_logotxt span {font-weight: 500;}
.company_logotxt span {font-size: 15px;color: var(--bs-color-5A);display: inline-block;}
.modal_hdng{margin-bottom: 15px;}
.modal_hdng h3 {font-size: 24px;color: var(--bs-color-00);font-weight: 600;}
.job_modal .modal-header .close {position: absolute;height: 34px;width: 34px;background-color: var(--bs-color-FF);border-radius: 50%;  right: 20px;font-size: 18px;color: var(--bs-color-5A); top: 20px; padding: 0; margin: 0;display: flex;align-items: center;justify-content: center;z-index: 99;opacity: 1;}
.prsnl_detailsArea {border-bottom: 1px solid var(--bs-color-ED); padding: 30px 20px; background-color: var(--bs-color-FF);}
.others_detailsArea {padding: 30px 40px 30px;}
.prsnl_detailsArea .row {margin: 0 -5px -20px;}
.prsnl_detailsArea .col-sm-6 { padding: 0 5px;}
.jobinput_fill label {font-weight: 600;color: var(--bs-color-00);font-size: 14px;margin-bottom: 13px;}
.input_fill.jobinput_fill .form-control {height: 50px;background-color: var(--bs-color-ED);padding: 0 35px 0 20px;font-size: 15px;color: var(--bs-color-00);font-weight: 500;border-radius: 8px;border-color: transparent;box-shadow: none;}
.prsnl_detailsArea .jobinput_fill {margin-bottom: 20px;}
.prsnl_detailsArea .col-lg-4{margin-bottom: 20px;}
.job_fillOuter .jobinput_fill {width: 50%;padding: 0 6px;}
.details_input_txt label {margin: 0;color: var(--bs-color-00);font-weight: 600;font-size: 14px;}
.job_fillOuter {display: flex;flex-wrap: wrap;margin: 0 -6px;}
.details_row {margin-bottom: 10px;}
.details_row .row {align-items: center;}
.details_row .input_fill {margin-bottom: 0;position: relative;}
.uro_sign {background-image: url(../images/uro.svg);background-repeat: no-repeat;background-position: right 12px top 50%;}
.uploaded_innrBx .delete_btn {position: absolute;right: 10px;top: 10px;margin: 0;}
.copy_link {right: 12px;top: 50%;position: absolute;transform: translateY(-50%);line-height: 100%;color: var(--bs-color-5A);}
.description_fill.input_fill .form-control {height: 103px;resize: none;padding-top: 15px;padding-bottom: 15px;}
.chek_row {padding: 15px 0;}
.radio-check {margin: 0;cursor: pointer;display: flex;}
.radio-check input[type='checkbox'], .radio-check input[type='radio'] {display: none;}
.radio-check input[type='checkbox'] + span {width: 19px;min-width: 19px;height: 19px;border-radius: 4px;border: 2px solid var(--bs-color-D3);position: relative;margin-right: 10px;}
.radio-check p {font-size: 15px;color: var(--bs-color-00);font-weight: 500;}
.radio-check input[type=checkbox]:checked+span:after {content: "";position: absolute;left: 1px;top: 1px;right: 1px;bottom: 1px;  background: url(../images/check.png) no-repeat center center; background-size: 10px;}
.results_lising {padding: 20px 0 0;}
.people_details_bx .results_innr{padding: 0;}
.dates_pnlItem {padding-left: 45px;position: relative;padding-bottom: 30px;}
.dates_pnlItem:before {top: 0; left: 10px;width: 2px; background-color: var(--bs-color-ED);bottom: 0; position: absolute; content: "";}
.complete_item:before {background-color: var(--bs-color-38);}
.dates_pnlItem:last-child::before{display: none;}
.dates_nmbr {width: 36px;height: 36px; border: 2px solid var(--bs-color-ED);border-radius: 10px;font-size: 16px;color: var(--bs-color-5A);
  font-weight: 600; display: flex;align-items: center; justify-content: center; position: absolute; left: -7px; background: var(--bs-color-FF);}
.complete_item .dates_nmbr { border-color: var(--bs-color-38); color: var(--bs-color-38);}
.dates_hdng {margin-bottom: 10px;}
.dates_hdng h4 { color: var(--bs-color-00);font-size: 16px;font-weight: 600;}
.complete_item .dates_hdng h4 {color: var(--bs-color-5A);}
.dates_listing h3 {font-weight: 500;font-size: 15px;color: var(--bs-color-38); margin-bottom: 15px;}
.dates_listing li {font-size: 14px;color: var(--bs-color-5A);font-weight: 500;margin-bottom: 10px;position: relative;  display: flex;align-items: center;gap: 10px;}
.dates_listing li i{font-size: 18px;color: var(--bs-color-31);}
.jobs_info_left .experts_holdertext{text-align: left;margin-top: 10px;}
.job_info ul{display: flex;align-items: center;margin-bottom: 10px;}
.job_info ul li{font-size: 14px;color: var(--bs-color-5A);padding-right: 10px;margin-right: 10px;border-right: 1px solid var(--bs-color-E3);}
.job_info ul li:last-child{margin: 0;padding: 0;border: 0;}
.job_info ul li span{color: var(--bs-color-00);}
.price_fill{height: 34px;display: inline-flex;align-items: center;justify-content: center;padding: 0 15px;border-radius: 90px;background: rgba(49, 166, 30, 0.1);color: var(--bs-color-31);font-weight: 500;font-size: 15px;line-height: 18px;margin-bottom: 10px;}
.jobs_info_left .jb_rating{display: inline-flex;font-weight: 400;}
.details_apply .btn{height: 44px;}
.job_sharebtn a{display: flex;align-items: center;justify-content: center;border: 1px solid var(--bs-color-E3);border-radius: 50%;height: 44px;width: 44px;color: var(--bs-color-5A);}
.job_sharebtn a span{font-size: 20px;}
.similar_post_row .experts_holdertext{text-align: left;}
.similar_post_row .company_logotxt{margin-bottom: 10px;}
.jobs_info_left .feedback_jointbtnOuter flex-wrap{margin-top: 20px;}
.right_people_btn a{gap: 5px;}
.right_people_btn a i{font-size: 18px;}
.accepted_btn, .accepted_btn:hover{color: var(--bs-color-31) !important;border-color: var(--bs-color-31) !important;background-color: transparent !important;}
.rejected_btn{border-color: var(--bs-color-E8) !important;color: var(--bs-color-E8) !important;background-color: transparent !important;}
.lg_medium{max-width: 588px;}
.profile_upload_bx{height: 250px;position: relative;border: 2px dashed var(--bs-color-AA);text-align: center;border-radius: 10px;overflow: hidden;}
.profile_upload_bx .upload_icon{margin-bottom: 5px;color: var(--bs-color-5A);}
.profile_pic{height: 240px;width: 240px;margin: 0 auto;overflow: hidden;border-radius: 50%;}
.profile_pic img{height: 100%;width: 100%;object-fit: cover;}

.invoice_offcanvas .offcanvas-end{max-width: 1050px;}
.text-primary{color: var(--bs-primary);}
.text-5a{color: var(--bs-color-5A);}
table.table { --bs-table-bg: var(--bs-body-bg); --bs-table-border-color: rgba(0, 0, 0, 0.1); border-collapse: separate; border-spacing: 0; border-vertical-spacing: 5px; -webkit-border-vertical-spacing: 5px; }

.table-wrapper table.table thead tr {
  background: var( --gredient-light);
}
table.table thead tr th::before, table.table thead tr th {
  position: relative;
  cursor: pointer;
}
table.table thead tr th, table.table tbody tr td {
  padding: 0.938rem 2.813rem 0.938rem 0.938rem;
  border-color: var(--bs-table-border-color);
}
.table-wrapper table.table thead tr th:first-child, .table-wrapper table.table tbody tr td:first-child {
  border-top-left-radius: 0.625rem;
  border-bottom-left-radius: 0.625rem;
}

table.table thead tr th::before, table.table thead tr th {
  position: relative;
  cursor: pointer;
}
table.table thead tr th::before, table.table thead tr th::after {
  font-family: 'Material Symbols Outlined';
  font-size: 1rem;
  opacity: 0.5;
  position: absolute;
  right: 0.938rem;
}
table.table thead tr th::before {
  content: 'keyboard_arrow_up';
  top: 0.625rem;
}

.invoice_offcanvas .form-control, .invoice_offcanvas .form-select {
  line-height: 1.625rem;
  padding: 0.625rem 1rem;
  border-color: var(--bs-body-bg);
  border-radius: 0.5rem;
}

.btn-close{height: 44px;width: 44px;opacity: 1;right: 15px;top: 15px;z-index: 99;}


/* job-& job-details */

/* company--& company-details */

.top_company_row.company_top{align-items: flex-start;}
.company_top .event_tags ul{margin-bottom: 0;}
.company_info_outer{padding-top: 16px;margin-top: 16px;border-top: 1px solid var(--bs-color-E3);}
.company_info_row{margin-bottom: 10px;display: flex;gap: 5px;align-items: center;}
.company_info_row:last-child{margin-bottom: 0;}
.company_info_row .event_tags ul{margin-bottom: 0;}
.company_info_row > label{font-size: 14px;color: var(--bs-color-00);font-weight: 500;min-width: 60px;}
.company_top .event_hdng h3{font-size: 18px;}
.company_pic{height: 64px;width: 64px;background-color: var(--bs-color-ED);display: flex;align-items: center;justify-content: center;border-radius: 50%;margin: 0 auto;}
.rewards_item {text-align: center;padding: 20px;}
.rewards_pic{max-width: 50px;margin: 0 auto 16px;}
.rewards_text h3{line-height: 19px;font-size: 16px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 5px;}
.rewards_text h6{font-size: 14px;color: var(--bs-color-5A);line-height: 16px;}
.rewards_item{margin-bottom: 16px;}
.rvw_rating_bx{display: flex;background-color: var(--bs-color-ED);padding: 20px;margin-bottom: 30px;border-radius: 8px;}
.left_rvwpnl{width: 40%;}
.right_rvwpnl{width: 50%;}
.left_rvwpnl > h2{color: var(--bs-color-37);font-size: 30px;font-weight: 600;line-height: 40px;font-family: 'Roboto', sans-serif;}
.left_rvwpnl > h2 span{font-size: 12px;color: var(--bs-color-5A);font-weight: 500;line-height: 18px;}
.left_rvwpnl ul{display: flex;}
.left_rvwpnl ul li a{color: var(--bs-color-D3);}
.add_rvw_btn{margin: 30px 0 0;}
.progress_row{display: flex;align-items: center;gap: 10px;margin-bottom: 16px;}
.progress_row:last-child{margin-bottom: 0;}
.progress_row .progess_outer{width: 100%;background-color: var(--bs-color-D3);}
.progress_star{font-size: 14px;color: var(--bs-color-00);font-weight: 400;min-width: 45px;}
.rvw_percent{color: var(--bs-color-5A);font-size: 14px;}
.review_commnts .forum_row{margin: 0;padding: 16px;border-bottom: 1px solid var(--bs-color-E3);}
.review_commnts .forum_row:last-child{border: 0;margin-bottom: 0;padding-bottom: 0;}
.esg_items{background-color: var(--bs-color-FF);padding: 16px;border-radius: 10px;box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10), 0px 0px 1px 0px rgba(0, 0, 0, 0.25);margin-bottom: 10px;}
.esg_items:last-child{margin-bottom: 0;}
.esg_items .event_tags ul{margin-bottom: 10px;}
.esg_items h3{font-size: 16px;font-weight: 500;line-height: 26px;}
.esg_modal .modal-header{padding: 30px 16px 16px;}
.esg_modal .process_hdng h3{margin-bottom: 8px;}
.esg_modal .process_hdng{margin-bottom: 0;}
.company_details_outer .pnl_mdlpnl{width: 75%;}
.company_btns .job_sharebtn a{color: var(--bs-color-38);}
.form_innr .form-group textarea.form-control{resize: none;height: 125px;padding: 16px 20px;}
.form-group .course_rvw_row{margin-top: 0;}
.ods_icons span{display: inline-block;height: 32px;width: 41px;}
.offcanvas {z-index: 9999;border: 0;font-family: 'Roboto', sans-serif;}
.esg_canvas .offcanvas{width: 100%;}
.top_logo_info li{padding-right: 12px;border-right: 1px solid var(--bs-color-D3);line-height: 100%;font-size: 14px;color: var(--bs-color-5A);}
.top_logo_info li:last-child{padding: 0;border: 0;}
.top_logo_info li i{font-size: 18px;}
.esg_hdng h2{font-size: 48px;font-weight: 600;line-height: 54px;margin-bottom: 24px;font-family: 'Roboto', sans-serif;}
.esg_hdng p{font-size: 16px;color: var(--bs-color-5A);line-height: 150%;}
.goals_rightpnl{text-align: center;}
.goals_leftpnl  > p{font-size: 16px;color: var(--bs-color-5A);line-height: 150%;margin-bottom: 30px;}
.listing_circle li{margin-bottom: 24px;display: flex;color: var(--bs-color-5A);font-size: 16px;gap: 8px;}
.listing_circle li span strong{font-weight: 600;}
.listing_circle li i{color: var(--bs-color-31);font-size: 20px;min-width: 20px;display: inline-block;margin-top: 3px;}
.listing_circle li:last-child{margin-bottom: 0;}
.goals_outer .row{align-items: center;}
.goals_outer{margin-bottom: 100px;}
.ceo_row{background-color: var(--bs-color-ED);padding: 30px;}
.ceo_row .row{align-items: center;}
.ceo_row{position: relative;}
.ceo_pic{margin-top: -68px;position: relative;}
.ceo_pic::after{position: absolute;content: "";left: 0;right: 0;top: 0;bottom: -30px;background-image: url(../images/ceo-img-dots.png);background-position: bottom;background-repeat: no-repeat;pointer-events: none;}
.ceo_pic img{width: 100%;}
.ceo_textpnl{max-width: 530px;padding-left: 70px;}
.esg_sub_heading{margin-bottom: 30px;}
.esg_sub_heading h3{font-size: 30px;line-height: 40px;font-weight: 600;}
.esg_sub_heading p{font-size: 16px;color: var(--bs-color-5A);margin-top: 30px;line-height: 150%;}
.ceo_row::after{position: absolute;content: "";top: 0;bottom: 0;right: 0;max-width: 200px;background-image: url(../images/ceo-dots.png);background-position: right center; background-repeat: no-repeat;pointer-events: none;}
.sustainability_row{padding: 95px 0;}
.esg_report_goals{border-bottom: 1px solid var(--bs-color-D3);}
.sustainability_pic{position: relative;}
.sustainability_pic img{display: inline-block;position: relative;z-index: 2;}
.sustainability_pic::after{position: absolute;content: "";left: 0;right: -40px;top: -55px;bottom: -40px;background-image: url(../images/sustainbility-dots.png);z-index: 1;background-position: center;background-repeat: no-repeat;pointer-events: none;}
.acchivement_sec{border-bottom: 1px solid var(--bs-color-D3);}
.achievements_row{padding: 65px 0 90px;}
.tab_links.esg_tablinks .nav-tabs .nav-item::after{display: none;}
.tab_links.esg_tablinks .nav-tabs .nav-link{padding: 10px 15px;line-height: 24px;color: var(--bs-color-5A);background-color: transparent;border: 1px solid var(--bs-color-D3);}
.tab_links.esg_tablinks .nav-tabs .nav-item{padding-bottom: 0;}
.tab_links.esg_tablinks .nav-tabs .nav-link.active, .tab_links.esg_tablinks .nav-tabs .nav-link:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);border-color: transparent;}
.impact_row{padding: 24px;background-color: var(--bs-color-ED);margin-bottom: 30px;}
.left_impact_info h3{font-size: 18px;margin-bottom: 10px;font-weight: 600;}
.left_impact_info li{font-size: 16px;color: var(--bs-color-5A);line-height: 100%;padding-right: 16px;border-right: 1px solid var(--bs-color-D3);}
.left_impact_info li:last-child{border: 0;padding: 0;}
.left_impact_info li i{font-size: 20px;}
.left_impact_info li.green{color: var(--bs-color-31);}
.right_impact_icons span{display: flex;align-items: center;justify-content: center;height: 52px;width: 52px;}
.icons_info_row {gap: 24px;padding: 20px 0;border-bottom: 1px solid var(--bs-color-D3);}
.icons_info_row:last-child{border: 0;}
.info_icon{height: 77px;width: 77px;min-width: 77px;}
.points_bx {margin-top: 10px;background-color: var(	--bs-color-F1);height: 32px;padding: 0 10px;font-size: 16px;color: var(--bs-color-5A);line-height: 150%;border-radius: 40px;}
.points_bx span{display: inline-block;padding-right: 16px;margin-right: 16px;border-right: 1px solid var(--bs-color-D3);line-height: 100%;}
.points_bx span:last-child{margin: 0;padding: 0;border: 0;}
.challenges_learning_bx{background-color: var(--bs-color-ED);padding: 48px;}
.achievements_row.upcoming_row{padding: 90px 0;}
.esg_tabs_row .row{align-items: center;}
.upcoming_row .info_icon_rightpnl .left_impact_info h3{margin-bottom: 0;}
.impact_stories_sec{padding: 90px 0 60px;}
.impact_slide_outer{position: relative;}
.impct_slide_innr{background-color: var(--bs-color-ED);}
.impact_pic{height: 250px;width: 250px;min-width: 250px;}
.impact_pic img{width: 100%;height: 100%;object-fit: cover;}
.impact_quatetion{padding: 32px;}
.impact_quatetion p{font-size: 16px;color: var(--bs-color-5A);font-size: 16px;line-height: 150%;margin-bottom: 34px;}
.impact_quatetion h4{font-size: 16px;font-weight: 600;}
.impact_quatetion h4 span{display: block;font-weight: 400;font-size: 14px;margin-top: 7px;}
.impact_slide_item{width: 50vw;}
.impact_arws{display: flex;align-items: center;justify-content: center;margin-top: 40px;gap: 16px;}
.impact_arws .slick-arrow{height: 50px;width: 50px;display: flex;align-items: center;justify-content: center;border: 1px solid var(--bs-color-D3);background-color: transparent;border-radius: 50%;}
.summery_pic{max-width: 470px;margin: 0 auto;position: relative;}
.achievements_rightpnl{text-align: center;}
.microsoft_logo{position: absolute;left: 0;right: 0;top: 50%;transform: translateY(-50%);text-align: center;}
.microsoft_summery_sec{padding: 65px 0 110px;}
.report_area{background-color: var(--bs-color-ED);padding: 60px 0;}
.report_row{gap: 10px;padding: 20px 0;border-bottom: 1px solid var(--bs-color-D3);}
.report_row:last-child{border: 0;}
.report_row h4{font-size: 18px;font-weight: 600;}
.report_doc{background-color: var(--bs-color-DC);border-radius: 90px;height: 38px;padding: 0 16px;}
.report_doc a{padding-right: 12px;margin-right: 12px;border-right: 1px solid var(--bs-color-AA);font-size: 14px;color: var(--bs-color-00);}
.report_doc a:hover{color: var(--bs-color-38);}
.report_doc h5{font-size: 14px;color: var(--bs-color-5A);}
.summery_outer{align-items: center;}
.esg_contact_area{background-color: var(--bs-color-4E);padding: 65px 0;}
.esg_contact_area .container{position: relative;}
.esgcontact_dots {position: absolute;top: 50%;transform: translateY(-50%);pointer-events: none;}
.esgcontact_dots1{left: 0;}
.esgcontact_dots2{right: 0;}
.esg_contact_info li a{display: flex;align-items: center;gap: 10px;font-size: 16px;color: var(--bs-color-00);font-weight: 600;}
.esg_contact_info li a i{font-size: 20px;}
.esg_contact_info li{margin-bottom: 12px;}
.doc_link .btn{height: 32px;font-size: 14px;background-color: var(--bs-color-38);color: var(--bs-color-FF);padding: 0 12px;font-weight: 400;}
.contact_icons span{height: 76px;width: 76px;display: inline-block;}
.esg_canvas .back_btn {margin-bottom: 0;}

/* =============company--& company-details======================== */

/* ---post-modall-screens-------- */
.post_modal{font-family: 'Roboto', sans-serif;}
.post_modal .modal-header{padding: 30px 30px 10px;border: 0;}
.post_modal .modal-header .btn-close{height: 44px;width: 44px;background: var(--bs-color-ED);opacity: 1;color: var(--bs-color-5A);font-size: 14px;display: flex;align-items: center;justify-content: center;border-radius: 50%;padding: 0;}
.post_modal .modal-header h3{font-family: 'Roboto', sans-serif;font-weight: 500;font-size: 24px;line-height: 24px;color: var(--bs-color-00);text-transform: capitalize;}
.post_modal .modal-body{padding: 30px;}
.post_modal .pofile_info a{display: inline-flex;align-items: center;justify-content: center;height: 26px;background-color: var(--bs-color-ED);padding: 0 12px;border-radius: 90px;font-size: 13px;color: var(--bs-color-5A);gap: 4px;margin-top: 5px;}
.post_modal .pofile_info a i{font-size: 14px;}
.post_description{margin-top: 16px;}
.post_description .form-control{height: 220px;padding: 16px 20px;border-radius: 10px;resize: none;font-weight: 400;font-size: 15px;  line-height: 18px;color: var(--bs-color-5A);background-color: var(--bs-color-ED);border-color: transparent;box-shadow: none;}
.post_list{margin-top: 16px;}
.post_btn{margin-top: 16px;}
.post_btn .btn{width: 100%;}
.post_list .post_bttm_menu ul{gap: 5px;}
.post_list .post_bttm_menu ul li{border: 0;}
.post_list .post_bttm_menu ul li a{display: flex;align-items: center;gap: 8px;border: 1px solid var(--bs-color-E3);justify-content: center;border-radius: 90px;padding: 0 10px;height: 44px;}
.gretings_icon{text-align: center;color: var(--bs-color-31);margin-bottom: 15px;}
.gretings_icon span{font-size: 60px;}
.gretings_hdng{text-align: center;margin-bottom: 20px;}
.gretings_hdng h3{font-weight: 500;font-size: 24px;line-height: 24px;color: var(--bs-color-00);}
.gretings_hdng h6{font-size: 16px;color: var(--bs-color-00);margin-top: 30px;}
.post_bnts_outer{display: flex;justify-content: center;gap: 7px;}
.post_bnts_outer .btn{height: 48px;font-size: 14px;display: inline-flex;width: auto;padding: 0 20px;}
.success_greetings_mdl .modal-dialog{max-width: 370px;}
.commnts_description .form-control{border: 0;box-shadow: none;height: 80px;padding: 16px;color: var(--bs-color-5A);font-size: 15px;resize: none;}
.commnts_description{margin-top: 16px;}
.upload_imgbx{position: relative;background-color: var(--bs-color-ED);border-radius: 8px;display: flex;align-items: center;justify-content: center;height: 220px;border: 2px dashed var(--bs-color-AA);}
.post_upload_innr{text-align: center;}
.post_icon{color: var(--bs-color-00);margin-bottom: 7px;}
.post_upload_innr h4{font-weight: 600;font-size: 16px;line-height: 19px;color: var(--bs-color-00);margin-bottom: 6px;}
.post_upload_innr h6{font-weight: 400;font-size: 14px;line-height: 16px;letter-spacing: 0.002em;color: var(--bs-color-5A);}
.post_list .post_bttm_menu ul li.active a{background-color: var(--bs-color-E3);}
.evnt_dateOuter{display: flex;gap: 10px;}
.date_item{width: 100%;}
.date_item .form-control{box-shadow: none;height: 50px;border-radius: 8px;padding: 0 15px;font-size: 15px;color: var(--bs-color-5A);background-color: var(--bs-color-ED);}
.event_calender{margin-top: 16px;}
.job_fill_Outer{display: flex;gap: 16px;}
.job_fill_Outer .input_fill{width: 100%;}
.job_modaldil_total{margin-top: 16px;}

/* ---post-modall-screens-------- */


/* ---my-profile--page-- */

.profile_pnlOuter .pnl_mdlpnl{width: 75%;}
.profile_top_row {display: flex;align-items: center;justify-content: space-between;margin-bottom: 20px;}
.personel_profile_area {display: flex;gap: 30px;}
.prsnl_profile_pic {position: relative;overflow: hidden;height: 120px;width: 120px;border-radius: 50%;background-color: var(--bs-color-EA);}
.prsnl_profile_pic figure img { width: 100%;height: 100%;}
.upload_camera { position: absolute;left: 0; right: 0; bottom: 0;top: 0;text-align: center;display: flex;align-items: center;  justify-content: center; cursor: pointer;gap: 15px;}
.upload_camera a {display: block;position: relative;z-index: 2;transform: scale(0);}
.upload_camera:after {position: absolute;content: "";top: 100%;left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.6); z-index: 1;transition: 0.5s;}
.upload_btn {position: absolute; left: 0; right: 0; top: 0; opacity: 0; cursor: pointer; z-index: 5; height: 100%; width: 100%;}
.upload_camera a span {font-size: 20px;color: var(--bs-color-FF);}
.upload_camera:hover:after {top: 0;}
.upload_camera:hover a {transform: scale(1);}
.profile_owner_outer {display: flex;margin-bottom: 20px;}
.profile_owner_editbtn {margin-left: 15px;}
.profile_owner_editbtn span, .edit_send {height: 40px;width: 40px;box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);  border-radius: 20.5px; display: flex; align-items: center; justify-content: center; color: var(--bs-color-5A);background-color: var(--bs-color-FF);}
.profile_owner_text h2 {font-family: 'Roboto', sans-serif; font-size: 24px; line-height: 30px; color: var(--bs-color-00);font-weight: 500;margin-bottom: 7px;}
.profile_owner_text h5{font-weight: 400;font-size: 15px;line-height: 18px;color: var(--bs-color-5A);}
.edit_input {display: none;}
.editname_bx .edit_input {display: block;}
.input_fill.edit_input .form-control {height: 42px;}
.edit_send{display: none;}
.edit_send span{font-size: 20px;color: var(--bs-color-38);}
.profile_social ul {display: flex;align-items: center; gap: 6px;}
.profile_social ul li a {height: 40px;width: 40px;box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);  border-radius: 8px;background-color: var(--bs-color-FF);display: flex;align-items: center;justify-content: center;}
.refernel_code h4{font-weight: 400;font-size: 15px;line-height: 18px;color: var(--bs-color-00);margin-bottom: 5px;}
.refer_code{background-color: var(--bs-color-E8);height: 30px;padding: 0 8px;border-radius: 90px;font-size: 15px;line-height: 18px;color: var(--bs-color-FF);display: flex;align-items: center;justify-content: center;}
.info_hdng h4{font-weight: 700;font-size: 14px;line-height: 19px;color: var(--bs-color-5A);text-transform: uppercase;} 
.profile_info_pnl .profile_info_row{padding: 15px 0;display: flex;align-items: center;border-bottom: 1px solid var(--bs-color-ED);gap: 10px;}
.profile_info_pnl .profile_info_row:last-child{border-bottom: 0;}
.profile_info_row > label{min-width: 160px;font-weight: 400;font-size: 15px;line-height: 18px;color: var(--bs-color-5A);}
.profile_inner_info{width: 100%;display: flex;align-items: center;justify-content: space-between;gap: 16px;}
.right_edit_icons{min-width: 30px;}
.left_profile_info{width: 100%;}
.left_profile_info .form-control, .left_profile_info > span{font-weight: 400;font-size: 16px;line-height: 150%;color: var(--bs-color-00);border: 0;box-shadow: none;padding: 0;}
.left_profile_info input.form-control{height: 50px;background-color: var(--bs-color-ED);padding: 0 16px;}
.left_profile_info .form-control{display: none;}
.right_edit_icons a{color: var(--bs-color-5A);}
.right_edit_icons a:hover{color: var(--bs-color-38);}
.success_icons span{color: var(--bs-color-31) !important;}
.profile_information {margin-bottom: 20px;}
.profile_bxOuter{display: flex;gap: 10px;margin-bottom: 20px;}
.profile_bx_item {width: 50%;}
.profile_bx_item  > h4{font-weight: 700;font-size: 14px;line-height: 19px;color: var(--bs-color-5A);text-transform: uppercase;margin-bottom: 16px;}
.user_info_row{display: flex;align-items: center;justify-content: space-between;}
.left_user_info{padding-right: 30px;}
.right_user_info span{display: block;height: 75px;width: 75px;min-width: 75px;overflow: hidden;}
.left_user_info p{font-weight: 400;font-size: 16px;line-height: 150%;color: var(--bs-color-00);}
.left_user_info h5{color: var(--bs-color-00);font-weight: 600;font-size: 15px;margin-bottom: 10px;}
.add_btns{margin-top: 30px;}
.portfolio_outer{padding-top: 16px;display: flex;gap: 8px;}
.portfolio_outer .portfolio_pic{position: relative;cursor: pointer;}
.profile_information  .portfolio_outer .portfolio_pic::after{position: absolute;content: "";left: 0;right: 0;top: 0;bottom: 0;background-color: rgba(0,0,0,0.6);transition: 0.5s;transform: scale(0.4);opacity: 0;z-index: 2;}
.full_screen{position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);color: var(--bs-color-FF);z-index: 9;}
.full_screen a, .trash_btn a{color: var(--bs-color-FF);transform: scale(0.2);opacity: 0;}
.trash_btn{position: absolute;right: 10px;top: 10px;z-index: 9;}
.trash_btn a span{font-size: 20px;}
.profile_information  .portfolio_outer .portfolio_pic:hover::after{opacity: 1;transform: scale(1);z-index: 2;}
.profile_information  .portfolio_outer .portfolio_pic:hover .full_screen a, .profile_information  .portfolio_outer .portfolio_pic:hover .trash_btn a{opacity: 1;transform: scale(1);}
.upload_img{margin-top: 20px;}
.blue_text{color: var(--bs-color-38) !important;}
.editname_bx .profile_owner_text h2, .editname_bx .profile_owner_text h5 {display: none;}
.editname_bx .edit_pencil {display: none;}
.editname_bx .edit_send {display: block;}
.add_fiiled .left_profile_info > span{display: none;}
.add_fiiled .left_profile_info .form-control{display: block;}
.add_fiiled  .edit_icons{display: none;}
.add_fiiled  .edit_send{display: flex;}

/* ---my-profile--page-- */


/* -----------course-details--page==-------- */
.course_toppnl{width: 100%;margin-bottom: 30px;}
.course_bluebx{border-radius: 10px;overflow: hidden;display: flex;justify-content: space-between;}
.course_topbx h5{color: var(--bs-color-5A);font-size: 14px;font-weight: 500;margin-bottom: 14px;}
.course_topbx h3{font-weight: 500;font-size: 24px;color: var(--bs-color-00);margin-bottom: 10px;font-family: 'Roboto', sans-serif;}
.event_tags.course_tags li{padding: 0;height: auto;background-color: transparent;}
.event_tags.course_tags li a{padding: 0 12px;display: flex;height: 36px;align-items: center;justify-content: center;}
.intermidate_btn{background: linear-gradient(180deg, #E8604D 0%, #FF961B 100%) !important;border-radius: 90px;color: var(--bs-color-FF) !important;}
.rating_bx{border-radius: 90px;background-color: var(--bs-color-ED);}
.course_duartion ul{display: flex;}
.course_duartion ul li{color: var(--bs-color-5A);font-size: 15px;position: relative;padding-right: 8px;margin-right: 8px;}
.course_duartion ul li:last-child{margin-right: 0;padding-right: 0;}
.course_duartion ul li::after{height: 5px;width: 5px;border-radius: 50%;background-color: var(--bs-color-5A);left: -8px;top: 50%;transform: translateY(-50%);position: absolute;content: "";}
.course_duartion ul li:first-child::after{display: none;}
.course_duartion ul li span{color: var(--bs-color-E8);}
.userexprt_pnltxt{display: flex;align-items: center;gap: 5px;}
.expert_usr{height: 30px;width: 30px;border-radius: 50%;overflow: hidden;}
.exprt_text h5{margin-bottom: 0;color: var(--bs-color-5A);}
.course_topbx .feedback_jointbtnOuter{margin: 16px 0 0;justify-content: flex-start;}
.course_topbx .job_sharebtn a{color: var(--bs-color-5A);border-color: var(--bs-color-5A);}
.subscribe_text{font-size: 15px;color: var(--bs-color-00);}
.subscribe_text label{margin: 0;color: var(--bs-color-5A);}
.course_bttmbx{padding: 16px;}
.duration_outer{display: flex;align-items: center;justify-content: flex-end;gap: 10px;margin-bottom: 20px;}
.course_skill{display: flex;align-items: center;gap: 10px;}
.course_add_btn a{color: var(--bs-color-38);font-size: 14px;height: 30px;border: 1px solid var(--bs-color-E3);padding: 0 10px;min-width: 57px;display: flex;align-items: center;justify-content: center;border-radius: 10px;}
.course_skill ul li a{height: 30px;border-radius: 10px;}

.course_bluebx .feedback_jointbtnOuter{justify-content: flex-end;}

.w_75{width: 75%;}
.inner_pnl_outer{display: flex;flex-wrap: wrap;margin: 0 -10px;}
.inner_leftpnl{width: 70%;padding: 0 10px;}
.inner_rightpnl{width: 30%;padding: 0 10px}
.training_ticklisting p {display: flex;font-size: 15px;color:var(--bs-color-00);line-height: 25px; margin-bottom: 16px;gap: 12px;}
.training_ticklisting p i{color: var(--bs-color-38);}
.course_vdo{height: 460px;}
.course_row {display: flex;padding: 10px 0;gap: 10px;border-bottom: 1px solid var(--bs-color-E3);}
.course_icon {height: 50px;width: 50px;min-width: 50px;display: flex;align-items: center;justify-content: center;border-radius: 50%;background-color: var(--bs-color-4E);color: var(--bs-color-00);}
.course_text h3 {font-size: 16px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 5px;}
.course_text p{font-weight: 400;font-size: 14px;line-height: 24px;color: var(--bs-color-5A);}

.coursl_lesson_total {width: 100%;}
.lessons_part_outer{display: flex;flex-wrap: wrap;}
.lessons_leftpnl{width: 30%;}
.lessons_rightpnl {width: 70%;padding-left: 20px;}
.lesson_rating_outer{margin-bottom: 20px;}
.lesson_rating_row {padding: 20px 16px;cursor: pointer;transition: 0.4s; display: block;background-color: var(--bs-color-FF);border-bottom: 1px solid var(--bs-color-E3);}
.lesson_status {font-size: 14px;color: var(--bs-color-00);}
.lesson_status i{font-size: 18px;}
.success_status i, .success_status span{color: var(--bs-color-31);}
.pending_status i{color: var(--bs-color-E8);}
.active_lessons{background-color: var(--bs-color-ED);}
.lesson_statusOuter {display: flex;align-items: center;justify-content: space-between;margin-bottom: 10px;}
.lesson_time ul li {font-size: 12px;color: var(--bs-color-5A);margin-right: 10px; padding-right: 10px; position: relative;text-transform: capitalize;}
.lesson_time ul li:last-child {margin-right: 0;padding-right: 0;}
.lesson_time ul li + li:after {position: absolute;content: ""; height: 4px; width: 4px; border-radius: 50%;background-color: var(--bs-color-5A);left: -10px;top: 7px;}
.lesson_hdng h3 {font-size: 16px;color: var(--bs-color-00);font-weight: 600;}
.lesson_hdng {margin-bottom: 15px;}
.lesson_time ul {display: flex; align-items: center;}
.rating_status li i {font-size: 16px;}
.rating_status li span {color: var(--bs-color-00);font-size: 15px;}
.lesson_detailsHdng{margin-bottom: 10px;}
.lesson_detailsHdng h2{font-weight: 500;font-size: 24px;line-height: 34px;font-family: 'Roboto', sans-serif;}
.content_pnl .jb_rating{display: inline-flex;}
.course_vdopnl{margin-top: 20px;height: 460px;margin-bottom: 20px;overflow: hidden;box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);border-radius: 10px;}
.contnt_hdng {margin-bottom: 20px;}
.contnt_hdng h4 {font-size: 18px;color: var(--bs-color-00);font-weight: 600;text-transform: capitalize;}
.course_contentBx{margin-bottom: 30px;}
.lessons_check{margin-bottom: 20px;}
.task_row{padding: 16px 0;border-bottom: 1px solid var(--bs-color-E3);display: flex;align-items: center;justify-content: space-between;}
.task_left h3{font-weight: 600;font-size: 18px;line-height: 21px;margin-bottom: 10px;color: var(--bs-color-37);}
.task_left p{font-size: 16px;color: var(--bs-color-5A);line-height: 26px;margin-bottom: 10px;}
.task_left .jb_rating{display: inline-flex;}
.task_right{display: flex;align-items: center;gap: 10px;}
.details_arw_btn a{min-width: 36px;height: 36px;width: 36px;border-radius: 50%;display: flex;align-items: center;justify-content: center;border: 1px solid var(--bs-color-E3);color: var(--bs-color-5A);}
.details_arw_btn a i{font-size: 18px;}
.resume_btn .btn{background-color: var(--bs-color-ED);border-color: transparent;color: var(--bs-color-38);}
.task_row:last-child{border-bottom: 0;}
.success_btn .btn{color: var(--bs-color-31);border-color: var(--bs-color-31);}
.question_submit_area{margin-top: 30px;}
.question_modal_bx .modal-dialog {max-width: 390px;}
.question_modal_bx .modal-body {padding: 20px;text-align: center;}
.sucess_question_icon span {font-size: 46px;color: var(--bs-color-4E);margin-bottom: 20px;}
.question_success_hdng h2 {color: var(--bs-color-00);font-size: 24px;margin-bottom: 15px;}
.question_success_hdng h5 {font-size: 16px; color: var(--bs-color-5A);}
.question_closeBtn {margin: 30px 0 0}
.scorebx_outer {display: flex;gap: 10px;}
.scorebx_item{width: 33.33%;}
.scorebx_innr {background-color: var(--bs-color-ED);padding: 30px 20px;text-align: center;border-radius: 10px;}
.scorebx_innr h5 {font-size: 15px; color: var(--bs-color-00); margin-bottom: 10px;}
.scorebx_innr h4{font-weight: 600;font-size: 16px; line-height: 19px;color: var(--bs-color-38);}
.certificate_bx {margin: 30px 0;overflow-x: auto;}
.donate_certificate {background-size: 100% 100%; background-repeat: no-repeat; background-position: center; text-align: center;padding: 45px 65px;min-width: 800px;}
.certificate_hdng h2 {text-transform: uppercase;font-family: 'EB Garamond', serif; font-size: 40px; color: #E8604D;margin-bottom: 20px;}
.certificate_hdng h2 {text-transform: uppercase;font-family: 'EB Garamond', serif;font-size: 40px;color: #E8604D;margin-bottom: 20px;}
.certificate_nameHdng {margin: 30px 0;}
.certificate_nameHdng p {font-size: 20px;color: #42231a;font-family: 'EB Garamond', serif;}
.certificate_nameHdng h3 {margin: 20px 0 0;font-size: 25px;color: #ed7d40; font-weight: 700;}
.certificate_para p { max-width: 590px; margin: 0 auto;list-style: none; font-size: 20px; color: #42231a; font-family: 'EB Garamond',serif;}
.crtificate_signOuter {display: flex;align-items: center;justify-content: center; margin: 40px 0 0;}
.certificate_date {margin-right: 30px;}
.certificate_stamp { max-width: 60px; margin-right: 30px;}
.certificate_sign { max-width: 150px;}
.task_detailspnl{display: none;}
.task_back a{gap: 5px;margin-bottom: 16px;}
.task_back a i{font-size: 18px;}
.task_status_outer{border: 1px solid var(--bs-color-E3);padding: 20px;border-radius: 10px;gap: 20px;display: flex;align-items: center;justify-content: space-between;margin-bottom: 20px;}
.taskstatus_item h3{font-weight: 500;font-size: 16px;line-height: 19px;color: var(--bs-color-5A);margin-bottom: 10px;}
.taskstatus_item h5, .taskstatus_item h4{font-weight: 500;font-size: 16px;color: var(--bs-color-00);}
.sucess-txt{color: var(--bs-color-31) !important;}
.taskstatus_item .btn{height: 46px;}
.task_detailsHdng > h2{font-family: 'Roboto', sans-serif;font-weight: 500;font-size: 24px;line-height: 32px;color: var(--bs-color-00);margin-bottom: 16px;}
.task_detailsHdng .jb_rating{display: inline-flex;}
.task_detailspara p{font-size: 16px;font-weight: 400;line-height: 26px;color: var(--bs-color-00);margin-bottom: 20px;}
.task_detailspara p:last-child{margin-bottom: 0;}
.task_detailspara p img{width: 100%;}
.tsk_btns{display: flex;gap: 10px;margin: 30px 0 0;}
.task_open .task_detailspnl{display: block;}
.task_open .task_outer{display: none;}
.feedback_modal.post_modal .modal-body{padding-top: 0;}
.rating_hdng h5{font-size: 16px;color: var(--bs-color-00);margin-bottom: 10px;}
.course_rvw_row ul{display: flex;}
.course_rvw_row ul li a {color: var(--bs-color-ED);}
.course_rvw_row ul li a span{font-size: 38px;}
.course_rvw_row ul li.active_star a{color: var(--bs-color-4E);}
.course_rvw_row ul li a:hover, .course_rvw_row ul li a:focus{color: var(--bs-color-4E);}
.feedback_modal .post_description > label{font-size: 16px;color: var(--bs-color-00);margin-bottom: 10px;}
.feedback_modal .post_description .form-control{height: 160px;}
.progress_courseOuter{display: none;}
.course_rvw_row{margin-top: 20px;}
.course_industry_mdl .modal-dialog{max-width: 600px;}
.tags_modalbx .modal-dialog{max-width: 588px;}
.process_modal.tags_modalbx .modal-body{padding-top: 16px;}
.tags_search{position: relative;}
.tags_search .form-control{height: 56px;padding: 0 35px 0 24px;border-radius: 90px;background-color: var(--bs-color-ED);font-size: 16px;color: var(--bs-color-5A);border: 0;box-shadow: none;}
.plus_add{position: absolute;top: 50%;right: 16px;transform: translateY(-50%);color: var(--bs-color-5A);}
.plus_add:hover{color: var(--bs-color-38);}
.tags_list li.active a{background-color: var(--bs-color-F2);}
.tags_list li a:hover{color: var(--bs-color-5A);border-color: var(--bs-color-E3);background-color: transparent;}

/* -----------course-details--page==-------- */


/* my-profile screens */
.fill_input{height: 50px;width: 100%;padding: 0;font-size: 16px;color: var(--bs-color-5A);}
.fill_input::placeholder{color: var(--bs-color-AA);}
.fill_input::-webkit-placeholder{color: var(--bs-color-AA);}
.experience_bxItem .select2-container--default .select2-selection--single{background-color: transparent;padding-left: 0;}
.experience_bxItem .select2-container--default .select2-selection--single .select2-selection__rendered{color: var(--bs-color-AA);font-weight: 400;}
.right_edit_icons .radio-check p{font-size: 14px;color: var(--bs-color-5A);font-weight:400;min-width: 150px;}
.info_hdng {display: flex;justify-content: space-between;align-items: center;}
.info_mainhdng{margin-bottom: 24px;}
.info_mainhdng h2{font-family: 'Roboto', sans-serif;font-size: 30px;color: var(--bs-color-00);font-weight: 600;}
.add_anotherBtn{display: inline-flex;align-items: center;gap: 10px;font-size: 16px;color: var(--bs-color-38);font-weight: 500;}
.add_anotherBtn i{font-size: 18px;}
.delete_bxbtn{color: var(--bs-color-5A);}
.delete_bxbtn:hover{color: var(--bs-color-E8);}
.experience_bxItem{margin-bottom: 30px;}
.experience_bxItem:last-child{margin-bottom: 0;}
.profile_info_row.description_row{align-items: flex-start;}
.description_row textarea{height: 85px;resize: none;}
.password_btnOuter{display: flex;align-items: center;justify-content: space-between;margin-top: 20px;}
.password_btn .btn{height: 46px;gap: 8px;}
.password_btn .btn i{font-size: 18px;}
.forgot_mdl .modal-header h3 span{display: block;font-weight: 400;font-size: 14px;line-height: 24px;color: var(--bs-color-5A);margin-top: 10px;}
.forgot_mdl.post_modal .modal-header{align-items: flex-start;}
.forgot_mdl.post_modal .modal-header{align-items: flex-start;}
.forgot_mdl .post_description .form-control{height: 50px;}
.forgot_mdl.post_modal .modal-dialog{max-width: 443px;}
.forgot_mdl .post_description {margin-top: 0;}
.forgot_mdl .post_description label{margin-bottom: 10px;}
.forgot_mdl .modal-body {padding: 16px 30px 30px;}
.email_verify{margin-bottom: 30px;}
.email_verify{display: inline-flex;align-items: center;font-size: 15px;color: var(--bs-color-38);gap: 5px;}
.email_verify a{display: inline-block;color: var(--bs-color-5A);}
.forgot_mdl .resend_btn a{font-size: 16px;color: var(--bs-color-38);font-weight: 600;}
.account_user_outer{display: flex;align-items: center;gap: 20px;}
.account_usr{height: 80px;width: 80px;border-radius: 50%;overflow: hidden;min-width: 80px;}
.account_usr_info h4{font-weight: 400;font-size: 18px;line-height: 21px;color: var(--bs-color-00);margin-bottom: 10px;}
.account_usr_info h4 strong{font-weight: 600;}
.account_usr_info p{color: var(--bs-color-5A);font-size: 15px;}
.account_row > label{min-width: 240px;}
.password_btn{display: flex;gap: 10px;}
.btn.fill_btn{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.left_profile_info ul{gap: 30px;}
.privacy_row > label{min-width: 300px;}
.left_profile_info .radio-check input[type='checkbox'] + span{height: 15px;width: 15px;min-width: 15px;}
.left_profile_info .radio-check p{font-size: 14px;color: var(--bs-color-5A);}
.left_profile_info .radio-check input[type=checkbox]:checked+span:after{background-size: 7px;}

/* ---end-my-profile-pages---- */

/* -blog--start-- */
.blog_landing_total{padding: 230px 0 100px;}
.landing_hdng_row{display: flex;align-items: center;justify-content: space-between;margin-bottom: 40px;}
.landing_hdng_row .page_hdng{margin-bottom: 0;}
.details_arw a{height: 60px;width: 60px;display: flex;align-items: center;justify-content: center;background-color: var(--bs-color-D3);border-radius: 50%;color: var(--bs-color-00);}
.blog_pic a{max-height: 450px;overflow: hidden;display: block;height: 450px;border-radius: 40px;margin-bottom: 16px;}
.blog_content h5{font-size: 16px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 10px;}
.blog_content a{display: inline-block;font-size: 22px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 16px;}
.blog_content a:hover{color: var(--bs-color-38);}
.blog_content p{font-size: 16px;color: var(--bs-color-00);line-height: 26px;margin-bottom: 20px;}
.bttm_step_cloude{display: flex;align-items: center;gap: 16px;}
.cloud_icon{height: 50px;width: 50px;min-width: 50px; display: flex;align-items: center;justify-content: center;border-radius: 50%;color: var(--bs-color-FF);background-color: var(--bs-color-5A);overflow: hidden;}
.cloud_icon span{display: block;height: 100%;width: 100%;}
.cloud_icon img{height: 100%;width: 100%;object-fit: cover;}
.cloud_icon_text h4{font-size: 16px;color: var(--bs-color-00);font-weight: 600;text-transform: uppercase;line-height: 28px;}
.cloud_icon_text h6{font-size: 16px;color: var(--bs-color-00);}
.cloud_icon_text h6 span{display: inline-block;padding-left: 8px;margin-left: 8px;position: relative;}
.cloud_icon_text h6 span::after{position: absolute;content: "";height: 4px;width: 4px;background-color: var(--bs-color-00);border-radius: 100px;left: -4px;top: 50%;transform: translateY(-50%);}
.landing_col_inner{margin-bottom: 80px;}
.landing_row{margin-bottom: 30px;}
.landing_row:last-child{margin-bottom: 0;}
.blog_pic img{width: 100%;height: 100%;object-fit: cover;}
.landing_hdng_row .page_hdng{display: flex;align-items: center;gap: 16px;}
.green_back a{background-color: var(--bs-color-4E);}
.blog_listing .col-lg-4{margin-bottom: 80px;}
.news_date_info .cloud_icon_text{display: flex;align-items: center;gap: 16px;}
.news_date_info{display: flex;align-items: center;margin-bottom: 24px;}
.news_date_info > label{font-weight: 600;font-size: 18px;line-height: 32px;color: var(--bs-color-00);padding-right: 16px;margin-right: 16px;margin-bottom: 0;border-right: 1px solid rgba(0,0,0,0.2);}
.details_pic{margin-bottom: 30px;}
.content_item h4{font-weight: 600;font-size: 22px;line-height: 32px;margin-bottom: 16px;}
.details_para p{font-size: 16px;color: var(--bs-color-00);line-height: 26px;margin-bottom: 30px;}
.details_para p:last-child{margin-bottom: 0;}
.content_item{margin-bottom: 40px;}
.content_item:last-child{margin-bottom: 0;}
.details_para ul{padding: 35px 25px;border: 1px solid var(--bs-color-D3);border-radius: 30px;}
.details_para ul li{display: flex;gap: 16px;font-size: 16px;color: var(--bs-color-00);line-height: 26px;margin-bottom: 30px;}
.details_para ul li i{height: 32px;width: 32px;font-style: normal;color: var(--bs-color-FF);font-weight: 600;min-width: 32px;background-color: var(--bs-color-5A);border-radius: 50%;display: flex;align-items: center;justify-content: center;}
.details_para ul li:last-child{margin-bottom: 0;}
.details_back{margin-top: 40px;}
.details_back a{display: inline-flex;align-items: center;justify-content: center;padding: 0 25px;height: 50px;color: var(--bs-color-00);font-size: 18px;border-radius: 90px;background-color: var(--bs-color-4E);font-weight: 600;}
.news_slider_sec{padding: 0 0 100px;}
.news_slider{margin: 0 -12px;}
.news_slideItem {padding: 0 12px;}
.news_slide_outer{position: relative;}
.inBtns.news_arws {left: inherit;right: 0;bottom: inherit;  top: -115px;position: absolute;}
.inBtns.news_arws .slick-arrow{opacity: 1;}
.story_textpnl{padding-left: 100px;}
.story_textpnl .page_hdng {margin-bottom: 40px;}
.news_date_info .bttm_step_cloude .cloud_icon{background-color: var(--bs-color-38);}
/* -blog--start-- */


/* digital--kits */

.kit_para{padding-left: 50px;}
.kit_para p{font-size: 20px;font-weight: 400; line-height: 32px;margin-bottom: 30px;}
.kit_para p:last-child{margin-bottom: 0;}
.digital_price_outer{margin: 80px 0 0;}
.digital_pricebx{background-color: var(--bs-color-ED);padding: 32px;border-radius: 24px;}
.price_icon{height: 83px;width: 83px;border-radius: 50%;display: flex;align-items: center;justify-content: center;background-color: var(--bs-color-4E);margin-bottom: 24px;}
.price_icon span{font-size: 40px;}
.price_text_area h6{font-size: 20px;font-weight: 400; line-height: 160%;}
.price_text_area h2{font-size: 60px;font-weight: 400; line-height: 100%;  text-transform: uppercase;margin-bottom: 24px;}
.price_text_area p{min-height: 64px;font-size: 18px;font-weight: 500;line-height: 32px; margin-bottom: 30px;}
.price_text_area a{display: inline-flex;align-items:  center;justify-content: center;background-color: var(--bs-color-FF);height: 43px;padding: 0 20px;border-radius: 40px;font-size: 16px;color: var(--bs-color-00);font-weight: 600;}
.blue_ecosystem_sec .page_hdng h5{margin-top: 16px;}
.kit_icon_outer .help_item{margin-bottom: 0;padding: 32px 24px;border: 1px solid rgba(0, 0, 0, 0.2);padding: 32px 24px;border-radius: 24px;height: 100%;}
.kit_icon_outer .col-lg-4{margin-bottom: 24px;}
.digital_srvc_row{background-color: var(--bs-color-ED);padding: 32px 45px;border-radius: 24px;gap: 80px;margin-bottom: 32px;}
.digital_srvc_row:last-child{margin-bottom: 0;}
.kit_srvc_leftpnl{width: 45%;}
.kit_srvc_rightpnl{width: 55%;}
.kit_srvc_text{margin-bottom: 30px;}
.kit_srvc_text h3{font-size: 30px;font-weight: 600;line-height: 133%;margin-bottom: 16px;}
.kit_srvc_text p{font-size: 16px;font-weight: 400;margin-bottom: 24px;}
.kit_srvc_rightpnl li{display: flex;font-size: 18px;font-weight: 500;line-height: 28px;gap: 16px;margin-bottom: 16px;}
.kit_srvc_rightpnl li i, .kit_srvc_rightpnl li span{display: inline-block;}
.kit_srvc_rightpnl li i{color: var(--bs-color-31);margin-top: 4px;}
.kit_srvc_rightpnl li:last-child{margin-bottom: 0;}

/* digital--kits */


/* --success-stories-- */

.banner_area.seccess_bnr{padding-bottom: 300px;}
.success_testimonial_bx{padding: 64px;background-color: var(--bs-color-ED);border-radius: 30px;margin-top: -200px;}
.success_quote_outer{display: flex;align-items: center;}
.quote_leftpnl{width: 60%;}
.quote_rightpnl{width: 40%;text-align: center;}
.slick-slide .quote_rightpnl img{display: inline-block;}
.quote_leftpnl > span{display: block;margin-bottom: 24px;}
.quote_leftpnl h4{font-size: 16px;font-weight: 600;line-height: normal;margin-bottom: 24px;}
.quote_leftpnl p{font-size: 26px;font-weight: 600;line-height: 38px;}
.quote_author{margin-top: 48px;}
.quote_author h3{font-size: 16px;font-weight: 600;line-height: normal;margin-bottom: 5px;}
.quote_author h5{font-size: 15px;}
.success_testimonial_bx .slick-dots{display: flex;align-items: center;list-style: none;gap: 10px;margin-top: 64px;}
.success_testimonial_bx .slick-dots li button{font-size: 0;background-color: transparent;height: 10px;width: 10px;border: 1px solid var(--bs-color-00);border-radius: 50%;}
.success_testimonial_bx .slick-dots li.slick-active button{background-color: var(--bs-color-00);}
.rewards_outer{padding: 140px 0 0;}
.succes_rewards_sec{padding-bottom: 90px;}
.rewards_point_item{text-align: center;}
.rewards_point_item h2{font-size: 90px;color: var(--bs-color-38);font-weight: 400;line-height: 80px;margin-bottom: 12px;}
.rewards_point_item p{font-size: 20px;line-height: 32px;}
.success_stories{padding: 90px 0 140px;}
.success_item{background-color: var(--bs-color-ED);padding: 40px 32px;border-radius: 24px;}
.success_item h5{font-size: 14px;margin-bottom: 20px;}
.success_logo{display: inline-flex;margin-bottom: 20px;height: 38px;align-items: center;}
.success_item p{font-size: 30px;line-height: 40px;font-weight: 600;margin-bottom: 30px;}
.success_logo img{max-width: 75%;}
.sucess_outer .col-lg-4{margin-bottom: 24px;}

/* --success-stories-- */

/* --success-stories-details-- */
.success_stories_details{padding: 144px 0 100px;}
.success_list_bx{margin-top: 60px;}
.success_list_bx > h3{color: var(--bs-color-00);font-size: 26px;font-weight: 600; line-height: 38px;margin-bottom: 16px;}
.success_list{padding: 40px 35px;border-radius: 30px;border: 1px solid var(--bs-color-D3);}
.success_list ol{padding-left: 15px;}
.success_list ol li{font-size: 16px;color: var(--bs-color-00);margin-bottom: 16px;}
.success_list ol li:last-child{margin-bottom: 0;}
.rvw_rating li i{color: var(--bs-color-AA);}
.rvw_rating li.active i{background: linear-gradient(180deg, #E8604D 0%, #FF961B 100%);-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;}
.testimonial_slideouter .rvw_para {margin-bottom: 15px;}
.rvw_rating{margin-bottom: 15px;}


/* --success-stories-details-- */

/* --digital-kit-modal-- */
.digital_modal .modal-header{padding: 0;border: 0;}
.digital_modal .modal-body{display: flex;flex-wrap: wrap;padding: 0;}
.digital_left{width: 40%;background-color: var(--bs-color-38);padding: 48px 32px;background-position: bottom;background-size: cover;background-repeat: no-repeat;}
.digital_right{width: 60%;}
.digital_modal .modal-dialog{max-width: 1296px;padding: 0 10px;}
.digital_modal .close{height: 40px;width: 40px;display: flex;align-items: center;justify-content: center;position: absolute;right: 10px;top: 10px;background-color: var(--bs-color-ED);border-radius: 50%;z-index: 99;}
.digital_left h4{font-size: 30px;line-height: 40px;color: var(--bs-color-FF);font-weight: 600;margin-bottom: 24px;}
.digital_left h5{font-size: 20px;line-height: 32px;color: var(--bs-color-FF);}
.digital_left h2{font-size: 60px;color: var(--bs-color-FF);line-height: 100%;}
.digital_right{padding: 68px 32px 48px;}
.digital_right .row{margin: 0 -6px;}
.digital_right .col-lg-12, .digital_right .col-lg-6{padding: 0 6px;}
.digital_right .input_fill .form-control{background-color: var(--bs-color-ED);border-radius: 16px;color: var(--bs-color-5A);font-size: 18px;box-shadow: none;height: 60px;border-color: transparent;padding: 0 20px;}
.digital_right .input_fill{margin-bottom: 16px !important;}
.digital_right .input_fill.description_fill .form-control{height: 180px;resize: none;padding: 20px;}
.digital_modal .modal-content{border-radius: 24px;overflow: hidden;}
.digital_success_modal .modal-content{background-color: var(--bs-color-ED);border-radius: 24px;}
.digital_success_modal .modal-body{padding: 48px 64px;text-align: center;}
.kit_rewards_icon{margin-bottom: 24px;}
.kit_success h2{text-transform: uppercase;font-size: 60px;text-transform: uppercase;line-height: 60px;margin-bottom: 24px;}
.kit_success p{font-size: 20px;color: var(--bs-color-00);line-height: 32px;margin-bottom: 24px;}
/* --digital-kit-modal-- */
/* ---start-sucess-share---- */
.sucess_share_bnr{position: relative;}
.sucess_share_bnr::after{position: absolute;content: "";left: 0;right: 0;top: 0;bottom: 0;background-color: rgba(56, 76, 255, 0.8);z-index: 1;}
.banner_outer{position: relative;z-index: 2;}
.page_hdng p{font-size: 18px;margin: 15px 0 0;}
.benefits_outer .circle_icon{background-color: var(--bs-color-4E);}
.benefits_outer .row{justify-content: center;}
.process_work_sec{background-color: var(--bs-color-ED);}
.blue_ecosystem_sec.join_part_sec{background-color: var(--bs-color-4E);}
.join_part_sec .system_btn .btn{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.ofr_textpnl{position: relative;}
.number_count{position: absolute;left: 20px;top: -30px;pointer-events: none;height: 200px;width: 200px;}
.ofr_row:nth-child(even) .col-lg-6 .ofr_textpnl .number_count{left: -30px;}
.success_faq {border-bottom: 1px solid var(--bs-color-D6);}
.success_faq_rightpnl .accordion-button::after{display: none;}
.success_faq_rightpnl .accordion-header{font-family: 'Inter', sans-serif;}
.success_faq_rightpnl .accordion-button{display: flex;align-items: center;justify-content: space-between;padding: 20px 0;box-shadow: none;}
.success_faq_rightpnl .accordion-button.collapsed .minus{display: none;}
.success_faq_rightpnl .accordion-button.collapsed .plus{display: block;}
.success_faq_rightpnl .accordion-button .minus{display: block;}
.success_faq_rightpnl .accordion-button .plus{display: none;}
.success_faq_rightpnl .accordion-button > span{font-size: 20px;font-weight: 600;}
.success_faq_rightpnl .accordion-body{padding: 0 0 30px;}
.success_faq_rightpnl .accordion-body p{font-size: 16px;}
/* ---end-sucess-share---- */

/* --process-step--area--- */
.starting_signup_process{padding: 75px 0;font-family: 'Roboto', sans-serif;}
.medium_container{max-width: 460px;margin: 0 auto;width: 100%;}
.large_container{max-width: 800px;margin: 0 auto;width: 100%;}
.process_innr{height: calc(100vh - 201px);display: flex;align-items: center;overflow-y: auto;justify-content: center;}
.lobees_logo{padding: 15px 0;}
.process_hdng{margin-bottom: 32px;}
.process_hdng h3{font-size: 30px;font-weight: 600; line-height: 40px;margin-bottom: 16px;}
.process_hdng p{font-size: 16px;color: var(--bs-color-5A);line-height: 150%;}
.process_type_bx{text-align: center;border: 1px solid var(--bs-color-E3);padding: 16px 16px 24px 16px;border-radius: 10px;width: 33.33%;}
.type_icon{height: 133px;width: 133px;margin: 0 auto 6px;}
.type_process_text h4{font-size: 16px;font-weight: 600;margin-bottom: 8px;}
.type_process_text p{font-size: 14px;color: var(--bs-color-5A);margin-bottom: 20px;}
.type_process_text .radio-check input[type='checkbox'] + span{margin: 0 auto;}
.type_process_text .radio-check input[type=checkbox]:checked+span{background-color: var(--bs-color-38);border-color: var(--bs-color-38);}
.type_process_text .radio-check input[type=checkbox]:checked+span::after{background: url(../images/tick.svg) no-repeat}
.process_innr .btn{height: 48px;}

.process_modal .close {background-color: var(--bs-color-F1); color: var(--bs-color-00); font-size: 17px;position: absolute;right: 15px;  top: 15px;height: 34px; width: 34px;border-radius: 50%;display: flex; align-items: center; justify-content: center;z-index: 99;}
.process_modal .close i{font-size: 20px;}
.process_modal .modal-content{border-radius: 20px;}
.process_modal .modal-header{border: 0;}
.process_modal .modal-body{padding: 30px;}
.form_innr .form-group{margin-bottom: 24px;}
.form_innr .form-group label{font-size: 16px;color: var(--bs-color-00);margin-bottom: 8px;font-weight: 600;}
.form_innr .form-group .select2-container--default .select2-selection--single{border-radius: 10px;}
.form_innr .form-group .form-control{height: 50px;box-shadow: none;border-radius: 10px;background-color: var(--bs-color-ED);color: var(--bs-color-5A);border-color: transparent;}
.form_innr .form-group .radio-check{gap: 8px;}
.form_innr .form-group .radio-check p{font-size: 14px;font-weight: 400;color: var(--bs-color-5A);}
.experice_row{border: 1px solid var(--bs-color-E3);padding: 16px;border-radius: 16px;}
.experice_row .dates_nmbr{left: inherit;position: static;height: 47px;width: 47px;min-width: 47px;border-color: var(--bs-color-E3);}
.adress_tags{display: inline-flex;align-items: center;height: 30px;padding: 0 8px;font-size: 14px;color: var(--bs-color-5A);background-color: var(--bs-color-ED);border-radius: 90px;}
.experice_row{margin-bottom: 8px;}
.experice_row:last-child{margin-bottom: 0;}
/* ---multi-select-css--- */
.multiple_slct_bx{background-color: var(--bs-color-ED);height: 206px;border-radius: 10px;}
.multiple_slct_bx .select2-container--default .select2-selection--multiple{background-color: var(--bs-color-ED);border: 1px solid transparent;border-radius: 10px;padding: 16px;font-size: 15px;color: var(--bs-color-5A);}
.multiple_slct_bx .select2-container .select2-search--inline .select2-search__field{color: var(--bs-color-AA);}
.multiple_slct_bx .select2-container--default .select2-selection--multiple .select2-selection__choice{background-color: var(--bs-color-FF);display: inline-flex;align-items: center;padding: 7px 30px 7px 8px;border: 0;}
.multiple_slct_bx .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {background-color: transparent;border: none;color: var(--bs-color-75);cursor: pointer; font-size: 24px; font-weight: normal;padding: 0; left: inherit;top: 50%; right: 8px;transform: translateY(-50%);}
.multiple_slct_bx .select2-container--default .select2-selection--multiple .select2-selection__clear{display: none;}
/* ---multi-select-css--- */

.option_btns a{height: 42px;border: 1px solid var(--bs-color-E3);padding: 0 16px;font-size: 15px;color: var(--bs-color-5A);border-radius: 90px;}
.option_btns a:hover{background-color: var(--bs-color-4E);border-color: var(--bs-color-4E);color: var(--bs-color-00);}
.process_upload{position: relative;height: 135px;border: 2px dashed rgba(0,0,0,0.2);border-radius: 8px;display: flex;align-items: center;justify-content: center;}
.process_upload_text span{display: block;color: var(--bs-color-D3);font-size: 30px;margin-bottom: 10px;}
.process_upload_text h3{font-size: 14px;color: var(--bs-color-00);font-weight: 600;margin-bottom: 10px;}
.process_upload_text h5{font-size: 12px;}
.w_60{width: 60%;}
.w_40{width: 40%;}
.map_pic img{width: 100%;}
.map_fill{position: relative;}
.map_pointer{position: absolute;right: 15px;top: 50%;transform: translateY(-50%);color: var(--bs-color-5A);}
.friend_results_bx {height: calc(100vh - 400px);overflow-y: auto;padding: 10px;}
.friends_result_innr{box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10), 0px 0px 1px 0px rgba(0, 0, 0, 0.25);background-color: var(--bs-color-FF);border-radius: 20px;padding: 20px;}
.friends_result_innr .people_row{padding: 16px 0;border-bottom: 1px solid var(--bs-color-D3)}
.friends_result_innr .people_row:last-child{border-bottom: 0;}
.people_hdng h3{font-size: 16px;color: var(--bs-color-00);font-weight: 500;margin-bottom: 12px;}
.friends_result_innr .right_people_btn .btn{height: 36px;}
/* .h-100{overflow-y: auto;display: flex;align-items: center;justify-content: center;width: 100%;} */

/* --process-step--area--- */

.empty_vh{height: 100vh;}
.empty_text h2 span{color: var(--bs-color-E8)}

/* ---cv--builder---- */
.build_innr_row{border-bottom: 1px solid var(--bs-color-DC);}
.build_usrpic {height: 80px;width: 80px;min-width: 80px;border-radius: 8px;cursor: pointer;}
.build_usrpic .upload_camera{color: var(--bs-color-FF);opacity: 0;transition: 0.4s;}
.build_usrpic:hover .upload_camera{color: var(--bs-color-FF);opacity: 1;}
.build_usrpic:hover .upload_camera::after{border-radius: 8px;}
.build_usrpic .upload_camera span{position: relative;z-index: 2;}
.build_usrpic .file_btn{z-index: 9;}
.build_usrinfo h3{font-size: 30px;font-weight: 600;line-height: 40px;}
.build_usrinfo h3 span{font-size: 14px;font-weight: 500;color: var(--bs-color-5A);letter-spacing: -0.042px;line-height: 100%;}
.build_usrinfo ul li, .build_usrinfo h5{font-size: 14px;color: var(--bs-color-5A);}
.build_usrinfo ul li i{font-size: 18px;}
.build_usrinfo h5 strong{font-weight: 400;color: var(--bs-color-00);}
.build_edit_icon a{color: var(--bs-color-5A);}
.build_edit_icon a:hover{color: var(--bs-color-38);}
.build_hdng  h3{font-size: 18px;color: var(--bs-color-00);font-weight: 600;}
.build_hdng i{color: var(--bs-color-D3);}
.exp_logo{min-width: 47px;width: 47px;height: 47px;}
.exp_logo_details h4{font-size: 16px;font-weight: 600;line-height: 26px;}
.exp_logo_details h4 span{font-weight: 400;}
.exp_logo_details h5{font-size: 14px;color: var(--bs-color-5A);margin-bottom: 10px;}
.build_para p{color: var(--bs-color-5A);font-size: 15px;line-height: 25px;margin-bottom: 20px;}
.build_para p:last-child{margin-bottom: 0;}
.language_filed li a{color: var(--bs-color-00);}
.language_filed li a span{color: var(--bs-color-5A);}
.gray_bx_item{width: 25%;padding: 12px;background-color: var(--bs-color-F2);border-radius: 8px;}
.gray_bx_item h4{font-size: 14px;color: var(--bs-color-5A);}
.gray_bx_item h3{color: var(--bs-color-00);font-size: 15px;}
.event_tags.portfolio_links a{color: var(--bs-color-38);}
.logo_summery_top .ai_logo{height: 42px;width: 42px;min-width: 42px;}
.ai_info_top h3{font-size: 18px;font-weight: 600;}.ai_info_top h5{font-size: 14px;color: var(--bs-color-5A);}.ai_info_top h6{font-size: 14px;color: var(--bs-color-38);}
.summery_bx, .skill_bx{background-color: var(--bs-color-F2);border-radius: 16px;}
.summery_bx h3, .skill_bx h3{font-weight: 600;font-size: 16px;}
.summery_bx  p{color: var(--bs-color-5A);}
.summery_bx .gray_btn{background-color: var(--bs-color-DC);border-color: var(--bs-color-DC);}
.skill_bx .btn{height: 36px;padding: 0 12px;font-size: 14px;color: var(--bs-color-5A);font-weight: 400;}
.skill_bx .btn i{font-size: 16px;}
.process_modal.builder_modal .modal-header {padding: 30px 30px 0;}
.adnew_btn a{height: 36px;border-radius: 90px;padding: 0 16px;font-size: 14px;color: var(--bs-color-5A);background-color: var(--bs-color-F2);}
.adnew_btn a i{font-size: 16px;}

.phone_filed_outer .w-30{min-width: 134px;}
.form-group .form-control.srch_filed{background-image: url(../images/search.svg);background-repeat: no-repeat;background-position: left 10px top 50%;padding-left: 35px;}
.form_innr .form-group.summery_textbx textarea.form-control{height: 220px;}
.form-group .builder-radio-list li .radio-check input[type=radio]+span{margin: 0;border: 1px solid var(--bs-color-5A);}
.form-group .builder-radio-list{gap: 30px;}
.form-group .builder-radio-list .radio-check input[type=radio]:checked+span:after{background-color: var(--bs-color-38);}
.form-group .builder-radio-list .radio-check input[type=radio]:checked+span{border-color: var(--bs-color-38);}
.form-group .builder-radio-list .radio-check input[type=radio]:checked+span+p{color: var(--bs-color-38);}
.previous_cvbx > h3{font-size: 18px;color: var(--bs-color-00);font-weight: 600;}
.cv_row {background-color: var(--bs-color-F2);border-radius: 16px;margin-bottom: 5px;}
.cv_row:last-child{margin-bottom: 0;}
.cv_left_text h4{font-size: 16px;font-weight: 600;margin-bottom: 5px}
.cv_left_text h5{font-weight: 400;font-size: 15px;color: var(--bs-color-5A);}
.cv_more_btn a{color: var(--bs-color-5A);}
.portfolio_links li a{max-width: 290px;overflow: hidden;}
.portfolio_links li a span{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;display: block;}
.text-green{color: #31A61E !important;}
.tick_icon{min-width: 36px;}
.applied_nfo h4{font-size: 14px;color: var(--bs-color-00);font-weight: 400;}
.applied_nfo h4 span{color: var(--bs-color-75);}
.expand_arw{height: 38px;width: 38px;min-width: 38px;background-color: var(--bs-color-F2);}
/* ---------my-meeting--page--- */
.fw-medium{font-weight: 500;} .fw-semibold{font-weight: 600;}.fw-bold{font-weight: 700;}.fw-regular{font-weight: 400 !important;}
.fs-sm{font-size: 14px;} .fs-md{font-size: 16px;} .fs-lg{font-size: 20px !important;} .fs-xl{font-size: 24px;}.fs-xs{font-size: 12px;}
.text-blue{color: var(--bs-color-38);}
.text-gray{color: var(--bs-color-75);}
.text-black-gray{color: var(--bs-color-5A);}
.bg-light{background-color: var(--bs-color-F2) !important;}
.text-white{color: var(--bs-color-FF);}
.text-light{color: var(--bs-light-gray) !important;}
.rounded-4{border-radius: 4px;}.rounded-90{border-radius: 90px;}
.bg-green{background-color: var(--bs-color-31);}
.bg-red{background-color: var(--bs-color-E8);}
.meeting_left, .meeting_right{width: 27%;}
.meeting_mdl{width: 46%;}
.meeting_usr{height: 120px;width: 120px;min-width: 120px;}
.meeting_item + .meeting_item::after{position: absolute;content: "";height: 109px;width: 1px;background-color: var(--bs-color-DD);left: 0;top: 50%;transform: translateY(-50%);}
.filter_right{min-width: 250px;} .filter_select{min-width: 180px;}
.filter_select .select2-container--default .select2-selection--single{border-radius: 90px !important;}
.filter_btn a{padding: 12px 18px;border-radius: 90px;color: var(--bs-color-37);background-color: var(--bs-color-F2);border: 1px solid var(--bs-color-DC);}
.offcanvas_sidebar .offcanvas{max-width: 400px;width: 100%;}
.meet_btns .btn{height: 48px;}
.feedback_innr_pnl .radio-check input[type=radio]+span{border-color: var(--bs-color-75);}
.feedback_innr_pnl .radio-check input[type=radio]:checked+span:after{background-color: var(--bs-color-38);}
.feedback_innr_pnl .radio-check input[type=radio]:checked+span{border-color: var(--bs-color-38);}
.feedback_innr_pnl .input_fill .form-control{background-color: var(--bs-color-F2);}
.feedback_innr_pnl .rating_star .material-icons{font-size: 32px;} 
.feedback_innr_pnl .rating_star li.active a{background: var(--bs-color-38);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.build_innr_row:last-child{border-bottom: 0;}
.doc_item{border-radius: 14px;max-width: 262px;}
.pdf_info i{display: block;height: 38px;width: 38px;min-width: 38px;}
.pdf_text h5{white-space: nowrap;overflow: hidden;text-overflow: ellipsis;max-width: 130px;}
.survey_rating .text-gray{opacity: 0.2;}
.rounded-10{border-radius: 10px;}.rounded-16{border-radius: 16px;}
.feed_usr{height: 34px;width: 34px;min-width: 34px;background-color: var(--bs-color-D3);}
.feed_history{border-top: 1px solid var(--bs-color-D3);}
.form_innr .form-group:last-child{margin-bottom: 0;}

.ai_msgbx{position: absolute;left: 0;right: 0;bottom: 16px;padding: 0 20px;}
.details_ailogo a i{height: 24px;width: 24px;min-width: 24px;}
.ai_fill .form-control, .ai_send{background-color: transparent !important;color: var(--bs-color-5A);}
.ai_fill span{height: 30px;width: 30px;min-width: 30px;}
.details_ai_fill{max-width: 365px;}
.details_ai_fill {display: none;}
.download_btn .btn{height: 42px;font-size: 14px;padding: 0 15px;}
/* ---------------------- */

/* ---feedback--canvas--- */
.bg-gray-light{background-color: var(--bs-light-gray);}
.bg-38{background-color: var(--bs-color-38);}
.user_pic{height: 24px;width: 24px;min-width: 24px;}
.feed_offcanvas .close-btn{background-color: transparent;}
.canvas_feeback_row{border-bottom: 1px solid var(--bs-light-gray);}
.canvas_feeback_row:last-child{border-bottom: 0;}
.feed_upload .doc_item{max-width: 100%;}
.feed_upload .pdf_info i{height: 20px;width: 20px;min-width: 20px;vertical-align: middle;}
.feed_upload .pdf_info i img{width: 100%;height: 100%;}
.feed_upload .pdf_text h5{max-width: 100%;}
.ordering_app .radio-check input[type=checkbox]:checked+span:after{background-image: url(../images/tick.svg);background-size: 10px;}
.ordering_app .radio-check input[type=checkbox]:checked+span{border-color: var(--bs-color-38);background-color: var(--bs-color-38);}
.ordering_app .radio-check input[type=radio]:checked+span:after{background-color: var(--bs-color-38);}
.ordering_app .radio-check input[type=radio]+span{border-color: var(--bs-color-5A);}
.ordering_app .radio-check input[type=radio]:checked+span{border-color: var(--bs-color-38);}
.form_innr .process_upload{height: 250px;}
.feededit_modal .rating_star .material-icons{font-size: 40px;}
.selected a{background-color: var(--bs-color-38) !important;color: var(--bs-color-FF) !important;}

.blank-img-inr{height: calc(100vh - 270px);overflow-y: auto;}
.upload_cvbx .profile_upload_bx {height: 600px;border-color: var(--bs-light-gray);}
.upload_cvbx .upload_icon span{font-size: 40px;}
.upload_cvbx .upload_txt h4{font-size: 15px;}
.upload_cvbx .upload_txt h5{font-size: 14px;}
.batch_btns .btn{height: 40px;padding: 0 12px;}

.batch_top_row{box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);background-color: var(--bs-color-FF);margin-bottom: 30px;padding: 10px 15px;}

.load-icon span {display: inline-block;background: var(--bs-color-00);width: 4px;height: 4px;border-radius: 100px;}
.load-icon span:nth-of-type(1) {left: 0;}
.load-icon span:nth-of-type(2) {left: 10%;}
.load-icon span:nth-of-type(3) {left: 12%;}

/* Animation */
.load-icon span {animation-name: loading;animation-duration: 1.5s;animation-iteration-count: infinite;animation-fill-mode: both;}
.load-icon span:nth-of-type(2) {animation-delay: .2s;}
.load-icon span:nth-of-type(3) {animation-delay: .4s;}

/* Keyframes */
@keyframes loading {
	0% { opacity: 1; }
	50% { opacity: 0; }
	100% { opacity: 1; }
}

.loading_icon span{font-size: 42px;color: var(--bs-color-38);}
.upload_progress_outer{background-color: var(--bs-light-gray);height: 8px;border-radius: 90px;max-width: 311px;margin: 0 auto;position: relative;}
.upload_progress_outer .progress_bar{background-color: var(--bs-color-38);left: 0;top: 0;bottom: 0;}


/* ----------survey--fedback--page------ */

.feedback_header{padding: 10px 0;}
.feedback_header .btn{height: 34px;}
.print_usr{height: 34px;width: 34px;min-width: 34px;}
.feed_share_btn .btn{width: 34px;min-width: 34px;}
.feed_share_btn .btn .fs-sm{font-size: 14px;}
.feedback_pnl_sec{padding: 100px 0 40px;}
.feedback_content_pnl{max-width: 550px;margin: 0 auto;}
.pnl_innr {height: calc(100vh - 300px);overflow-y: auto;}

/* -----------survey--fedback--page---------------------- */

/* --my-survey----- */
.rvw_survey_body{position: fixed;top: 0;left: 0;right: 0;}
.survey_innr {height: calc(100vh - 150px);}
.survey-canvas .offcanvas-bottom{height: 100vh;}
.left_survey{width: 35%;background-color: var(--bs-color-EA);}
.right_survey{width: 65%;padding-top: 40px;padding-bottom: 40px;}
.survey_topbx{box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.25), 0px 2px 4px 0px rgba(0, 0, 0, 0.10);}
.survey-canvas .btn-close{height: 48px;width: 48px;min-width: 48px;opacity: 1;}
.survey_srch .form-control{height: 48px;box-shadow: none;border-radius: 16px;padding: 0 16px 0 38px;border: 0;background-image: url(../images/search.svg);background-repeat: no-repeat;background-position: left 10px top 50%;background-size: 24px;}
.survey_pic {height: 78px;width: 78px;margin: 0 auto;}
.survey_progress_outer{height: 5px;background-color: var(--bs-light-gray);border-radius: 8px;}
.s_progress_bar{left: 0;top: 0;bottom: 0;position: absolute;border-radius: 8px;}
.img_rvw {width: 100px;border-radius: 10px;overflow: hidden;}
.survey_revw_innr{height:calc(100vh - 337px);overflow-y: auto;padding-right: 10px;}
.right_survey{height: 100vh;overflow-y: auto;}
.survey-canvas .offcanvas-body{padding: 0;overflow: hidden;}
.survey_usr_pic{height: 30px;width: 30px;min-width: 30px;}
.kitchen_info_top h2{font-size: 32px;text-transform: capitalize;font-weight: 400;}
.survey_date_outer .form-group{min-width: 150px;}
.input_fill.form-group .form-control{background-color: var(--bs-color-ED);border: 0;}
.survey_kitchen_info_item .upload_inner_btn{border: 2px dashed var(--bs-light-gray);}
.survey_kitchen_info_item .upload_icon{height: auto;}
.survey_kitchen_info_item .uploaded_innrBx{padding: 0;border-radius: 10px;overflow: hidden;}
.survey_kitchen_info_item .upload_pic{height: 100%;}
.survey_kitchen_info_item .upload_pic img{height: 100%;}
.survey_kitchen_info_item .delete_btn{background-color: rgba(255,255,255,1);height: 30px;width: 30px;}

.right_rvw_inr{height: 100%;}
.survey_subheading h3{font-size: 32px;}

.open_rvw_trigger{box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);border-radius: 10px;position: fixed;top: 0;left: 0;right: 0;z-index: 9;display: none !important;}

/* --my-survey----- */
.builder_lrightpnl .build_usrinfo h3{font-size: 22px;}



@media(max-width:991px){
  .left_survey {width: 290px;transition: 0.4s;position: fixed;top: 0;left: 0; z-index: 99;transform: translateX(-100%);}
  .right_survey{width: 100%;padding: 70px 24px 40px !important;}
  .open_rvw_trigger{display: flex !important;}
  .open-review .left_survey{transform: translateX(0);}
  .survey_bttmbx{padding: 24px 15px !important;}
  .survey_revw_innr{padding-right: 0;}
  .open-review  .overlay{display: block;}
  .right_survey .overlay{z-index: 10;}
}
@media(max-width:767px){
  .feed_head_inr {flex-wrap: wrap;}
  .feedback_pnl_sec {padding: 40px 0;}
  .question_heading h3{line-height: 150%;}
  .save_files{order: 1;}
  .kitchen-text{order: 3;width: 100%;margin-top: 20px;}
  .print_share_user {order: 2;}
  .feedback_header{background-color: var(--bs-color-EA);}
  .print_btn a span{display: none !important;}
  .print_btn .btn{width: 34px;min-width: 34px;padding: 0;}

}
@media(max-width:575px){
  .survey_date_outer .form-group {min-width: 115px;}
}
@media(max-width:420px){
  .pnl_innr .form_innr{flex-wrap: wrap;}
  .pnl_innr .form_innr .form-group{margin-bottom: 0;}
  .order_outer, .survey_date_outer {flex-wrap: wrap;}
  .survey_date_outer .form-group {min-width: 100%;}
  .survey_kitchen_info_item .gap-5 {gap: 1rem !important;}
}

.plan_sec{padding: 150px 0 70px;}
.pricing_item{border-radius: 24px;background-color: var(--bs-color-F2);}
.pricing_head{border-bottom: 1px solid var(--bs-color-DD);}
.pricing_head{border-bottom: 1px solid var(--bs-color-DD);}
.pricing_head h5{color: var(--bs-color-75);}
.pricing_head h3{color: var(--bs-color-00);font-size: 32px;font-weight: 600;}
.pricing_list li i{height: 24px;width: 24px;min-width: 24px;background-color: var(--bs-color-DC);font-size: 16px;}
.pricing_list li{font-size: 18px;color: var(--bs-color-00);margin-bottom: 16px;}
.pricing_list li:last-child{margin-bottom: 0;}
.pricing_list {min-height: 285px;}


@media(max-width:767px){
    .pricing_head h3{font-size: 24px;}
    .pricing_head h5{font-size: 14px;}
    .pricing_list li{font-size: 16px;}
    .pricing_list {min-height: auto;}
    .plan_sec {padding: 100px 0 30px;}
}


.links_wrapper ul li{width: 32.5%;}
.links_wrapper ul li a{word-break: break-word;}
.links_wrapper ul li a:hover{color: var(--bs-primary) !important;}
@media(max-width:991px){
  .links_wrapper ul li{width: 49%;}
}
@media(max-width:575px){
  .links_wrapper ul{flex-wrap: wrap;}
  .links_wrapper ul li{width: 100%;}
}

/* ==============Login-css-------- */

.addons_tabLinks.jobdetails_tablinks.login_tabs ul {margin: 0 0 24px;}
.addons_tabLinks.jobdetails_tablinks.login_tabs ul li {width: 50%;text-align: center;margin: 0;}
.addons_tabLinks.jobdetails_tablinks.login_tabs ul li a { margin: 0; border: 0;border-bottom: 2px solid var(--bs-color-F1);padding: 14px 10px;font-weight: 600;font-size: 14px;color: var(--bs-color-00);}
.addons_tabLinks .nav-tabs .nav-item.show .nav-link, .addons_tabLinks .nav-tabs .nav-link.active {color: var(--bs-color-38);border-color: var(--bs-color-38);}
.login_body .input_fill_landing .form-control{border: 1px solid var(--bs-color-D3);height: 50px;border-radius: 10px;padding: 0 16px;font-size: 15px;box-shadow: none;}
.login_body .input_fill_landing .form-control:focus{border-color: var(--bs-color-38);}
.login_body .input_fill_landing label{margin-bottom: 8px;} 
.form-group label{margin-bottom: 8px;}
.registration_modal .modal-header{position: relative;padding: 40px 30px 15px;border: 0;}
.registration_modal .close{background-color: var(--bs-color-F1);color: var(--bs-color-00);font-size: 20px;position: absolute;right: 15px;top: 15px;height: 44px;width: 44px;border-radius: 50%;display: flex;align-items: center;justify-content: center;}
.modal-content{border-radius: 10px;}
.small_modal{max-width: 460px;}
.medium_modal{max-width: 640px;}
.signin_hdng h3 {font-size: 30px;color: var(--bs-color-00);font-weight: 600;}
.signin_hdng h5{margin:12px 0 0;font-size:14px;color:var(--bs-color-75);font-weight:500}
.landing_login_fillOuter .input_fill_landing label{margin-bottom: 8px;font-weight: 600;font-size: 14px;line-height: 20px;color: var(--bs-color-00);}
.landing_login_fillOuter .input_fill_landing .form-control{border: 1px solid var(--bs-color-D3);border-radius: 5px;height: 46px;}
.landing_login_fillOuter .input_fill_landing p{display: flex;gap: 6px;margin: 10px 0 0;align-items: center;}
.error{font-size: 14px;line-height: 18px;color: var(--bs-color-E8);}
.error span{font-size: 14px;}
.landing_login_fillOuter .input_fill_landing{margin-bottom: 20px;}
.registration_modal .modal-body{padding: 15px 30px 44px;}
.registration_modal .radio-check p{color: var(--bs-color-00);}
.registration_modal .radio-check p a{display: inline-block;color: var(--bs-color-38);}
.eye_btnfill{position: relative;}
.forgot_applyOuter{display: flex;align-items: center;justify-content: space-between;}
.create_userOuter{margin: 20px 0 0;}
.linkedin_btn a{display: flex;align-items: center;height: 46px;gap: 10px;border: 1px solid var(--bs-color-F1);border-radius: 5px;justify-content: center;font-weight: 600; font-size: 15px;line-height: 18px;color: var(--bs-color-00);background-color: var(--bs-color-F1);}
.login_body .btn{height: 44px;font-size: 15px;}
.google_btn{margin-bottom: 8px;}
.create_text{margin: 20px 0 0;text-align: center;}
.create_text p{font-size: 15px;line-height: 19px;color: var(--bs-color-00);}
.create_text p a{display: inline-block;color: var(--bs-color-38);}
.create_text p a:hover{color: var(--bs-color-00);text-decoration: underline;}
.registration_modal .radio-check input[type='checkbox']+span{width:19px;min-width:19px;height:19px;border-radius:4px;border:2px solid var(--bs-color-D3);position:relative;margin-right:10px}
.registration_modal .radio-check input[type='checkbox']:checked+span:after{content:"";position:absolute;left:1px;top:1px;right:1px;bottom:1px;background:url(../images/check.png) no-repeat center center;background-size:10px}
.copy_link{right:12px;top:50%;position:absolute;transform:translateY(-50%);line-height:100%;color:var(--bs-color-75)}
.copy_link:hover{color: var(--bs-color-38);}
.copy_link span{font-size: 20px;}
.email_info{display: inline-flex;align-items: center;justify-content: center;margin-top: 10px;color: var(--bs-color-38);font-size: 14px;line-height: 18px;color: var(--bs-color-38);border-radius: 40px;height: 30px;gap: 10px;}
.email_info a{display: inline-block;color: var(--bs-color-38);}
.email_info a i{font-size: 18px;}
.input_fill_landing > label{font-weight: 600;}
.validate_fill {display: flex; gap: 8px;}
.validate_fill .form-control{box-shadow: none;height: 46px;text-align: center;border: 1px solid var(--bs-color-D3);color: var(--bs-color-00);width: 52px;border-radius: 5px;background-color: var(--bs-color-FF);padding: 0 10px;}
.validate_error{margin-top: 22px;}
.resend_btn a{gap: 5px;}
.resend_btn a i{font-size: 18px;}
.loginstep_btnsouter{display: flex;align-items: center;justify-content: space-between;margin-top: 30px;}
.text-left{text-align: left;}
.validate_msg{margin-bottom: 20px;}
.validate_msg p{font-size: 14px;line-height: 18px;color: var(--bs-color-75);}
.validate_list label, .validate_list li{font-size: 14px;color: var(--bs-color-75);line-height: 20px;margin-bottom: 15px;}
.validate_list li{position: relative;padding-left: 15px;}
.validate_list li::after{position: absolute;content: "";height: 5px;width: 5px;border-radius: 50%;background-color: var(--bs-color-75);left: 0;top: 8px;}
.validate_list ul{padding-left: 15px;}
.interest_hdng h4{color: var(--bs-color-00);font-weight: 500;font-size: 16px;line-height: 20px;}
.info_hdng_top{display: flex;align-items: center;justify-content: space-between;margin-bottom: 16px;}
.info_hdng_top h3{font-weight: 500;font-size: 24px;line-height: 30px;color: var(--bs-color-00);}
.info_hdng_top a{display: inline-flex;align-items: center;gap: 5px;color: var(--bs-color-75);font-size: 14px;}
.info_hdng_top a i{font-size: 18px;}
.info_hdng_top a:hover{color: var(--bs-color-38);}
.info_outerFill{display: flex;flex-wrap: wrap;margin: 0 -8px;}
.info_outerFill .input_fill_landing{padding: 0 8px;margin-bottom: 24px;}
.w_50{width: 50%;}
.w_100{width: 100%;}
.info_outerFill .input_fill_landing .form-control{height: 46px;box-shadow: none;border: 1px solid var(--bs-color-F1);border-radius: 5px;background-color: var(--bs-color-FF);font-size: 15px;line-height: 19px;color: var(--bs-color-00);padding: 0 15px;}
.info_outerFill .input_fill_landing label, .personel_information_area .description_fill.input_fill_landing label{font-weight: 500;font-size: 14px;line-height: 20px;color: var(--bs-color-00);}
.info_outerFill .input_fill_landing .select2-container--default .select2-selection--single{background-color: var(--bs-color-FF);color: var(--bs-color-75);font-weight: 400;border: 1px solid var(--bs-color-F1);}
.srvc_tags{margin: 8px 0 0;}
.srvc_tags ul{display: flex;gap: 5px 5px;flex-wrap: wrap;}
.srvc_tags ul li a{height: 34px;display: inline-flex;align-items: center;padding: 0 12px;border-radius: 90px;color: var(--bs-color-75);font-size: 14px;line-height: 18px;background-color: var(--bs-color-F1);}
.srvc_tags ul li a:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.login_info_item{margin-bottom: 30px;}
.login_info_item:last-child{margin-bottom: 0;}
.personel_information_area{background-color: var(--bs-color-F1);padding: 20px 16px;}
.personel_information_area .description_fill.input_fill_landing .form-control{background-color: var(--bs-color-FF);}
.step_bullet ul{display: flex;align-items: center;justify-content: center;gap: 5px;}
.step_bullet ul li{height: 10px;width: 10px;border: 1px solid var(--bs-color-75);border-radius: 50%;cursor: pointer;}
.step_bullet ul li.active{background-color: var(--bs-color-38);border-color: var(--bs-color-38);}
.documents_uploaditem{margin-bottom: 40px;}
.documents_uploaditem:last-child{margin-bottom: 0;}
.documents_uploaditem h4{font-weight: 500;font-size: 24px;line-height: 30px;color: var(--bs-color-00);margin-bottom: 16px;}
.upload_bx{position: relative;border: 1px dashed var(--bs-color-75);border-radius: 5px;height: 180px;color: var(--bs-color-00);display: flex;align-items: center;justify-content: center;padding: 15px;text-align: center;}
.upload_bx i{display: block;margin-bottom: 10px;}
.upload_bx span{font-weight: 500;font-size: 16px;line-height: 20px;}
.profile_pic_modal .modal-content{background-color: var(--bs-color-00);}
.rotate ul{display: flex;align-items: center;justify-content: center;gap: 15px;}
.profile_pic_modal .modal-header{padding: 30px 24px;justify-content: flex-start;}
.arrw_back a{display: inline-block;color: var(--bs-color-FF);}
.days_bx ul{display: flex;align-items: center;gap: 8px;}
.days_bx ul li{width: 100%;}
.days_bx ul li span{display: flex;height: 46px;align-items: center;justify-content: center;font-size: 15px;line-height: 19px;color: var(--bs-color-75);background-color: var(--bs-color-F1);border-radius: 8px;cursor: pointer;}
.days_bx ul li.active span{background: var(--bs-color-FF); box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);border-radius: 8px;color: var(--bs-color-38);}
.time_bx ul{gap: 0;margin: 0 -4px;flex-wrap: wrap;}
.time_bx ul li{width: 20%;padding: 0 4px 8px;}
.job_change_outer ul{padding: 5px;background-color: var(--bs-color-F1);display: flex;gap: 8px;}
.job_change_outer ul li{width: 100%;}
.job_change_outer ul li a{display: flex;align-items: center;justify-content: center;height: 46px;border-radius: 8px;padding: 0 24px;font-size: 15px;line-height: 19px;color: var(--bs-color-75);}
.job_change_outer ul li.active a{background-color: var(--bs-color-FF);box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);color: var(--bs-color-38);}
.srvc_tags.joining_tags ul{display: block;}
.srvc_tags.joining_tags ul li{margin-bottom: 10px;}
.srvc_tags.joining_tags ul li:last-child{margin-bottom: 0;}
.circle_tick{color: var(--bs-color-38);}
.circle_tick span{font-size: 18px;}
.grn_circle{color: var(--bs-color-31);}
.success_text .btns_group .basic_btn{margin: 0;}
.create_chk_outer .trms_check{margin-bottom: 20px;}
.create_chk_outer .trms_check .radio-check{align-items: flex-start;}
.registration_modal .create_chk_outer .radio-check input[type='checkbox']+span{margin-top: 3px;}
.registration_modal .create_chk_outer .radio-check p{color: var(--bs-color-75);font-weight: 400;}
.forgot_btn a{color: var(--bs-color-38);font-size: 15px;font-weight: 600;}
.or_text{color: var(--bs-color-B3);font-size: 15px;font-weight: 400;line-height: 25px;letter-spacing: 0px;text-align: center;}
#login_modal .modal-body{padding-top: 0;}
.otp_fill_outer .form-group{margin-bottom: 24px;}
.otp_fill_outer .form-group .btn.outline{margin-top: 10px;gap: 6px;height: 44px;}
.conatct_modal.success_popup .modal-dialog {max-width: 364px;}
.success_popup .modal-body {padding: 40px 20px;}
.success_graphics {text-align: center;margin-bottom: 30px;}
.success_text {text-align: center;}
.success_text h3 {font-weight: 700;font-size: 22px; line-height: 26px;color: var(--bs-color-00);margin-bottom: 8px;}
.success_text p {font-weight: 500;font-size: 16px;line-height: 26px; color: var(--bs-color-00);}
.success_popup .btn{height: 44px;}

@media(max-width:767px){
    .registration_modal .modal-body {padding: 15px 15px 25px;}
    .signin_hdng h3{font-size: 22px;}
    .registration_modal .modal-header{padding: 40px 15px 15px;}
    .info_hdng_top h3, .documents_uploaditem h4{font-size: 18px;line-height: 24px;}
    .registration_modal .close{height: 36px;width: 36px;}
    .registration_modal .close i{font-size: 20px;}
}
@media(max-width:575px){
    .w_50 {width: 100%;}
    .days_bx ul li span{font-size: 12px;line-height: 17px;height: 36px;}
    .days_bx ul{gap: 4px;}
    .time_bx ul {gap: 0;margin: 0 -2px;}
    .time_bx ul li {padding: 0 2px 6px;}
    .info_outerFill .input_fill_landing{margin-bottom: 16px;}
    .job_change_outer ul{flex-wrap: wrap;border-radius: 5px;}
    .info_outerFill .input_fill_landing .form-control, .landing_login_fillOuter .input_fill_landing .form-control{height: 48px;}
    .validate_list label, .validate_list li{font-size: 13px;}
    .info_outerFill .input_fill_landing label, .personel_information_area .description_fill.input_fill_landing label{font-size: 13px;}
    .job_change_outer ul li a{padding: 0 15px;}
}
@media(max-width:420px){
    .create_userOuter{flex-wrap: wrap;}
}

/* end--login-css */

/* ============marketplace========== */
.cursor-pointer{cursor: pointer;}
.filter_toggle .form-check-input { box-shadow: none !important; height: 24px; width: 41px; margin-top: 0; }
.form-check-input:checked { background-color: var(--bs-primary); border-color: var(--bs-primary); }
.pl-0{padding: 0 !important;}
.filter_list li.selected{border-color: var(--bs-primary) !important;color: var(--bs-primary) !important;font-weight: 700 !important;}
.icon-fill { font-variation-settings: 'FILL' 1, 'wght' 400, 'GRAD' 0, 'opsz' 24; }
.catagories_list li{width: 20%;}
.catagories_list li + li::before{position: absolute;content: "";height: 31px;width: 1px;background-color: #D3E3FD;top: 50%;left: 0;transform: translateY(-50%);}
.top_serach_pnl .search_fill .form-control{padding-left: 50px;}

.top_serach_pnl .srch_btn{left: 20px;top: 56%;}
.premium_tags { left: 0; top: 0; background: var(--premium-gradient); border-radius: 0 0 8px 0; }
.desc h5 a, .desc p{white-space: nowrap;text-overflow: ellipsis;overflow: hidden;display: block;}
.marketplace-content .btn{background-color: var(--bs-primary);color: var(--bs-white);height: 50px;}
.marketplace-content .btn:hover{background-color: var(--bs-color-4E);color: var(--bs-black);}
.img-container{height: 160px;}
.img-container img{max-height: 100%;max-width: 100%;}
/* ============marketplace========== */

/*details*/
.marketplace-details-wrapper .marketplace-details-head .product-slider-container .product-slider-nav .slick-list .slick-track{display: block;}
.marketplace-details-wrapper .marketplace-details-head .product-slider-container .product-slider-nav .slick-list .slick-track .nav-item{width: 6.25rem;height: 6.25rem;margin-bottom: 0.625rem;}
.marketplace-details-wrapper .marketplace-details-head .product-slider-container .product-slider-nav .slick-list .slick-track .nav-item:last-child{margin-bottom: 0;}
.marketplace-details-wrapper .marketplace-details-head .product-slider-container .product-slider-nav .slick-list .slick-track .nav-item.slick-current img{opacity: 0.3;}
.marketplace-details-wrapper .marketplace-details-head .product-slider-container .product-slider .slick-list .slick-track .slide-item .img-container{height: 26.875rem;}
.marketplace-details-wrapper .marketplace-details-head .product-meta li:not(:last-child){position: relative;padding-right: 0.625rem;margin-right: 0.625rem;}
.marketplace-details-wrapper .marketplace-details-head .product-meta li:not(:last-child)::after{content: '|';display: block;color: var(--bs-gray-600);position: absolute;top: 0;right: -0.063rem;}
.marketplace-details-wrapper .marketplace-details-head .cart-quantity input{padding-left: 2.25rem;padding-right: 2.25rem;height: 52px;}

.marketplace-details-head h1{font-family: 'Roboto', sans-serif;}
.product-details-info{max-width: 680px;}
.product-details-info li:nth-child(even){background-color: var(--bs-color-F2);}
.product-details-info li > span:first-child{min-width: 240px;}

.marketplace-details-content .people_details_bx .accordion-button{font-size: 16px;color: var(--bs-black);}

/*details*/
.border-green{border: 1px solid #31A61E;} 
.bg-light-green {background: rgba(49, 166, 30, 0.10) !important;}
.border-light-gray{border-color: var(	--bs-light-gray) !important}

/* ---people-details--appointment-table--- */
.booking_table .table-responsive{border: 1px solid #DBE4ED;}
.booking_table .table-responsive thead{background-color: #fff;}
.booking_table .table-responsive thead th{padding: 10px;border-bottom: 1px solid #DBE4ED;}
.booking_table .table-responsive td{padding: 4px;}
.booking_table .table-responsive td a{display: block;padding: 10px;background-color: #fff;font-size: 14px;font-weight: 600;color: #000;text-align: center;border-radius: 5px;border: 2px solid transparent;}
.booking_table .table-responsive td a.disabled{background-color: #E4EEF8;color: #9AABC6;pointer-events: none;}
.booking_table .table-responsive td a.active{background-color: #dbe4ed;border-color: #3191FF;color: #3191FF;}
.booking_table .table-responsive tbody{background-color: #F1F6FB;}
.appointment_date{text-align: center;}
.appointment_date h4{font-size: 14px;font-weight: 600;line-height: 20px;}
.appointment_date h5{font-size: 12px;color: var(--bs-gray-paragraph);}
/* ---people-details--appointment-table--- */

/* --new--stories-details--- */
.sucess_progress_item { background-color: var(--bs-color-F1); border-radius: 10px; padding: 15px 20px; height: 100%; }
.sucess_progress_item h3 { font-size: 25px; font-weight: 600; line-height: 112%; letter-spacing: -1px; margin-bottom: 10px; }
.sucess_progress_item .progess_outer { height: 6px; background-color: var(--bs-color-31); margin-bottom: 10px; }
.sucess_progress_item .progress_bar { background-color: var(--bs-color-E8); }
.sucess_progress_item h5 { font-size: 14px; }
.sucess_progress_outer{max-width: 960px;}
.information_list ol{padding-left: 20px;}
.sucess_progress_outer .row{row-gap: 20px;}
/* --new--stories-details--- */

/* ---ecosystem---- */
.company_bxitem { padding: 20px; border-radius: 10px; background-color: var(--bs-color-FF); box-shadow: 0px 1px 6px 0px rgba(62, 96, 147, 0.18); margin-bottom: 20px; }
.ecosystem_wrapper .company_logo { height: 105px; border-radius: 5px; background-color: var(--bs-color-FF); margin-bottom: 15px; border: 1px solid var(--bs-color-DD); }
.ecosystem_wrapper .company_logo{background-color: var(--bs-color-F1);border-color: var(--bs-color-F1);}
.ecosystem_listing li{margin-bottom: 5px;display: flex;align-items: center;gap: 5px;color: var(--bs-color-00);font-size: 16px;}
.ecosystem_listing li i{font-size: 18px;color: var(--bs-color-31);}
.more_listing a{display: inline-block;color: var(--bs-color-38);font-weight: 600;font-size: 16px;}
.more_listing a:hover{color: var(--bs-color-00);}
.border-green{border-color: var(--bs-color-31) !important;}
/* ---ecosystem---- */

/* ---corporations---- */
.corporation_logo {height: 64px;min-width: 64px;width: 64px;}

/* ----corporations-details--page--- */
.top_company_row .row {margin: 0 -5px;width: 100%;}
.top_company_row .col-sm-6{padding: 0 5px;}
.project_video{height: 318px;}
.share_icon{height: 44px;width: 44px;background-color: var(--bs-color-F1);color: var(--bs-color-00);}
.share_icon span{font-size: 20px;}
.share_icon:hover{color: var(--bs-color-38);}
.company_hdng h3{font-weight: 600;font-size: 25px;}
.rounded_tags .jb_rating{border-radius: 40px;}
.srvc_bxouter .row{margin: 0 -5px;}
.srvc_bxouter .col-md-6{padding: 0 5px;}
.srvc_bxItem{padding: 16px;border-radius: 10px;background-color: var(--bs-color-FF);box-shadow: 0px 1px 6px 0px rgba(62, 96, 147, 0.18);
  margin-bottom: 10px;font-family: 'Titillium Web', sans-serif;}
.left_srvc h3{font-weight: 600;font-size: 20px;line-height: 30px;margin-bottom: 2px;}
.left_srvc p{font-size: 14px;line-height: 21px;color: var(--bs-color-00);margin-bottom: 5px;}
.right_status{min-width: 30px;color: var(--bs-color-31);}
.project_pnl_row, .success_stories_row{padding: 20px;border-radius: 10px;background-color: var(--bs-color-FF);box-shadow: 0px 1px 6px 0px rgba(62, 96, 147, 0.18); margin-bottom: 20px;font-family: 'Titillium Web', sans-serif;}
.project_leftpnl{display: flex;gap: 10px;}
.project_postpic{width: 150px;min-width: 150px;}
.project_info_text h4{font-weight: 600;font-size: 20px;line-height: 30px;color: var(--bs-color-00);margin-bottom: 5px;}
.project_info_text h6{color: var(--bs-color-38);font-weight: 600;font-size: 14px;line-height: 21px;margin-bottom: 5px;}
.project_info_text p{font-size: 14px;line-height: 21px;color: var(--bs-color-00);}
.project_rightpnl{min-width: 130px;}
.job_hdng .company_name_place{margin-top: 10px;}
.challenges_tag_outer{display: flex;align-items: center;justify-content: space-between;margin: 20px 0;}
.right_challenges ul{display: flex;align-items: center;}
.right_challenges li{margin-right: 20px;padding-right: 20px;border-right: 1px solid var(--bs-color-DD);}
.right_challenges li:last-child{margin-right: 0;padding-right: 0;border-right: 0;}
.right_challenges li a{color: var(--bs-color-00);font-size: 14px;line-height: 21px;}
.right_challenges li a span{color: var(--bs-color-F0);}
.job_details_para ul{padding-left: 15px;}
.job_details_para li{margin-bottom: 15px;color: var(--bs-color-00);font-size: 14px;line-height: 21px;list-style: disc;}
.job_details_para li:last-child{margin-bottom: 0;}
.challenges_item .details_btn .btn{width: auto;}
.success_stories_row{gap: 20px;}
.stories_text h5 {font-size: 14px;color: var(--bs-color-00);font-weight: 400;margin-bottom: 5px;}
.stories_text h3 {font-size: 25px;font-weight: 600;color: var(--bs-color-00);letter-spacing: -1px;line-height: 28px;margin-bottom: 5px;}
.stories_text p {font-weight: 400;font-size: 14px;line-height: 26px;color: var(--bs-color-00);}
.stories_text h4{font-size: 14px;color: var(--bs-color-38);margin-bottom: 16px;}
.stories_text .btn{height: 36px;}
.company_info_area, .company_links_outer{font-family: 'Titillium Web', sans-serif;}
.company_info_item h3{font-weight: 600;font-size: 16px;line-height: 18px;color: var(--bs-color-00);margin-bottom: 5px;}
.company_info_item a{display: inline-flex;align-items: center;font-weight: 400;font-size: 14px;line-height: 21px;color: var(--bs-color-38);}
.company_info_item a i{font-size: 16px;display: inline-block;margin-left: 5px;}
.company_info_item h5{font-weight: 400;font-size: 14px;line-height: 21px;color: var(--bs-color-00);}
.company_info_item{margin-bottom: 25px;}
.company_linksItem ul{display: flex;gap: 8px;flex-wrap: wrap;}
.company_linksItem ul li a{height: 30px;border: 1px solid var(--bs-color-00);padding: 0 10px;border-radius: 90px;color: var(--bs-color-00);font-size: 14px;line-height: 21px;display: flex;align-items: center;justify-content: center;}
.company_linksItem ul li a:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);border-color: var(--bs-color-38);}
.company_linksItem h4{font-weight: 500;font-size: 16px;line-height: 18px;color: var(--bs-color-00);margin-bottom: 10px;}
.company_linksItem{margin-bottom: 25px;}
.company_linksItem:last-child{margin-bottom: 0;}
.report_btn .border_btn{height: 36px;padding: 0 16px;}
.hdng_tag{border-radius: 90px;padding: 3px 8px;color: var(--bs-color-FF);background-color: var(--bs-color-38);font-size: 12px;line-height: 100%;}
.founders_item{box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.25);border-radius: 5px;overflow: hidden;font-family: 'Titillium Web', sans-serif;}
.team_usrInfo h3{font-size: 18px;color: var(--bs-color-00);font-weight: 600;}
.founders_top, .founders_bttm{padding: 16px;}
.founders_top .people_team_row {margin-bottom: 12px;}
.team_usrInfo h5{font-size: 14px;font-weight: 400;}
.team_usrpic{height: 40px;width: 40px;border-radius: 50%;text-align: center;background-color: var(--bs-color-F1);color: rgba(0, 0, 0, 0.2);display: flex;align-items: center;justify-content: center;}
.founders_bttm{background: var(--Gredient, linear-gradient(180deg, #384CFF 0%, #9441FF 100%));}
.founders_bttm h6{margin-bottom: 10px;font-size: 14px;color: var(--bs-color-FF);}
.founders_top h5{font-size: 14px;color: var(--bs-color-00);}
.founders_top h5 span{font-weight: 600;font-size: 12px;color: var(--bs-color-38);}
.team_usrInfo h4{font-size: 14px;font-weight: 600;}
.founders_bttm .team_usrInfo h5{font-size: 12px;}
.founders_bttm .team_usrpic{background-color: transparent;}
.people_team_row{margin-bottom: 40px;}
.people_team_row:last-child{margin-bottom: 0;}
.company_peoplebx{box-shadow: 0px 1px 6px 0px rgba(62, 96, 147, 0.18);border-radius: 5px;padding: 16px;font-family: 'Titillium Web', sans-serif;margin-bottom: 10px;}
.company_peoplebx .people_team_row{margin-bottom: 15px;}
.bg-dark-gray{background-color: var(--bs-light-gray);}
.people_btns a:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF) !important;}
.people_team_row .row{margin: 0 -5px;}
.people_team_row .col-6{padding: 0 5px;}
.kpi_table_innr{overflow-x: auto;}
.kpi_table_innr table{width: 100%;}
.kpi_table_innr thead{background-color: var(--bs-light-gray);}
.kpi_table_innr thead th, .kpi_table_innr tbody td{padding: 16px;font-size: 14px;font-weight: 400;color: var(--bs-color-00);}
.kpi_table_innr thead th:first-child, .kpi_table_innr tbody td:first-child{padding: 16px 10px;}
.kpi_table_innr thead th{border-right: 2px solid var(--bs-color-FF);}
.kpi_table_innr thead th:last-child{border-right: 0;}
.kpi_table_innr tbody td{border-bottom: 1px solid var(--bs-color-DD);}
.kpi_table_innr tbody th span{display: block;}
.kpi_table_innr tbody td:last-child{text-align: center;}
#kpis .accordion-item, #kpis .accordion-flush .accordion-item .accordion-button{background-color: var(--bs-color-FF);border: 0;font-size: 18px;color: #000;}
#kpis .accordion-flush .accordion-item .accordion-button{padding: 15px 0;}
#kpis .accordion-flush .accordion-item .accordion-body{padding: 0;}
#kpis .accordion-item{border-bottom: 1px solid var(--bs-color-DD);}
#kpis .accordion-item:last-child{border-bottom: 0;}
.card .percent {position: relative;text-align: center;}
.card svg {position: relative; transform: rotate(-90deg); margin: 0 auto;height: 100px;width: 100px;}
.card svg circle {width: 100%;height: 100%;fill: none;stroke: #EAEAEA;stroke-width: 3;stroke-linecap: round;}
.card svg circle:last-of-type {stroke-dasharray: 281px;stroke-dashoffset: calc(281px - (281px * var(--percent)) / 100); stroke: var(--bs-color-38);}
.card:nth-child(1) svg circle:last-of-type { stroke: var(--bs-color-38);}
.card .number_value {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);}
.dashboad_progress_outer .card{background-color: var(--bs-color-F1);padding: 25px;text-align: center;border-radius: 5px;border: 0;margin-bottom: 10px;}

.circle_hdng{margin-bottom: 20px;}
.circle_hdng h5{font-weight: 400; font-size: 14px;line-height: 21px;color: var(--bs-color-00);}
.circle_hdng h5 span{font-weight: 600;}
.color_red{color: var(--bs-color-38) !important;}
.color_green{color: var(--bs-color-31) !important;}
.number_value h3{font-weight: 600;font-size: 16px;line-height: 20px;}
.number_value h3 span{display: block;font-weight: 400;font-size: 12px;line-height: 15px;letter-spacing: -0.01px;}
.expartbttm_area .tag_links li a{font-weight: 600;}
.copy_links_popup {position: absolute;left: 0px;bottom: 0px;right: 0;text-align: center;}
.copy_links_popup a {border-radius: 6px;border: 1px solid var(--bs-color-DD); background-color: var(--bs-color-FF); box-shadow: 0px 1px 6px 0px rgba(62, 96, 147, 0.18);padding: 4px 8px; color: var(--bs-color-00);font-size: 15px;font-weight: 600;}
.connected_text p {gap: 6px;color: var(--bs-color-75);font-size: 14px;white-space: nowrap;}
.connected_text p i {font-size: 18px;color: var(--bs-color-31);}
/* ---end----- */

/* faq-list */
.search_left .form-control{padding-left: 40px;height: 40px;}
.faq_info_link p a{text-decoration: underline;} 
.faq_info_link p a:hover{color: #000 !important;text-decoration: none;}
.faq_sponsor li + li{padding-left: 24px;border-left: 1px solid #ccc;}

/* --suppy-bids--challenges--- */

.upload_row .upload_inner_btn{border: 2px dashed #99A1AA;}
.sample_radio .radio-check input[type=radio]:checked+span{border-color: var(--bs-primary);}
.sample_radio .radio-check input[type=radio]:checked+span:after{background-color: var(--bs-primary);}
.text-5a{color: var(--bs-color-5A);}
.bg-f2{background-color: var(--bs-color-F2);}

/* ---offer--module--- */
.btn-md{height: 48px !important;}
.ofrsell_detailspnl{padding: 24px;border: 1px solid var(--bs-color-DD);border-radius: 10px;position: relative;}
.ofr_selldetails_top{position: relative;}
.right_top_bookmark{position: absolute;right: 0;top: 0;z-index: 9;}
.right_top_bookmark a{display: inline-block;color: var(--bs-color-75);}
.info_top h3{font-size: 18px;color: var(--bs-color-00);font-weight: 600;letter-spacing: -1px;margin-bottom: 5px;}
.ofr_selldetails_top .info_top h3{padding-right: 20px;font-size: 25px;font-weight: 600;}
.ofr_selldetails_top .info_top  h4{font-size: 18px;}
.sell_product_row{overflow-y: hidden;overflow-x: auto;}
.sell_product_item{height: 236px;width: 236px;background-color: var(--bs-color-F1);border-radius: 10px;min-width: 236px;}
.ofr_sellpic{min-width: 144px;height: 136px;background-color: var(--bs-color-F1);border-radius: 10px;padding: 10px;}
.ofr_tags {display: inline-flex;align-items: center;padding: 4px 8px;border-radius: 40px;background-color: var(--bs-color-F1);color: var(--bs-primary);font-size: 12px;font-weight: 600;}
.on_buy{background-color: var(--bs-color-31);color: var(--bs-color-FF);}
.ofr_tags_outer  h3{font-size: 14px;color: var(--bs-color-00);font-weight: 400;}
.ofr_bookmark{color: var(--bs-color-75);}
.ofr_row:hover, .ofr_row.active{background: linear-gradient(270deg, rgba(241, 241, 241, 0.00) 0%, #FFF9F9 51.56%, rgba(241, 241, 241, 0.00) 100%), #FFF;}
.buy_innr{border-top: 1px solid var(--bs-color-DD);margin-top: 20px;}
.lft_details { width: 40%; }
.rght_details{width: 60%;}
.text-black-medium{color: var(--bs-color-37);}
.ofr_leftpnl{max-height: 900px;overflow-y: auto;}

/* --------offer-details----- */
.prodetails_list li + li{border-left: 1px solid #ccc;}
.list_style_none{list-style: none;}

/* -------------------- */

.chat_modal .own_msgRow .chat_msgright{max-width: 100%;width: 100%;padding-right: 0;}
.modal .msg_outer { max-height: 300px; overflow-y: auto; }
.typemsg_bx { background-color: var(--bs-color-F1); border-radius: 10px; padding: 0 18px; margin-top: 20px;}
.typemsg_bx .form-control { border: 0; box-shadow: none; padding: 0; box-shadow: none; height: 50px; background-color: transparent; }
.msg_icons { min-width: 85px; }
.currency_dropmenu{min-width: 80px;}

.mob_float_challenges { position: fixed; left: 0; right: 0; bottom: 0; background: #fff; box-shadow: 0 0 15px rgba(0, 0, 0, 0.1); padding: 10px; display: none;}

@media(min-width:1200px){
  .sameheight_scroll{height: calc(100vh - 260px);overflow-y: auto;}
}
@media(min-width:1400px){
  .marketplace-details-content .left_rvwpnl { width: 20%; }
  .marketplace-details-content .right_rvwpnl { width: 25%; }
}
@media(min-width:1600px){
  .batch_total{min-height: 100vh;}
}
@media(max-width:1600px){
    .banner_content h1{font-size: 90px;}
    .banner_content p{font-size: 16px;}
    .pd_130 {padding: 80px 0;}
    .tab_links .nav-tabs .nav-link{font-size: 16px;}
    .page_hdng h2 {font-size: 60px;line-height: 66px;}
    /* .page_hdng {margin-bottom: 30px;} */
    .tab_links .nav-tabs .nav-link span{height: 35px;width: 35px;min-width: 35px;}
    .help_text h3 {font-size: 20px;line-height: 26px;margin-bottom: 15px;}
    .help_text p{font-size: 16px;line-height: 26px;}
    .review_slideInner{padding: 40px 20px;}
    .koma {max-width: 30px; margin-bottom: 20px;}
    .rvw_para p {font-size: 18px;line-height: 28px;}
    .rvw_para {margin-bottom: 30px;}
    .user_rvwpic{height: 60px;width: 60px;}
    .user_name h4 {font-size: 18px;line-height: 28px;}
    .user_name h6 {font-size: 14px;line-height: 24px;}
    /* ============================================================== */
    /* innerpages--CRM */
    .business_rightpnl p{font-size: 16px;}
    .ofr_hdng h3{font-size: 30px;}
    .ofr_hdng{margin-bottom: 15px;}
    .ofr_para p{font-size: 17px;line-height: 27px;}
    /* innerpages--CRM */
    .kit_srvc_text h3{font-size: 22px;}
    .kit_srvc_rightpnl li{font-size: 16px;}
    .rewards_point_item h2{font-size: 60px;line-height: 60px;}
    .rewards_point_item p{font-size: 18px;line-height: 26px;}
    .rewards_outer{padding: 80px 0 0;}
    .succes_rewards_sec{padding-bottom: 40px;}
    .success_stories{padding: 40px 0 80px;}
    .success_item p{font-size: 20px;line-height: 30px;}
    .digital_left h4 {font-size: 24px;line-height: 32px;margin-bottom: 16px;}
    .digital_left h5{font-size: 16px;line-height: 28px;}
    .digital_left h2{font-size: 48px;}
    .digital_right .form-group .form-control{font-size: 16px;}
    .contact_form_area .form-group .form-control{font-size: 16px;padding: 0 20px;}
    .contact_form_area .form-group.description_fill .form-control{padding: 16px 20px;}
}
@media(max-width:1199px){
    .banner_area{padding: 135px 0 90px;}
    .banner_content h1 {font-size: 54px;}
    .left_info h5{max-width: 100%;}
    .brand_logo_outer{padding-left: 30px;}
    .left_info h5{font-size: 18px;}
    .brand_company_sec{padding: 30px 0;}
    .page_hdng h2 {font-size: 42px;line-height: 50px;}
    .page_hdng h2 br{display: none;}
    .review_slideInner {padding: 30px 16px;}
    .rvw_para p {font-size: 16px;line-height: 25px;}
    .input_fill .form-control{height: 52px;font-size: 15px;}
    /* =============news-deed===================== */
    .contact_rightpnl{display: none;}
    .pnl_mdlpnl{width: 70%;}
    .event_slideItem{width: 33vw;}
    .people_slideItem{width: 25vw;}
    .pnl_leftpnl{width: 30%;}
    .pnl_rightpnl {width: 100%;margin-top: 20px; }
    /* =============news-deed===================== */
    /* ---job----------- */
    .pnl_mdlpnl.job_rightpnl { width: 70%; }
    .job_outer{flex-wrap: wrap;}
    .job_leftpnl {width: 100%;min-width: 100%;}
     /* ---job----------- */

     /* ---course--details--- */
     .task_btn{min-width: 100px;}
     .course_skill{align-items: flex-start;}

     .profile_pnlOuter .usr_left_top {display: none;}
     .profile_pnlOuter .pnl_leftpnl{width: 100%;margin-bottom: 20px;}
     .profile_pnlOuter .pnl_mdlpnl {width: 100%; }
     .profile_pnlOuter .left_menulist ul{display: flex;overflow-x: auto;overflow-y: hidden;justify-content: center;gap: 30px;}
     .profile_pnlOuter .left_menulist li a{padding: 0;font-size: 14px;}
     .profile_pnlOuter .feed_menu_left{gap: 5px;}
     .profile_pnlOuter .left_menulist li{position: relative;}
     .company_details_outer .pnl_mdlpnl {width: 70%; }
     /* --end-course-details== */
     /* --blog-- */
     .blog_landing_total {padding: 190px 0 30px;}

      /* --blog-- */

    /* --kit--digital---- */
      .kit_para p{font-size: 16px;line-height: 26px;margin-bottom: 20px;}
      .digital_pricebx{padding: 20px;}
      .price_icon{height: 60px;width: 60px;}
      .price_icon span{font-size: 30px;}
      .price_text_area h6{font-size: 16px;}
      .price_text_area h2{font-size: 30px;margin-bottom: 16px;}
      .price_text_area p{font-size: 16px;line-height: 26px;min-height: 50px;margin-bottom: 20px;}
      .price_text_area a{font-size: 14px;}
      .digital_srvc_row{padding: 30px 20px;gap: 30px;}
      .kit_srvc_text h3{font-size: 18px;}
       /* --kit--digital---- */
      /* --success-stories---- */
       .success_testimonial_bx{padding: 30px;}
       .quote_leftpnl p {font-size: 18px;font-weight: 600; line-height: 32px;}
       .rewards_outer {padding: 40px 0 0;}
       .rewards_point_item h2 {font-size: 40px;line-height: 50px;}
       .rewards_point_item p {font-size: 16px;line-height: 26px;}
       .success_item{padding: 30px;}
       .success_item p {font-size: 16px;line-height: 26px;}

      /* --success-stories---- */
    /* --staring----- */
    .starting_signup_process{padding: 30px 0 0 ;}
    .process_innr {height: calc(100vh - 81px);}
    .friend_results_bx { height: calc(100vh - 265px);padding: 10px;}

    /* --company-details--- */
    .esg_hdng h2{font-size: 36px;line-height: 44px;}
    .sustainability_pic::after{right: 0;}
    .impact_row{padding: 16px;}
    .impact_slide_item {width: 70vw;}

    /* --end-company--details--- */
    .meeting_left, .meeting_mdl, .meeting_right{width: 100%;margin-bottom: 24px;}
    .meeting_item + .meeting_item::after{display: none;}
    .meeting_row, .meeting_usr_info, .meeting_user_text ul, .meeting_left .d-flex {justify-content: center;}
    .meeting_right{margin-bottom: 0;}
    .meeting_usr_info {flex-wrap: wrap;}
    .meeting_user_text{width: 100%;text-align: center;}
    .filter_right  {position: fixed;left: 0;right: 0;bottom: 0; padding: 10px;background-color: var(--bs-color-FF);z-index: 99;box-shadow: 0 0 15px rgba(0,0,0,0.15);}
    .filter_select {min-width: auto; width: 100%;}
    .my-meeting{padding-bottom: 90px;}
    .marketplace_outer .col-xl-3{margin-bottom: 30px;}
    .pnl_list_wrap .pnl_mdlpnl{width: 100%;}
    .dashboad_progress_outer{margin-top: 30px;}
    .kpi_table_innr tbody td{min-width: 200px;}

    .ofr_leftpnl{display: flex;justify-content: space-between;flex-wrap: wrap;}
    .ofr_item {width: 49%;}

}
@media(max-width:991px){
    .banner_content h1 {font-size: 44px;line-height: 100%;}
    .bnr_btns {margin: 25px 0 0;}
    .left_info {width: 100%;border-right: 0;padding-right: 0;margin-bottom: 20px;}
    .brand_logo_outer {padding-left: 0;width: 100%;}
    .pd_130 {padding: 50px 0;}
    .tab_links .nav-tabs .nav-link{padding: 8px 15px 8px 8px;}
    .page_hdng h5{font-size: 16px;}
    .review_slideItem{width: 36vw;}
    .inBtns{position: static;margin: 30px 0 0;justify-content: center;}
    .rvw_outer .slick-arrow{height: 46px;width: 46px;}
    .lobees_para p, .talent_para p{font-size: 16px;line-height: 26px;}
    .user_comments h5{font-size: 20px;line-height: 30px;}
    .user_comments h6{font-size: 16px;line-height: 26px;}
    .why_rightpnl{margin-top: 20px;}
    .searching_sec .talent_outer .col-lg-5{order: 1;}
    .searching_sec .talent_outer .col-lg-7{order: 2;}
    .page_hdng h2 {font-size: 32px; line-height: 40px; }
    .page_hdng{margin-bottom: 40px;}
    .ofr_hdng h3 {font-size: 24px;line-height: 34px;}
    .ofr_para p{font-size: 15px;line-height: 26px;}
    .ofr_row:nth-child(odd) .col-lg-6 .ofr_textpnl{padding-left: 0;}
    .ofr_row:nth-child(even) .col-lg-6 .ofr_textpnl{padding-right: 0;}
    .ofr_row:nth-child(even) .col-lg-6:first-child{order: 1;}
    .ofr_row:nth-child(even) .col-lg-6:last-child{order: 2;}
    .ofr_hdng h3 br{display: none;}
    .why_leftpnl {padding-right: 0;}
    .howwrks_outer .col-lg-7{order: 2;}
    .howwrks_outer .col-lg-5{order: 1;margin-bottom: 30px;}
    .tab_links .nav-tabs .nav-item::after{display: none;}
    .tab_links .nav-tabs{display: flex;gap: 16px;overflow-x: auto;flex-wrap: nowrap;}
    .tab_links .nav-tabs .nav-link{white-space: nowrap;}
    .contact_form_area .form-group .form-control{height: 50px;}
/* =============news-deed===================== */
    .hdr_lftmenu {width: 290px; background-color: var(--bs-color-FF);padding: 30px 16px 20px;position: fixed;left: 0;top: 0;bottom: 0;    z-index: 102;box-shadow: 0 0 20px rgba(0,0,0,0.2);transform: translateX(-100%);transition: 0.5s;display: block;}
    .cross_btn{display: block;cursor: pointer;height: 30px;width: 30px;background-color: var(--bs-color-38);color: var(--bs-color-FF);display: flex;align-items: center;justify-content: center;border-radius: 50%;position: absolute;right: 15px;top: 15px;font-size: 16px;}
    .pnl_mdlpnl {width: 100%;}
    .hdr_lftmenu .left_menulist{padding: 0;height: 100%;overflow-y: auto;}
    .header_serach .form-control{width: 100%;}
    .hdr_hamberger{display: block;}
    .event_slideItem {width: 44vw;}
    .aftre_login_header{padding: 13px 0;}
    .hdr_hamberger{min-width: 25px;}
    .sidebar_open .hdr_lftmenu{transform: translateX(0);}
    .sidebar_open .overlay{display: block;}
   .hidden_mobile{display: none;}
   .mobile_srch_filter{display: block;margin-top: 12px;}
  .mobile_srch_filter .form-control{height: 44px;padding: 0 30px 0 35px;background-image: url(../images/search.svg);background-repeat: no-repeat;background-position: left 10px top 50%;}
.mobile_filterbtn{position: absolute;right: 15px;top: 10px;z-index: 99;}
.feed_pnltotal{padding-top: 140px}


   /* .header_bottom_menu {display: block;position: fixed;right: 0;top: 0;bottom: 0;width: 280px;background: linear-gradient(90deg, #384CFF 0%, #7534FF 100%);box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25), 0px 2px 4px rgba(0, 0, 0, 0.1);padding: 70px 8px 15px;transform: translateX(100%);transition: 0.5s;z-index: 999;}
   .filter_open .header_bottom_menu{transform: translateX(0);}
   .filter_open .overlay{display: block;} */
   .bottom_menu_list > ul{overflow-x: auto;}
   /* .bottom_menu_list > ul > li{width: 100%;}
   .bottom_menu_list > ul > li > a >span{width: 100%;justify-content: space-between;display: flex;align-items: center;} */
   .bottom_menu_list > ul > li > a{white-space:nowrap;}
   .header_bottom_menu {padding-top: 10px;display: none;}
   .pnl_outer{margin: 0;}
   .pnl_leftpnl, .pnl_rightpnl, .pnl_mdlpnl{padding: 0;}
   .feed_pnltotal{padding-left: 0;padding-right: 0;}
   .pnl_leftpnl{width: 100%;margin-bottom: 20px;}
   .header_bottom_menu .cross_btn {background-color: var(--bs-color-FF);color: var(--bs-color-00);}
/* =============news-deed===================== */

/* --------job------- */
.pnl_mdlpnl.job_rightpnl {width: 100%;}
.job_modal .modal-dialog{padding: 0 10px;}
.job_modal .modal-header{padding: 24px 20px;}
.job_modal .event_tags ul{margin-bottom: 0;}
.prsnl_detailsArea{padding: 16px 0;}
.others_detailsArea {padding: 16px 0;}
.details_input_txt label{margin-bottom: 10px;}
.profile_user_info {flex-wrap: wrap;}
.user_infobx{width: 100% !important;}

/* --end--job--- */

/* -----course--details----- */
.course_bluebx{flex-wrap: wrap;}
.course_topbx{width: 100%;}
.course_rightbx{width: 100%;padding: 20px 0;}
.duration_outer{justify-content: flex-start;}
.course_bluebx .feedback_jointbtnOuter flex-wrap{justify-content: flex-start;}
.inner_leftpnl, .inner_rightpnl, .lessons_leftpnl, .lessons_rightpnl{width: 100%;padding: 0;}
.progress_courseOuter {margin-bottom: 20px;left: 0;right: 0;top: 70px;z-index: 9;display: block;}
.course_open .progress_courseOuter {position: fixed;}
.lessons_rightpnl {width: 100%; padding: 0; position: fixed; left: 0;right: 0; z-index: 100; bottom: 0; background-color: var(--bs-color-FF); transition: 0.5s;top: 100%;overflow-y: auto;padding: 0 10px;}
.course_open .lessons_rightpnl {top: 0;}
.back_progressOuter {display: flex;align-items: center;justify-content: space-between;flex-wrap: wrap;background-color: var(--bs-color-FF);
  box-shadow: 0 0 20px rgba(0,0,0,0.16);padding: 15px;}
.invite_progressouter {background-color: var(--bs-color-ED); height: 3px; position: relative;}
.invite_progress_bar { position: absolute;  left: 0;  top: 0;height: 2px;background-color: var(--bs-color-4E);}
.page_back a {font-size: 16px;color: var(--bs-color-00);font-weight: 600; display: inline-flex;align-items: center;}
.page_back a span {vertical-align: middle;display: inline-block;margin-right: 5px;}
.progress_text h5 {font-size: 16px; color:var(--bs-color-5A);}
.lessons_tablink {/* position: fixed; */left: 0; right: 0; bottom: 0;z-index: 9;}
.course_open .lessons_tablink {position: fixed;}
.lessons_tablink .nav-tabs {border-bottom: 0;background: var(--bs-color-4E);border-radius: 10px 10px 0px 0px; padding: 5px;}
.lessons_tablink.tab_links .nav-tabs .nav-link{border-radius: 10px;justify-content: center;}
.lessons_tablink.tab_links .nav-tabs .nav-item{padding: 0;width: 100%;}
.course_detailspnl_innr {padding: 130px 0 75px;}
.course_vdo, .course_vdopnl {height: 360px;}
.inner_leftpnl, .inner_rightpnl{padding: 0 10px;}
.inner_rightpnl{margin-top: 20px;}
/* -----course--details----- */

.profile_pnlOuter .left_menulist li a {white-space: nowrap;}
.profile_pnlOuter .left_menulist ul{gap: 20px;justify-content: flex-start;}
.prsnl_profile_pic {height: 90px;width: 90px;}
.personel_profile_area {gap: 16px;}
.profile_owner_text h2{font-size: 19px;}
.profile_owner_text h5{font-size: 14px;}
.left_user_info p{font-size: 14px;}
.refer_code{font-size: 13px;}
.portfolio_outer{flex-wrap: wrap;}
.profile_information .portfolio_outer .portfolio_pic{width: 24%;}
.info_mainhdng h2{font-size: 22px;}

.filter_btn{display: block !important;}
.filter_backouter{display: flex;align-items: center;justify-content: space-between;margin-bottom: 16px;}
.filter_backouter .back_btn{margin-bottom: 0;}
.filter_btn a{color: var(--bs-color-00);}
.filter_btn a i{font-size: 26px;}

/* ---start--blog-- */
.details_arw a {height: 50px;width: 50px;}
.blog_listing .col-lg-4 {margin-bottom: 30px;}
.content_item h4 {font-size: 18px;line-height: 28px;}
.details_para p, .details_para ul li {font-size: 14px;line-height: 24px;margin-bottom: 20px;}
.news_slider_sec {padding: 0 0 50px;}

/* ---start--blog-- */
/* --kit-digital--- */
.banner_content h1 br{display: none;}
.kit_para {padding-left: 0;}
.digital_price_outer {margin: 30px 0 0;}
.digital_price_outer .col-sm-6{margin-bottom: 20px;}
.kit_icon_outer .help_item{padding: 20px;}
.help_text h3{font-size: 18px;}
/* --end-kit-digital-- */
/* --seccess-stories--- */
.banner_area.seccess_bnr {padding-bottom: 240px;}
.quote_leftpnl > span{padding-left: 5px;}
.success_quote_outer{flex-wrap: wrap;}
.quote_leftpnl{width: 100%;order: 2;}
.quote_rightpnl{width: 100%;order: 1;}
.rewards_outer .col-6{margin-bottom: 30px;}
.rewards_outer .col-6:last-child{margin-bottom: 0;}
.page_hdng p{font-size: 16px;}
.ofr_pic{text-align: center;}
.ofr_row:nth-child(even) .col-lg-6 .ofr_textpnl .number_count {left: 20px;}
/* --end--success-stories--- */
/* --starting---- */
.starting_signup_process{padding: 30px 15px 0;}
.company_details_outer .pnl_mdlpnl {width: 100%;}

/* --company-details--start-- */
.esg_hdng h2 {font-size: 30px;line-height: 38px;}
.esg_hdng {margin-bottom: 24px !important;}
.goals_rightpnl{margin-top: 30px;}
.ceo_textpnl {max-width: 100%;padding: 30px 0 0;}
.sustainability_row {padding: 40px 0;}
.esg_sub_heading h3 br{display: none;}
.esg_report_goals{padding-bottom: 30px;}
.challenges_learning_bx{padding: 30px 20px;}
.achievements_row.upcoming_row, .impact_stories_sec, .report_area, .achievements_row, .esg_contact_area{padding: 40px 0;}
.esg_sub_heading h3{font-size: 24px;}
.impct_slide_innr{flex-wrap: wrap;padding: 30px 16px;border-radius: 10px;}
.impact_quatetion {padding: 0;text-align: center;}
.impact_slide_item {width: 80vw;}
.summery_pic{margin-bottom: 30px;max-width: 100%;}
.summery_pic figure img{width: 100%;}
.goals_leftpnl > p{margin-bottom: 20px;}
.microsoft_summery_sec{padding: 20px 0 40px;}
.report_row h4{font-size: 16px;}
.impact_pic{margin: 0 auto 30px;}
/* --company-details--start-- */
.builder_lrightpnl{position: fixed;left: 0;right: 0;bottom: 0;max-height: 100%;height: 80vh;transform: translateY(100%);transition: 0.4s;background-color: var(--bs-color-FF);z-index: 1000;overflow-y: auto;}
.canvas_open .builder_lrightpnl{transform: translateY(0);}
.canvas_open{height: 100vh;overflow: hidden;}
.canvas_open .overlay{display: block;z-index: 9;}
.mob_ai_logo{display: block !important;height: 64px;width: 64px;position: fixed;right: 20px;bottom: 30px;z-index: 9;cursor: pointer;}
.upload_cvbx .profile_upload_bx{height: 300px;}
.filter_srch_item{display: none !important;}
.mob_float_challenges{display: block;}
.challenges_bttm_gap{padding-bottom: 80px;}
#create_challenge .modal-dialog{max-width: 80%;}
#create_challenge ul{flex-wrap: wrap;}
#create_challenge ul li{max-width: 33.33%;}
}
@media(max-width:767px){
    .banner_content h1 {font-size: 38px;line-height: 100%;}
    .brand_logoItem { padding: 0 10px;}
    .page_hdng h2 {font-size: 34px;line-height: 38px;}
    .tab_links .nav-tabs .nav-link{font-size: 15px;}
    .circle_icon{height: 60px;width: 60px;}
    .help_item{margin-bottom: 30px;}
    .review_slideItem{width: 56vw;}
    .accordian_area .accordion-button{padding: 16px 0;font-size: 16px;}
    .faq_lftpnl{margin-bottom: 20px;}
    .page_hdng h2 br{display: none;}
    .ofr_icon span {font-size: 28px;}
    .ofr_icon{height: 55px;width: 55px;}
    /* =============news-deed===================== */
    .left_serch_logo{gap: 10px;}
    .event_slideItem {width: 66vw;}
    .people_slideItem { width: 50vw;}
    .header_serach{display: none;}
    .right_user_icon > ul{gap: 13px;}
    .post_bttm_menu ul li a{font-size: 13px;}
    .post_bttm_menu ul li a i{font-size: 18px;}
    .user_post_paragraph p{font-size: 14px;}
    .user_post_paragraph p {margin-bottom: 10px;}
    .user_post_paragraph span a{font-size: 14px;}
    .search_fill .form-control{height: 44px;font-size: 14px;}
    .search_btn {display: block;}
    .header_serach{position: absolute;left: 0;right: 0;top: 100%;background: linear-gradient(90deg, #384CFF 0%, #7534FF 100%);padding: 12px 16px;border-top: 2px solid rgba(227, 227, 238, 0.2);}
    .header_serach .form-control{padding-left: 28px;background-color: transparent;border: 0;box-shadow: none;color: var(
      --bs-color-FF);}
    .post_modal .modal-body {padding: 20px;}
    .post_modal .modal-header h3{font-size: 21px;}
    .pnl_mdlpnl{padding: 0;}
    .srch_btn{left: 12px;color: var(--bs-color-FF);}
    .header_serach .form-control::placeholder{color: var(--bs-color-FF);}
    .header_serach .form-control::-webkit-placeholder{color: var(--bs-color-FF);}


     /* =============news-deed===================== */
     /* =============activities==================== */

     .notify_status { min-width: 65px;text-align: right;}
    .notify_text p{font-size: 13px;}
     /* =============activities==================== */

     /* -----groups-------- */
     .commnets_reply{padding-left: 0;}
     /* -----groups-------- */

     /* -----events------ */
     .product_row {flex-wrap: wrap;gap: 0;}
     .product_pic{margin: 0 auto 20px;}
     .product_info { width: 100%;}
     .product_list ul{flex-wrap: wrap;gap: 10px;}
     /* -----events------ */
     .process_hdng h3{font-size: 22px;line-height: 32px;}
     .process_hdng p{font-size: 14px;}
     .process_modal .modal-body {padding: 16px 16px 20px;}
     .form_innr .form-group label{font-size: 14px;}

     /* --jobs---- */
     .progres_tagsouter{flex-wrap: wrap;}
     .tags_lftpnl { width: 100%;margin-bottom: 16px;}
     .tags_prgrsspnl{width: 100%;}
     .progess_outer{min-width: 180px;}
     .event_tags ul{flex-wrap: wrap;}
     .modal_hdng h3{font-size: 18px;}
       /* --jobs---- */
    .lesson_detailsHdng h2{font-size: 20px;line-height: 30px;}
    .task_row{flex-wrap: wrap;}
    .task_left{width: 100%;}
    .task_right{width: 100%;margin: 20px 0 0;display: flex;align-items: center;justify-content: space-between;}
    .task_left p{font-size: 14px;line-height: 24px;}
    .scorebx_outer{flex-wrap: wrap;justify-content: center;}
    .scorebx_item {width: 48%;}
    .course_skill{display: block;}
    .course_add_btn{margin: 10px 0 0;}
    .course_add_btn a{display: inline-flex;}
    .duration_outer{display: block;}
    .subscribe_text{margin-top: 10px;}
    .back_progressOuter{padding: 7px 10px;}
    .progress_courseOuter{top:67px}
    /* -===================================== */
    .profile_pnlOuter .profile_top_row{flex-wrap: wrap;justify-content: center;}
    .profile_pnlOuter .personel_profile_area{width: 100%;flex-wrap: wrap;justify-content: center;margin-bottom: 20px;}
    .profile_pnlOuter .personl_profiledetails{width: 100%;}
    .profile_pnlOuter .profile_owner_outer{justify-content: center;flex-wrap: wrap;}
    .profile_pnlOuter .profile_social ul{justify-content: center;}
    .profile_pnlOuter .profile_owner_text, .profile_owner_editbtn{width: 100%;text-align: center;margin: 0 auto;}
    .profile_pnlOuter .profile_owner_editbtn span{margin: 10px auto 0;}
    .edit_input.input_fill{max-width: 300px;margin: 0 auto;}
    .editname_bx .edit_send span{margin: 0 auto;}
    .editname_bx .edit_send{margin: 10px auto 0;}
    .profile_info_pnl .profile_info_row{flex-wrap: wrap;}
    .profile_info_row > label{width: 100%;}
    .profile_bx_item {width: 100%;}
    .profile_bxOuter{flex-wrap: wrap;}
    .profile_information .portfolio_outer .portfolio_pic {width: 32%;}
    .left_profile_info .form-control, .left_profile_info > span{font-size: 14px;}
    .right_user_info span {height: 50px; width: 50px;min-width: 50px;overflow: hidden;}
    .left_profile_info ul{flex-wrap: wrap;gap: 16px;}
    .left_profile_info input.form-control{height: 42px;}
    .edit_icons span{font-size: 19px;}
    /* --start--blog--- */
    .blog_pic a{max-height: 100%;height: auto;border-radius: 10px;}
    .blog_content a{font-size: 19px;}
    .blog_landing_total {padding: 130px 0 30px;}
    .landing_hdng_row{margin-bottom: 30px;}
    .details_arw a {height: 40px;width: 40px;}
    .inBtns .slick-arrow {height: 44px;width: 44px;}
    .inBtns.news_arws {margin: 0;top: -70px;}
    
    /* --start--blog--- */

    /* --kit--digital--- */
  .digital_srvc_row {flex-wrap: wrap;}
  .kit_srvc_leftpnl, .kit_srvc_rightpnl {width: 100%;}
  .kit_srvc_rightpnl li{font-size: 15px;}
  .digital_modal .modal-body{flex-wrap: wrap;}
  .digital_left{width: 100%;}
  .digital_right{padding: 30px 20px;width: 100%;}

  /* --kit--digital--- */

  /* --success-stories--- */
  .quote_rightpnl img{max-width: 170px;}
  .quote_leftpnl > span{display: inline-block;width: 40px;margin-bottom: 10px;}
  .quote_leftpnl p {font-size: 16px;line-height: 28px;}
  .quote_author {margin-top: 30px;}
  .success_testimonial_bx .slick-dots{margin-top: 30px;}
  .success_item{padding: 20px;}
  .success_stories_details {padding: 115px 0 60px;}
  .success_list_bx > h3{font-size: 21px;}
  .success_list {padding: 30px 20px;border-radius: 15px;}
  .success_list ol li{font-size: 15px;}
  .success_list_bx {margin-top: 30px;}
  .testimonial_slideouter .inBtns.news_arws{position: static;top: inherit;margin: 30px 0 0;}
  .number_count{height: 130px;width: 130px;top: -15px;}
  .success_faq_rightpnl .accordion-button > span{font-size: 16px;}
  .success_faq_rightpnl .accordion-body p{font-size: 14px;}
  .talent_btn {margin-top: 30px;}
  .faq_leftpnl{margin-bottom: 30px;}
/* --success-stories--- */
/* --starting---process--- */
  .process_type_outer {flex-wrap: wrap;}
  .starting_type .large_container{height: 100%;}
  .process_type_bx{width: 48%;} 
  /* --starting---process--- */
  /* --comany-details--page--- */
  .esg_items {flex-wrap: wrap;}
  .esg_leftpnl{width: 100%;margin-bottom: 16px;}
  .esg_rightpnl {width: 100%;justify-content: space-between;}
  .esg_hdng h2 {font-size: 26px;line-height: 32px;}
  /* --comany-details--page--- */
  .process_modal.builder_modal .modal-header {padding: 24px 16px 0;}
  .gray_bx_outer{flex-wrap: wrap;justify-content: space-between;}
  .gray_bx_item{width: 49%;}

  .catagories_list li{width: 33.33%;}
  .catagories_list ul{row-gap: 20px;}
  .product-slider-container .col-2{display: none;}
  .product-slider-container .col-10{width: 100%;}
  .project_wrap{flex-wrap: wrap;}
  .pnl_outer > .row{margin: 0;}

  .ofr_item {width: 100%;}

  #create_challenge .modal-dialog { max-width: 95%; }

}
@media(max-width:575px){
    .banner_content h1 {font-size: 38px;line-height: 110%;}
    .banner_content p {font-size: 16px;}
    .page_hdng h2 {font-size: 28px;line-height: 38px;}
    .review_slideItem {width: 75vw;}
    .input_filOuter{flex-wrap: wrap;gap: 0;}
    .contact_form_area .form-group.description_fill .form-control {padding: 10px 16px;border-radius: 12px;height: 100px;resize: none;}
    .page_hdng {margin-bottom: 30px;}
    .contact_form_area .form-group .form-control{padding: 16px;}
    .ofr_hdng h3 {font-size: 21px;line-height: 27px;}
    .bnr_btns .btn, .talent_btn .btn, .system_btn  .btn, .submit_btn .btn{height: 50px;}
  /* =============news-deed===================== */
    .post_modal .modal-header { padding: 20px 20px 0; border: 0;}
    .post_modal .modal-header .btn-close{height: 34px;width: 34px;}
    .post_modal .modal-header .btn-close span{font-size: 18px;}
    .post_list .post_bttm_menu ul li a{height: 36px;}
    .gretings_hdng h3{font-size: 19px;}
    .date_item .form-control{height: 38px;}
    .post_list .post_bttm_menu ul li a{gap: 2px;}
    /* =============news-deed===================== */

    /* ===people===== */
    .people_profile{height: 50px;width: 50px;margin: 0 auto 20px;}
    .people_row{flex-wrap: wrap;}
    .left_people{width: 100%;margin-bottom: 20px;flex-wrap: wrap;gap: 0;}
    .people_profile_info{width: 100%;text-align: center;}
    .right_people_btn {text-align: center;width: 100%;}
    .left_people .event_tags ul {justify-content: center;}
    .srvc_top{flex-wrap: wrap;margin-bottom: 15px;}
    .srvc_top h3{width: 100%;margin-bottom: 10px;}
    .srvc_top .jb_rating {display: inline-flex;}
    .team_userOuter{flex-wrap: wrap;}
    .team_usrTxt {padding-left: 0;padding-top: 10px;}
    .filter_srch_item{flex-wrap: wrap;}
    .filter_search_wrap {order: 2;}
    .filter_refresh_btn {order: 1;justify-content: flex-end;min-width: 100%;padding-top: 10px;}
    .header_inner li.dropdown.notification .dropdown-menu{min-width: 335px;right: -40px !important;}


 /* ===people===== */

 /* ===Groups== */
 .grp_pic_txtInnr{flex-wrap: wrap;}
 .left_group, .groups_imgbx_pnl{width: 100%;}
 .groups_imgbx_pnl{margin-top: 20px;}
 .group_pic {height: 260px;width: 100%;}
.top_company_row{flex-wrap: wrap;}
 .top_company_row .event_hdng{width: 100%;}
.top_company_row .event_tags{width: 100%;}
.uploaded_innrBx, .upload_btn{width: 120px;}
.share_text{display: none !important;}
.share_social_row {margin-bottom: 16px;padding-bottom: 16px;border-bottom: 1px solid var(--bs-color-DC);}
 /* ===Groups== */
 .rvw_rating_bx{flex-wrap: wrap;}
 .left_rvwpnl, .right_rvwpnl {width: 100%;}
 .add_rvw_btn {margin: 16px 0 0;}
.left_rvwpnl{margin-bottom: 16px;}
.review_commnts .forum_row{padding: 16px 0;}
.forum_para p{font-size: 14px;}
.contact_header{padding: 16px;}
.course_vdo, .course_vdopnl {height: 280px;}
.course_icon {height: 35px;width: 35px;min-width: 35px;}
.course_icon i{font-size: 18px;}
.course_topbx h3{font-size: 19px;}
.add_btns {margin-top: 20px;}
.add_btns .btn{width: 100%;}
.account_user_outer{flex-wrap: wrap;}
.account_usr{width: 60px;height: 60px;min-width: 60px;margin: 0 auto;}
.account_usr_info{text-align: center;}
.profile_information  .portfolio_outer .portfolio_pic {width: 48%;}
.add_anotherBtn{font-size: 14px;}
.info_mainhdng h2 {font-size: 20px;}
/* --end-group--- */
/* --start-blog-- */
.news_date_info > label {padding: 0;margin: 0 0 10px;border: 0; width: 100%;}
.news_date_info{flex-wrap: wrap;}
.details_para ul{padding: 20px 16px;border-radius: 10px;}

/* end--blog--- */
/* --digital-kit--- */
.price_text_area p{min-height: auto;font-size: 15px;}
.digital_price_outer .col-sm-6:last-child, .kit_icon_outer .col-lg-4:last-child{margin-bottom: 0;}
.kit_srvc_text p{font-size: 15px;}
.rewards_outer .col-sm-6{margin-bottom: 20px;}
.rewards_outer .col-sm-6:last-child{margin-bottom: 0;}
.rewards_point_item p br{display: none;}
.success_testimonial_bx{padding: 30px 16px;border-radius: 10px;}
.digital_modal .modal-dialog{padding: 0;}
.digital_right .input_fill .form-control{height: 50px;border-radius: 10px;font-size: 14px;}
.digital_left{padding: 48px 20px;}
.digital_left h4 {font-size: 20px;line-height: 28px;margin-bottom: 16px;}
.digital_left h2 {font-size: 36px;}
.digital_right .input_fill.description_fill .form-control{height: 100px;}
.digital_modal .close{height: 30px;width: 30px;}
.digital_modal .close i{font-size: 18px;}
.digital_success_modal .modal-body{padding: 30px;}
.kit_success h2{font-size: 35px;margin-bottom: 16px;line-height: 100%;}
.kit_rewards_icon{margin-bottom: 16px;}
.kit_success p{font-size: 16px;line-height: 28px;}
.success_stories_details {padding: 90px 0 60px;}
/* --digital-kit--- */
/* --starting--process-- */
.process_type_bx{width: 100%;} 
.adress_tags{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
/* --starting--process-- */
/* --company-details----- */
.esg_hdng h2{font-size: 24px;}
.ceo_row{padding: 30px 16px;}
.esg_sub_heading p{margin-top: 12px;}
.impact_row {flex-wrap: wrap;}
.left_impact_info{width: 100%;}
.right_impact_icons {width: 100%;}
.right_impact_icons{margin-top: 30px;}
.icons_info_row {flex-wrap: wrap;}
.esg_canvas .container{padding: 0;}
.report_area, .esg_contact_area{padding: 40px 16px;}
.report_row {flex-wrap: wrap;}
.report_row h4{width: 100%;}
.doc_link {flex-wrap: wrap;}

/* =============================== */
.build_usrleft_pnl{flex-wrap: wrap;}
.build_usrinfo h3 {font-size: 22px; line-height: 36px;}
.portfolio_links li a{max-width: 250px;}
.mob_ai_logo{height: 50px;width: 50px;}

.catagories_list li{width: 50%;}
.catagories_list li:nth-child(3)::before, .catagories_list li:nth-child(5)::before{display: none;}
.img-container { height: auto; }
.product-details-info li > span:first-child { min-width: 100%; margin-bottom: 10px; }
.modal-body .people_profile{margin: 0;}
.modal-body .people_profile_info{width: auto;}
.tags ul{justify-content: center;}
.upload_area{row-gap: 10px;}

#create_challenge ul{justify-content: space-between;}
#create_challenge ul li { max-width: 48.5%; }
.create_icon{height: 100px !important;width: 100px !important;}

.modal-header.bg-light.border-0 .btn-close{position: absolute;}

}
@media(max-width:480px){
    .input_fill .form-control {height: 44px;font-size: 14px; padding: 0 20px;}
    .rvw_para p {font-size: 14px;line-height: 24px;}
    .user_name h4 {font-size: 16px;line-height: 26px;}
      /* =============news-deed===================== */
    .event_slideItem { width: 90vw;}
    .details_btn .btn{width: 100%;}
      /* =============news-deed===================== */
      .scorebx_item, .exp_logo_details {width: 100%;}
      .left_exp_info{flex-wrap: wrap;}

}
@media(max-width:420px){
    .help_text h3 {font-size: 20px;line-height: 24px;margin-bottom: 10px;}
    .page_hdng h2 {font-size: 30px;line-height: 40px;}
    .review_slideItem {width: 85vw;}
    .friend_results_bx {height: calc(100vh - 290px);}
    .gray_bx_item {width: 48%;}
    .gray_bx_item h3{font-size: 13px;}
    .cart-quantity{flex-wrap: wrap;row-gap: 15px;}
    .field-container{width: 100%;}
    .cart-quantity .btn{width: 100%;}
}
/*****schedule-meeting*****/
#scheduleMeetingTab{gap: 76px;padding: 0 30px 0 60px;overflow-x: auto;}
#scheduleMeetingTab li a{position: relative;background-color: #fff;color: #333;}
#scheduleMeetingTab li a.active{color: var(--bs-color-38);}
#scheduleMeetingTab li a::before{content: '';display: block;width: 0;height: 0;border-top: 29px solid #ffffff;border-right: 29px solid #ffffff;border-bottom: 29px solid #ffffff;border-left: 29px solid transparent;position: absolute;right: 100%;top: 0;bottom: 0;}
#scheduleMeetingTab li a::after{content: '';display: block;width: 0;height: 0;border-top: 29px solid transparent;border-bottom: 29px solid transparent;border-left: 29px solid #ffffff;position: absolute;left: 100%;top: 0;bottom: 0;}
/*****schedule-meeting*****/