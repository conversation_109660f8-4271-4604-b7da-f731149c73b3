import React from 'react';
import { Link } from 'react-router-dom';


const ConversationRightContactMailPanel = () => {
  return (
    <>
    {/* polulate the chat data */}
    <div className="chating_mdlPnl">
        <div className="chatting_innr pd_top_60">
          <div className="msg_outer">

            {/* chat from my contact person with uploads */}
            <div className="msg_row reply_msgRow">
              <div className="chat_infoLft">
                <div className="msg_usr">
                  <img src="images/people1.png" alt="" />
                </div>
                <div className="chat_info_timeOuter">
                  <h4>
                    <i className="material-icons-round">question_answer</i> Chat
                  </h4>
                  <h5>Thursday 12:02am</h5>
                </div>
              </div>
              <div className="chat_msgright">
                <div className="msg_text">
                  <p>Cun’t wait for our online baking class tonight 😁</p>
                </div>
                <div className="upload_picOuter">
                  <div className="upload_pic_item">
                    <figure>
                      <img src="images/pic1.png" alt="" />
                    </figure>
                  </div>
                  <div className="upload_pic_item multiple_media">
                    <figure>
                      <img src="images/pic1.png" alt="" />
                    </figure>
                    <div className="media_caption">
                      <Link to="#">5+</Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>


            {/* chat from loggedin user or my message*/}
            <div className="msg_row own_msgRow">
              <div className="chat_infoLft">
                <div className="chat_info_timeOuter">
                  <h4>
                    <i className="material-icons-round">question_answer</i> chat
                  </h4>
                  <h5>
                    Thursday 12:02am{' '}
                    <span className="msg_status double_tick seen_tick">
                      <i className="material-icons-round"> done_all</i>
                    </span>
                  </h5>
                </div>
              </div>
              <div className="chat_msgright">
                <div className="own_msgArea">
                  <p>I’m all prepared</p>
                </div>
                <div className="own_msgArea">
                  <p>
                    Be together whenever, with our free* all-in-one
                    communication app, complete with unlimited text, voice,
                    video calling and group video chat features. Make one for me
                    also
                  </p>
                </div>
              </div>
            </div>


  

            {/* when person is thinking or replying */}
            <div className="msg_row reply_msgRow">
              <div className="chat_infoLft">
                <div className="msg_usr">
                  <img src="images/people1.png" alt="" />
                </div>
                <div className="chat_info_timeOuter">
                  <div className="chat_dots">
                    <img src="images/dots-loading.gif" alt="" />
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
    </div>

    {/* chat message box */}
    <div className="chat_sendPnl">
        <div className="emoji_iconArea">
          <ul className="list_stye_none">
            <li>
              <Link to="#">
                <span className="material-icons-round">upload_file</span>
              </Link>
            </li>
          </ul>
        </div>
        <div className="send_textbx">
          <form>
            <input
              type="text"
              value=""
              className="form-control"
              placeholder="Enter your message"
            />
            <button type="submit" className="chat_send_btn">
              <span className="material-icons-round">send</span>
            </button>
          </form>
        </div>
    </div>


    </>


  )
}


export default ConversationRightContactMailPanel;