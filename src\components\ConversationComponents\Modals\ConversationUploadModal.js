/* eslint-disable */
import { useState } from "react";
import { Link } from "react-router-dom";

/*import url and gateway methods */
import { getData, postData, putData, uploadMultipleFile } from "utils/Gateway";
import * as url from "helper/UrlHelper";
import { assetImages } from "constants";

const ConversationUploadModal = ({
  uploadedFiles = [],
  setuploadedFiles = () => {},
  uploadedFileIds = [],
  setuploadedFileIds = () => {},
  modalMessageText = "",
  setmodalMessageText = () => {},
  sendMessageHandler = () => {},
  sendMessageByEnter = () => {},
  messageUpdating = false,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  // uploads media files to server and get uploaded file id and setUploadedFileId
  const loadingCircle = [1, 2, 3, 4, 5, 6, 7];

  const [isUploading, setisUploading] = useState(false);
  const [messageWarning, setmessageWarning] = useState(false);
  const [fileIsBig, setFileIsBig] = useState(false);

  //function upload multiple files
  const fileUploadHandler = async (e) => {
    try {
      setFileIsBig(false);
      setisUploading(true);

      const files = e.target.files;

      let fileProceed = false;

      if (files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          let f = files[i];

          if (f.size <= 12500000) {
            fileProceed = true;
          } else {
            fileProceed = false;
          }
        }
      }

      if (fileProceed) {
        const requestUrl =
          url.API_BASE_URL + url.API_MULTIPLE_FILE_UPLOAD + `?token=${token}`;

        const response = await uploadMultipleFile(requestUrl, files);

        setisUploading(false);

        // console.log(response);

        if (response.status) {
          /*----- assign uploaded files with path for display start ------*/
          const uploadedFileValues = response.data.map(
            ({ name, path, _id, ...other }) => ({
              name,
              path: url.SERVER_URL + path,
              filepath: path,
              _id,
            })
          );

          //check old file exist or not if exist then new file will concat with old file
          if (uploadedFiles.length == 0) {
            setuploadedFiles(uploadedFileValues);
          } else {
            let fileArr = uploadedFiles.concat(uploadedFileValues);
            setuploadedFiles(fileArr);
          }
          /*----- assign uploaded files with path for display end ------*/

          /*----- assign uploaded file ids start ------*/
          const fileIds = response.data.map(({ _id, ...other }) => ({ _id }));

          //check old file ids are exist or not.If exist then new id will concat with old id
          if (uploadedFileIds.length == 0) {
            setuploadedFileIds(fileIds);
          } else {
            let fileArr = uploadedFileIds.concat(fileIds);
            setuploadedFileIds(fileArr);
          }
          /*----- assign uploaded file ids end ------*/
          resetFile();
        }
      } else {
        setFileIsBig(true);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  // function for clear file value
  const resetFile = () => {
    const file = document.getElementById("uploadConversationFile");
    if (file) {
      file.value = null;
    } else {
      return;
    }
  };

  //function for delete file from upload stack
  const closeFileHandler = (index) => {
    const closedFile = [...uploadedFiles];
    const removedFileId = [...uploadedFileIds];

    closedFile.splice(index, 1);
    removedFileId.splice(index, 1);

    setuploadedFiles(closedFile);
    setuploadedFileIds(removedFileId);

    resetFile();
  };

  //function for enter message handler
  const modalEnterMessageHandler = (e) => {
    if (e.key === "Enter") {
      sendMessageByEnter(e);

      //hide modal
      let uploadFileModal = document.querySelector("#upload_modal");
      let modal = bootstrap.Modal.getInstance(uploadFileModal);
      modal.hide();

      resetUploadModal();
    }
  };

  //function for reset upload modal
  const resetUploadModal = () => {
    setuploadedFileIds([]);
    setuploadedFiles([]);
    setmodalMessageText("");
    resetFile();
  };

  return (
    <div className="chat_modalbx uloaded_viewModal">
      <div className="modal fade" id="upload_modal">
        <div className="modal-dialog" role="document">
          <div className="modal-content">
            <div className="modal-header mdl_hdng d-flex justify-content-end">
              {/* <h5 className="modal-title"></h5> */}
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetUploadModal}
              >
                <span className="material-icons-round">close </span>
              </button>
            </div>
            <div className="modal-body">
              <div className="upload_innrscroll">
                <div className="upload_innrBody">
                  <div className="upload_outer">
                    {/* <!-- ========== Start file Section ========== --> */}
                    <div className="row">
                      {uploadedFiles.map((file, index) => {
                        const fileExtension = file.path.split(".").pop();

                        return (
                          <div className="col-lg-3 col-md-4 col-6" key={index}>
                            <div className="chat_uploadItem">
                              {fileExtension === "pdf" ? (
                                <div className="chat_upload_pic">
                                  <img src={assetImages.PdfImg} alt="" />
                                </div>
                              ) : fileExtension === "jpg" ||
                                fileExtension === "jpeg" ||
                                fileExtension === "png" ? (
                                <div className="chat_upload_pic">
                                  <img src={file.path} alt="" />
                                </div>
                              ) : fileExtension === "xlsx" ||
                                fileExtension === "xlsm" ||
                                fileExtension === "xlsb" ||
                                fileExtension === "xltx" ||
                                fileExtension === "xls" ? (
                                <div className="chat_upload_pic">
                                  <img src={assetImages.XlsImg} alt="" />
                                </div>
                              ) : (
                                <div className="chat_upload_pic">
                                  <img src={assetImages.DocImg} alt="" />
                                </div>
                              )}
                              <div className="chat_upload_caption">
                                <p>
                                  <span>{file.name}</span>
                                </p>
                                <Link
                                  to="#"
                                  className="upoad_cross"
                                  onClick={() => closeFileHandler(index)}
                                >
                                  <i className="material-icons-round">close</i>
                                </Link>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    {/* <!-- ========== End file Section ========== --> */}
                  </div>
                </div>
              </div>

              {/* ----- loading area for file uploading ----- */}
              {isUploading && (
                <div className="d-flex align-items-center justify-content-center mb-1">
                  <h6 className="text-white fw-bold">Uploading Files</h6>
                  {loadingCircle.map((count, index) => {
                    return (
                      <div
                        key={index}
                        className="spinner-grow mx-1 text-white"
                        style={{ height: "6px", width: "6px" }}
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    );
                  })}
                </div>
              )}
              {/* ----- loading area for file uploading end----- */}

              {/* ----- big file message area ----- */}
              {fileIsBig && (
                <div className="d-flex align-items-center justify-content-center">
                  <p className="text-light">
                    * Uploaded File Size must be lower than 10 mb
                  </p>
                </div>
              )}
              {/* ----- big file message area end ----- */}

              {/* ----- warning message area  ----- */}
              {messageWarning && (
                <div className="d-flex align-items-center justify-content-center mb-1">
                  <p className="text-warning">* Please enter a message</p>
                </div>
              )}
              {/* ----- warning message area end ----- */}

              <div className="upload_footerArea">
                <div className="upload_footerInnr">
                  <div className="upload_input_msg">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter your message"
                      value={modalMessageText}
                      onChange={(e) => setmodalMessageText(e.target.value)}
                      onKeyDown={modalEnterMessageHandler}
                      disabled={isUploading ? true : false}
                    />
                  </div>

                  {/* ------ sending message display ----- */}
                  {messageUpdating && (
                    <div className="d-flex align-items-center justify-content-center mb-3">
                      <h6 className="text-white fw-bold">Sending Message</h6>
                      {loadingCircle.map((circle, index) => {
                        return (
                          <div
                            key={index}
                            className="spinner-grow mx-1 text-white"
                            style={{ height: "6px", width: "6px" }}
                            role="status"
                          >
                            <span className="visually-hidden">Loading...</span>
                          </div>
                        );
                      })}
                    </div>
                  )}
                  {/* ------ sending message display ----- */}

                  {/* <!-- ========== Start send Section ========== --> */}
                  <div className="addsend_btnOuter">
                    <button className="upload_addBtn">
                      <i className="material-icons-round"> add</i>
                      <input
                        id="uploadConversationFile"
                        type="file"
                        className="upload_fileBtn"
                        multiple
                        onChange={fileUploadHandler}
                      />
                    </button>
                    <button
                      type="button"
                      className="upload_sendBtn"
                      data-bs-dismiss="modal"
                      onClick={sendMessageHandler}
                      disabled={isUploading ? true : false}
                    >
                      <i className="material-icons-round"> send</i>
                    </button>
                  </div>
                  {/* <!-- ========== End send Section ========== --> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConversationUploadModal;
