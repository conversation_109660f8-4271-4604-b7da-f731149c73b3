/* eslint-disable */
import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import Select from "react-select";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { postData, getData } from "utils/Gateway";

import { getConvertedDateText } from "helper/EventHelper/EventHelper";
import MeetingReasonOptions from "Data/MeetingReason.json";
import { getHourFromTime } from "helper/Common/CommonHelper";

const AppointmentModal = ({
  personName = "",
  meetingStartTime,
  setMeetingStartTime,
  meetingEndTime,
  setMeetingEndTime,
  meetingDuration,
  setMeetingDuration,
  selectedAgendaId,
  setSelectedAgendaId,
  setSelectedMeetingDate,
}) => {
  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [meetingDate, setMeetingDate] = useState("");

  const [meetingReasonValue, setmeetingReasonValue] = useState(null);
  const [meetingReasonString, setmeetingReasonString] = useState("");
  const [commentMessage, setcommentMessage] = useState("");
  const [meetingDetails, setMeetingDetails] = useState("");
  const [meetingSaving, setMeetingSaving] = useState(false);

  const [reasonError, setreasonError] = useState(false);
  const [commentError, setcommentError] = useState(false);

  // get agenda details
  const getAgendaDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_DETAILS_AGENDA +
        `/${selectedAgendaId}?token=${token}`;

      const response = await getData(requestUrl);

      if (response.status) {
        setMeetingDuration(getHourFromTime(response.data.duration));
        setMeetingDate(response.data.date);
        setSelectedMeetingDate(response.data.date);
        setMeetingStartTime(response.data.starttime);
        setMeetingEndTime(response.data.endtime);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const scheduleMeetingHandler = async () => {
    setMeetingSaving(true);
    try {
      let scheduleData = {
        reciever: params.id,
        meetingdate: meetingDate,
        starttime: meetingStartTime,
        endtime: meetingEndTime,
        message: commentMessage,
        meetingdetails: meetingDetails,
        meetingreason: meetingReasonString,
      };

      let requestUrl =
        url.API_BASE_URL + url.API_SAVE_SCHEDULE_MEETING + `?token=${token}`;

      const response = await postData(requestUrl, scheduleData);

      console.log(response);

      setMeetingSaving(false);

      if (response.status) {
        //hide login modal
        let loginModal = document.querySelector("#appointment_meet_modal");
        let modal = bootstrap.Modal.getInstance(loginModal);
        modal.hide();

        setMeetingDetails("");

        //show register modal
        let myModal = new bootstrap.Modal(
          document.getElementById("schedule_confirm")
        );
        myModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const resetHandler = () => {
    setMeetingDetails("");
    setmeetingReasonValue(null);
    setmeetingReasonString("");
    setcommentMessage("");
    setreasonError(false);
    setcommentError(false);
    setSelectedAgendaId(null);
    setMeetingStartTime("");
    setMeetingEndTime("");
    setMeetingDuration("");
    setMeetingDate("");
    setSelectedMeetingDate("");
  };

  useEffect(() => {
    if (selectedAgendaId) {
      getAgendaDetails();
    }
  }, [selectedAgendaId]);

  return (
    <div className="post_modal request_modal">
      <div
        className="modal fade"
        id="appointment_meet_modal"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog modal-lg">
          <div className="modal-content rounded-3 overflow-hidden">
            {/* Header */}
            <div className="modal-header border-0 p-4 bg-light">
              <h5 className="modal-title d-flex align-items-center gap-2 fw-bold">
                <span
                  className="material-icons-outlined text-primary"
                  style={{ fontSize: "22px" }}
                >
                  calendar_month
                </span>
                Schedule Meeting
              </h5>
              <button
                type="button"
                className="btn-close bg-primary text-white"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <span className="material-icons-outlined">close</span>
              </button>
            </div>

            {/* Body */}
            <div className="modal-body p-4">
              <div className="row g-4">
                {/* Left Column */}
                <div className="col-sm-5 border-end pe-4">
                  <h5 className="fw-bold text-primary mb-3">{personName}</h5>
                  <h6 className="fw-bold mb-4">
                    {meetingDuration} Hours Meeting
                  </h6>

                  <div className="mb-3 p-3 bg-white rounded shadow-sm">
                    <div className="d-flex align-items-center gap-2 mb-2">
                      <span className="material-icons-outlined text-primary">
                        schedule
                      </span>
                      <strong>{meetingDuration} Hrs</strong>
                    </div>
                    <div className="d-flex align-items-center gap-2 mb-2">
                      <span className="material-icons-outlined text-primary">
                        calendar_month
                      </span>
                      <span>
                        {meetingStartTime} - {meetingEndTime},{" "}
                        {getConvertedDateText(meetingDate)}
                      </span>
                    </div>
                    <div className="d-flex align-items-center gap-2">
                      <span className="material-icons-outlined text-primary">
                        language
                      </span>
                      <span>India Standard Time</span>
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="col-sm-7">
                  <h5 className="fw-bold mb-4">Enter Details</h5>

                  {/* Name */}
                  <div className="mb-3">
                    <label className="form-label fw-semibold">
                      {userInfo?.name} {userInfo?.surname}
                    </label>
                  </div>

                  {/* Email */}
                  <div className="mb-3">
                    <label className="form-label fw-semibold">
                      {userInfo?.email}
                    </label>
                  </div>

                  {/* Reason of meet */}
                  <div className="mb-3">
                    <label className="form-label fw-bold">Reason of meet</label>
                    <Select
                      placeholder="Select reason of meet"
                      options={MeetingReasonOptions}
                      isClearable
                      value={meetingReasonValue}
                      onChange={(val) => {
                        setreasonError(false);
                        setmeetingReasonValue(val);
                        setmeetingReasonString(val?.value || "");
                      }}
                    />
                    {reasonError && (
                      <small className="text-danger d-flex align-items-center mt-1">
                        <span className="material-icons-outlined me-1">
                          info
                        </span>
                        Please select reason
                      </small>
                    )}
                  </div>

                  {/* Comments */}
                  <div className="mb-3">
                    <label className="form-label fw-bold">Comments</label>
                    <textarea
                      className="form-control shadow-sm"
                      placeholder="Comment to meet"
                      value={commentMessage}
                      onChange={(e) => {
                        setcommentMessage(e.target.value);
                        setcommentError(false);
                      }}
                    ></textarea>
                    {commentError && (
                      <small className="text-danger d-flex align-items-center mt-1">
                        <span className="material-icons-outlined me-1">
                          info
                        </span>
                        Please enter comment
                      </small>
                    )}
                  </div>

                  {/* Meeting Details */}
                  <div className="mb-3">
                    <label
                      htmlFor="meetingDetails"
                      className="form-label fw-bold"
                    >
                      Meeting Details
                    </label>
                    <textarea
                      id="meetingDetails"
                      className="form-control shadow-sm"
                      rows="4"
                      placeholder="Enter details"
                      value={meetingDetails}
                      onChange={(e) => setMeetingDetails(e.target.value)}
                    ></textarea>
                  </div>
                </div>
              </div>

              {/* Footer Buttons */}
              <div className="d-flex justify-content-between pt-4 mt-4 border-top">
                <button
                  className="btn btn-outline-secondary"
                  data-bs-dismiss="modal"
                  onClick={resetHandler}
                >
                  Back
                </button>
                <button
                  className="btn btn-primary px-4"
                  disabled={meetingSaving}
                  style={{ cursor: meetingSaving ? "not-allowed" : "pointer" }}
                  onClick={scheduleMeetingHandler}
                >
                  Schedule Meeting
                  {meetingSaving && (
                    <div
                      className="mx-2 spinner-border spinner-border-sm"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentModal;
