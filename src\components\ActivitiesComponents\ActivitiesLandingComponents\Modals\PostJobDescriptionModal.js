import React from 'react';
import { Link } from 'react-router-dom';

const PostJobDescriptionModal = () => {
  return (
    <div className="post_modal">
      <div className="modal fade" id="create_job_commnts_mdl">
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="modal-title" id="exampleModalLabel">
                Create Post
              </h3>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <span className="material-icons-outlined">close</span>
              </button>
            </div>
            <div className="modal-body">
              <div className="post_left">
                <div className="pofile_pic">
                  <img src="/images/profile-pic5.png" alt="" />
                </div>
                <div className="pofile_info">
                  <h3><PERSON></h3>
                  <Link to="#">
                    <i className="material-icons">public</i>
                    <span>Public</span>
                  </Link>
                </div>
              </div>
              <div className="post_description">
                <textarea
                  className="form-control"
                  placeholder="Add the description you are hiring for"
                ></textarea>
                <div className="editor">
                  <img src="images/editor.png" alt="" />
                </div>
              </div>

              <div className="post_btn">
                <button
                  className="btn"
                  data-bs-toggle="modal"
                  data-bs-dismiss="modal"
                  data-bs-target="#job_question_mdl"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostJobDescriptionModal;
