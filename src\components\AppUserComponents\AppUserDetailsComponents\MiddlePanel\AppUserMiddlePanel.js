/* eslint-disable */
import React, { useState } from "react";
import { Link } from "react-router-dom";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

import OverviewTab from "./Tabs/OverviewTab/OverviewTab";
// import PortfolioTab from "./Tabs/PortfolioTab/PortfolioTab";
// import ServicesTab from "./Tabs/ServicesTab/ServicesTab";
// import SurveyTab from "./Tabs/SurveyTab/SurveyTab";
import ReviewsTab from "./Tabs/ReviewsTab/ReviewsTab";
import AppointmentTab from "./Tabs/AppointmentTab/AppointmentTab";

const AppUserMiddlePanel = ({
  reviewTabReload = false,
  setReviewTabReload = () => {},
}) => {
  const { t } = useTranslation(); // translation marker

  // Define all possible tabs
  const appUserTabs = [
    { id: "overview", label: t("Overview"), component: OverviewTab },
    { id: "appointments", label: t("Appointments"), component: AppointmentTab },
    { id: "reviews", label: t("Reviews"), component: ReviewsTab },
  ];

  const [activeTab, setActiveTab] = useState(appUserTabs[0].id);

  /**
   * Handle tab selection
   * @param {string} tabId - ID of the selected tab
   */
  const tabSelectionHandler = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="pnl_mdlpnl" style={{ width: "70%" }}>
      <div className="people_item">
        <div className="tab_links people_tablinks">
          <ul className="nav nav-tabs list_stye_none">
            {appUserTabs.map((tab) => (
              <li className="nav-item" key={tab.id}>
                <Link
                  className={`nav-link ${
                    activeTab === tab.id ? "active show" : ""
                  }`}
                  to="#"
                  onClick={() => {
                    tabSelectionHandler(tab.id);
                  }}
                >
                  {tab.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="tab-content">
          {appUserTabs.map((tab) => {
            const TabComponent = tab.component;
            return (
              <div
                key={tab.id}
                className={`tab-pane fade ${
                  activeTab === tab.id ? "show active" : ""
                }`}
              >
                <TabComponent
                  tabName={activeTab === tab.id ? tab.id : ""}
                  reviewTabReload={reviewTabReload}
                  setReviewTabReload={setReviewTabReload}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AppUserMiddlePanel;
