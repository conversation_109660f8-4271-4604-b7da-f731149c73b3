/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import { assetImages } from "constants";
import { getDateFormatInSpanish } from "helper/Common/CommonHelper";

const ViewPublicDataModal = ({
  challengeType,
  responseCqId
}) => {

  const [fetchingProfileData, setFetchingProfileData] = useState(false);
  const [cqContent, setCqContent] = useState({});

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [cqid, setcqId] = useState(null);


  //** function for get cv details of logged in user */
  const getCqDetails = async () => {

    console.log('get Capital Quest Details in ViewPublicDate', cqid);

    try {
      setFetchingProfileData(true);
      setCqContent({});

      let requestURL =
        url.API_BASE_URL + url.API_CAPITALQUEST_DETAILS + `/${cqid}?token=${token}`;

      const response = await getData(requestURL);

      console.log("response in cq details", response);

      if (response.status && response.data) {
        setCqContent(response.data);
      }

      setFetchingProfileData(false);
    } catch (error) {
      console.log(error.message);
    }
  };

  // set cqid 
  useEffect(() => {
    console.log('responseCqId', responseCqId);
    if (responseCqId && challengeType === 'capitalquest') {
      setcqId(responseCqId);
    }
  }, [responseCqId]);

  // get capital quest details
  useEffect(() => {
    if (cqid && challengeType === 'capitalquest') {
      getCqDetails();
    }
  }, [cqid]);

  // Helper function to check if a section has content and is public
  const shouldDisplaySection = (sectionData, isPublic) => {
    if (!isPublic) return false; // Don't display private sections
    return !!sectionData; // Only display if there's content
  };

  return (
    <div className="post_modal">
      <div className="modal fade show" id="view_publicdata_CQ_modal">
        <div className="modal-dialog modal-xl">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Capital Quest: {cqContent?.startupname || 'Startup Pitch'}</h5>
              <button type="button" className="close" data-bs-dismiss="modal" aria-label="Close">
                <i className="material-icons-outlined">close</i>
              </button>
            </div>
            
            <div className="modal-body">
              {fetchingProfileData ? (
                <div className="text-center py-5">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2">Loading Capital Quest data...</p>
                </div>
              ) : (
                <div className="capital-quest-view">
                  {Object.keys(cqContent).length === 0 ? (
                    <div className="alert alert-info">
                      No public data available for this Capital Quest submission.
                    </div>
                  ) : (
                    <>
                      {/* Personal Information - only if public */}
                      {shouldDisplaySection(
                        cqContent.candidateName || cqContent.candidateEmail || cqContent.candidatePhone || cqContent.candidatePosition, 
                        cqContent.personalinformation
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Personal Information</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                {cqContent.profilepicture && (
                                  <div className="mb-3">
                                    <img 
                                      src={url.SERVER_URL + cqContent.profilepicture.path} 
                                      alt="Profile" 
                                      className="rounded-circle"
                                      style={{ width: '100px', height: '100px', objectFit: 'cover' }}
                                    />
                                  </div>
                                )}
                                <p><strong>Name:</strong> {cqContent.candidateName || 'N/A'}</p>
                                <p><strong>Email:</strong> {cqContent.candidateEmail || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Phone:</strong> {cqContent.candidatePhone || 'N/A'}</p>
                                <p><strong>Position:</strong> {cqContent.candidatePosition || 'N/A'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Executive Summary - only if public */}
                      {shouldDisplaySection(
                        cqContent.startupname || cqContent.startupdescription || cqContent.problemstatement || cqContent.solution, 
                        cqContent.executiveSummary
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Executive Summary</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Startup Name:</strong> {cqContent.startupname || 'N/A'}</p>
                                <p><strong>Website:</strong> {cqContent.startupwebsite || 'N/A'}</p>
                                <p><strong>Email:</strong> {cqContent.startupemail || 'N/A'}</p>
                                <p><strong>Tagline:</strong> {cqContent.startuptagline || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Description:</strong> {cqContent.startupdescription || 'N/A'}</p>
                                <p><strong>Problem Statement:</strong> {cqContent.problemstatement || 'N/A'}</p>
                                <p><strong>Solution:</strong> {cqContent.solution || 'N/A'}</p>
                                <p><strong>Unique Value Proposition:</strong> {cqContent.uniquevalueproposition || 'N/A'}</p>
                              </div>
                            </div>
                            <div className="row mt-3">
                              <div className="col-12">
                                <p><strong>Business Model:</strong> {cqContent.businessmodel || 'N/A'}</p>
                                <p><strong>Current Status:</strong> {cqContent.currentstatus || 'N/A'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Founding Team - only if public */}
                      {shouldDisplaySection(
                        cqContent.founder && cqContent.founder.length > 0,
                        cqContent.foundingTeam
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Founding Team</h6>
                          </div>
                          <div className="card-body">
                            {cqContent.founder && cqContent.founder.map((founder, index) => (
                              <div key={index} className="founder-card mb-3 p-3 border rounded">
                                <h6>{founder.name || 'Founder'} - {founder.role || 'Role not specified'}</h6>
                                <div className="row">
                                  <div className="col-md-6">
                                    <p><strong>About:</strong> {founder.about || 'N/A'}</p>
                                    <p><strong>Experience:</strong> {founder.experience || 'N/A'}</p>
                                    <p><strong>Skills:</strong> {founder.skills || 'N/A'}</p>
                                  </div>
                                  <div className="col-md-6">
                                    <p><strong>LinkedIn:</strong> {founder.linkedin || 'N/A'}</p>
                                    <p><strong>Equity Holding:</strong> {founder.equityholding || 'N/A'}</p>
                                    <p><strong>Notable Advisors:</strong> {founder.notableadvisors || 'N/A'}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Product/Service - only if public */}
                      {shouldDisplaySection(
                        cqContent.productdescription || cqContent.technology || cqContent.currentdevelopmentstage,
                        cqContent.aboutTheProductOrService
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Product/Service</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Product Description:</strong> {cqContent.productdescription || 'N/A'}</p>
                                <p><strong>Current Development Stage:</strong> {cqContent.currentdevelopmentstage || 'N/A'}</p>
                                <p><strong>Development Roadmap:</strong> {cqContent.productdevelopmentroadmap || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Technology:</strong> {cqContent.technology || 'N/A'}</p>
                                <p><strong>Intellectual Property:</strong> {cqContent.intellectualproperty || 'N/A'}</p>
                                <p><strong>Competitive Advantage:</strong> {cqContent.productcompetitiveadvantage || 'N/A'}</p>
                              </div>
                            </div>
                            
                            {/* Product Demo Links - only if there are any */}
                            {cqContent.productdemo && cqContent.productdemo.length > 0 && (
                              <div className="mt-3">
                                <h6>Product Demos:</h6>
                                <ul className="list-group">
                                  {cqContent.productdemo.map((demo, index) => (
                                    <li key={index} className="list-group-item">
                                      <a href={demo.url} target="_blank" rel="noopener noreferrer">
                                        {demo.name || demo.url}
                                      </a>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Market Analysis - only if public */}
                      {shouldDisplaySection(
                        cqContent.marketsize || cqContent.markettrends || cqContent.targetmarket || cqContent.competitors,
                        cqContent.marketAnalysis
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Market Analysis</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Market Size:</strong> {cqContent.marketsize || 'N/A'}</p>
                                <p><strong>Market Trends:</strong> {cqContent.markettrends || 'N/A'}</p>
                                <p><strong>Target Market:</strong> {cqContent.targetmarket || 'N/A'}</p>
                                <p><strong>Competitors:</strong> {cqContent.competitors || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Competitive Advantage:</strong> {cqContent.competitiveadvantage || 'N/A'}</p>
                                <p><strong>Entry Barriers:</strong> {cqContent.entrybarriers || 'N/A'}</p>
                                <p><strong>Differentiation Strategy:</strong> {cqContent.differentiationstrategy || 'N/A'}</p>
                                <p><strong>Customer Acquisition Strategy:</strong> {cqContent.customeracquisitionstrategy || 'N/A'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Traction and Metrics - only if public */}
                      {shouldDisplaySection(
                        cqContent.mainkpis || cqContent.customerevolution || cqContent.growthrate || cqContent.revenue,
                        cqContent.tractionAndMetrics
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Traction and Metrics</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Main KPIs:</strong> {cqContent.mainkpis || 'N/A'}</p>
                                <p><strong>Customer Evolution:</strong> {cqContent.customerevolution || 'N/A'}</p>
                                <p><strong>Growth Rate:</strong> {cqContent.growthrate || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Revenue:</strong> {cqContent.revenue || 'N/A'}</p>
                                <p><strong>CAC and CLTV:</strong> {cqContent.cacandcltv || 'N/A'}</p>
                                <p><strong>Strategic Partnerships:</strong> {cqContent.stategicpartnerships || 'N/A'}</p>
                              </div>
                            </div>
                            
                            {/* Customer Testimonials - only if there are any */}
                            {cqContent.customertestimonials && cqContent.customertestimonials.length > 0 && (
                              <div className="mt-3">
                                <h6>Customer Testimonials:</h6>
                                <ul className="list-group">
                                  {cqContent.customertestimonials.map((testimonial, index) => (
                                    <li key={index} className="list-group-item">
                                      "{testimonial}"
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Business Model - only if public */}
                      {shouldDisplaySection(
                        cqContent.revenuesources || cqContent.distrubutionchannels || cqContent.grossmargin || cqContent.scalabilityplan,
                        cqContent.businessModel
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Business Model</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Revenue Sources:</strong> {cqContent.revenuesources || 'N/A'}</p>
                                <p><strong>Distribution Channels:</strong> {cqContent.distrubutionchannels || 'N/A'}</p>
                                <p><strong>Gross Margin:</strong> {cqContent.grossmargin || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Scalability Plan:</strong> {cqContent.scalabilityplan || 'N/A'}</p>
                                <p><strong>Real-time Order Tracking:</strong> {cqContent.realtimeordertracking ? 'Yes' : 'No'}</p>
                              </div>
                            </div>
                            
                            {/* Pricing Strategy - only if there are any */}
                            {cqContent.pricingstrategy && cqContent.pricingstrategy.length > 0 && (
                              <div className="mt-3">
                                <h6>Pricing Strategy:</h6>
                                <div className="table-responsive">
                                  <table className="table table-bordered">
                                    <thead>
                                      <tr>
                                        <th>Plan</th>
                                        <th>Duration</th>
                                        <th>Price</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {cqContent.pricingstrategy.map((strategy, index) => (
                                        <tr key={index}>
                                          <td>{strategy.name}</td>
                                          <td>{strategy.duration}</td>
                                          <td>${strategy.price}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Funding Request - only if public */}
                      {shouldDisplaySection(
                        cqContent.fundingamount || cqContent.proposedvalue || cqContent.fundinguse || cqContent.expectedrunway,
                        cqContent.requestForFunding
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Funding Request</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Funding Amount:</strong> {cqContent.fundingamount || 'N/A'}</p>
                                <p><strong>Proposed Value:</strong> {cqContent.proposedvalue || 'N/A'}</p>
                                <p><strong>Funding Use:</strong> {cqContent.fundinguse || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Expected Runway:</strong> {cqContent.expectedrunway || 'N/A'}</p>
                                <p><strong>Milestones:</strong> {cqContent.milestones || 'N/A'}</p>
                              </div>
                            </div>
                            
                            {/* Previous Funding - only if there are any */}
                            {cqContent.previousfunding && cqContent.previousfunding.length > 0 && (
                              <div className="mt-3">
                                <h6>Previous Funding:</h6>
                                <div className="table-responsive">
                                  <table className="table table-bordered">
                                    <thead>
                                      <tr>
                                        <th>Date</th>
                                        <th>Company</th>
                                        <th>Amount</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      {cqContent.previousfunding.map((funding, index) => (
                                        <tr key={index}>
                                          <td>{new Date(funding.date).toLocaleDateString()}</td>
                                          <td>{funding.company}</td>
                                          <td>${funding.amount}</td>
                                        </tr>
                                      ))}
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Financial Projections - only if public */}
                      {shouldDisplaySection(
                        cqContent.revenueprojections || cqContent.breakevenpoint || cqContent.mainassumptions || cqContent.scenarios,
                        cqContent.financialProjections
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Financial Projections</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Revenue Projections:</strong> {cqContent.revenueprojections || 'N/A'}</p>
                                <p><strong>Break-even Point:</strong> {cqContent.breakevenpoint || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Main Assumptions:</strong> {cqContent.mainassumptions || 'N/A'}</p>
                                <p><strong>Scenarios:</strong> {cqContent.scenarios || 'N/A'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Exit Strategy - only if public */}
                      {shouldDisplaySection(
                        cqContent.potentialacquirers || cqContent.timeframe || cqContent.estimatereturn,
                        cqContent.exitStrategy
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Exit Strategy</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Potential Acquirers:</strong> {cqContent.potentialacquirers || 'N/A'}</p>
                                <p><strong>Timeframe:</strong> {cqContent.timeframe || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Estimated Return:</strong> {cqContent.estimatereturn || 'N/A'}</p>
                              </div>
                            </div>
                            
                            {/* Exit Sectors - only if there are any */}
                            {cqContent.exitinsector && cqContent.exitinsector.length > 0 && (
                              <div className="mt-3">
                                <h6>Exit Sectors:</h6>
                                <div className="d-flex flex-wrap gap-2">
                                  {cqContent.exitinsector.map((sector, index) => (
                                    <span key={index} className="badge bg-info">{sector}</span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Risks and Mitigation - only if public */}
                      {shouldDisplaySection(
                        cqContent.businessrisks || cqContent.mitigationstrategies || cqContent.swotanalysis,
                        cqContent.risksAndMitigation
                      ) && (
                        <div className="card mb-4">
                          <div className="card-header bg-light">
                            <h6 className="mb-0">Risks and Mitigation</h6>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Business Risks:</strong> {cqContent.businessrisks || 'N/A'}</p>
                                <p><strong>Mitigation Strategies:</strong> {cqContent.mitigationstrategies || 'N/A'}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>SWOT Analysis:</strong> {cqContent.swotanalysis || 'N/A'}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Display message if no public sections are found */}
                      {!(
                        (shouldDisplaySection(cqContent.candidateName || cqContent.candidateEmail || cqContent.candidatePhone || cqContent.candidatePosition, cqContent.personalinformation)) ||
                        (shouldDisplaySection(cqContent.startupname || cqContent.startupdescription || cqContent.problemstatement || cqContent.solution, cqContent.executiveSummary)) ||
                        (shouldDisplaySection(cqContent.founder && cqContent.founder.length > 0, cqContent.foundingTeam)) ||
                        (shouldDisplaySection(cqContent.productdescription || cqContent.technology || cqContent.currentdevelopmentstage, cqContent.aboutTheProductOrService)) ||
                        (shouldDisplaySection(cqContent.marketsize || cqContent.markettrends || cqContent.targetmarket || cqContent.competitors, cqContent.marketAnalysis)) ||
                        (shouldDisplaySection(cqContent.mainkpis || cqContent.customerevolution || cqContent.growthrate || cqContent.revenue, cqContent.tractionAndMetrics)) ||
                        (shouldDisplaySection(cqContent.revenuesources || cqContent.distrubutionchannels || cqContent.grossmargin || cqContent.scalabilityplan, cqContent.businessModel)) ||
                        (shouldDisplaySection(cqContent.fundingamount || cqContent.proposedvalue || cqContent.fundinguse || cqContent.expectedrunway, cqContent.requestForFunding)) ||
                        (shouldDisplaySection(cqContent.revenueprojections || cqContent.breakevenpoint || cqContent.mainassumptions || cqContent.scenarios, cqContent.financialProjections)) ||
                        (shouldDisplaySection(cqContent.potentialacquirers || cqContent.timeframe || cqContent.estimatereturn, cqContent.exitStrategy)) ||
                        (shouldDisplaySection(cqContent.businessrisks || cqContent.mitigationstrategies || cqContent.swotanalysis, cqContent.risksAndMitigation))
                      ) && (
                        <div className="alert alert-warning text-center">
                          <i className="material-icons d-block mb-3" style={{fontSize: '48px'}}>lock</i>
                          <h6>No Public Information Available</h6>
                          <p>The applicant has set all sections to private.</p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>
            
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewPublicDataModal;
