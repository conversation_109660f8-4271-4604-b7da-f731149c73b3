/* eslint-disable */
import React, { useContext, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useParams } from "react-router-dom";
import AsyncSelect from "react-select/async";

import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

import { assetImages } from "constants";

const ChatAiMedicalHelp = ({ roomId, isGroupChat = false }) => {
  // console.log("roomId >>>", roomId);

  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  const [startDate, setStartDate] = useState(getCurrentDate());
  const [endDate, setEndDate] = useState(getCurrentDate());

  const [loading, setLoading] = useState(false);
  const [loadingSummary, setLoadingSummary] = useState(false);
  const [chatContent, setChatContent] = useState("");
  const [summary, setSummary] = useState([]);
  const [plannedTasks, setPlannedTasks] = useState([]);
  const [task, setTask] = useState([]);
  const [error, setError] = useState("");
  const [managedTasks, setManagedTasks] = useState([]);
  const [createdTasks, setCreatedTasks] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [purpose, setPurpose] = useState("ask_followup_questions");
  const [followupQuestions, setFollowupQuestions] = useState([]);
  const [suggestSpecialization, setSuggestSpecialization] = useState([]);
  const [suggestedDoctorList, setSuggestedDoctorList] = useState([]);
  const [suggestedPrescription, setSuggestedPrescription] = useState([]);

  // Add custom styles for react-select
  const customSelectStyles = {
    control: (base) => ({
      ...base,
      minHeight: "31px",
      height: "31px",
    }),
    dropdownIndicator: (base) => ({
      ...base,
      padding: "4px",
      svg: {
        height: "16px",
        width: "16px",
      },
    }),
    clearIndicator: (base) => ({
      ...base,
      padding: "4px",
      svg: {
        height: "14px",
        width: "14px",
      },
    }),
    valueContainer: (base) => ({
      ...base,
      height: "31px",
      padding: "0 6px",
    }),
    input: (base) => ({
      ...base,
      margin: "0px",
      padding: "0px",
    }),
  };

  const validateDates = () => {
    if (!startDate || !endDate) {
      setError("Both start and end dates are required.");
      return false;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end < start) {
      setError("End date must be greater than or equal to start date.");
      return false;
    }

    return true;
  };

  const handleGenerateSummary = async () => {
    if (!validateDates()) {
      return;
    }
    resetHandlers();

    try {
      let requestUrl =
        url.API_BASE_URL + url.API_GET_ALL_CHAT_MESSAGES + `?token=${token}`;

      requestUrl += `&startdate=${startDate}&enddate=${endDate}`;

      if (isGroupChat) {
        requestUrl += `&chatgroupid=${roomId}`;
      } else {
        requestUrl += `&chatpersonid=${roomId}`;
      }

      console.log("requestUrl", requestUrl);

      const response = await getData(requestUrl);

      console.log("chat response", response);

      if (response.status) {
        const allChatRecords = response.data;

        // if allChatRecords is array, run loop and concat all record.description
        if (Array.isArray(allChatRecords)) {
          let chatContent = "";
          allChatRecords.forEach((record) => {
            chatContent += record.description + "; ";
          });

          setChatContent(chatContent);
        }
      }
    } catch (error) {
      setError("Failed to fetch summary. Please try again.");
    }
    setLoading(false);
  };

  // get AI Summary and Task based on this chat text
  // Request API Post data with chatContent and get response
  const getTaskAndPrescription = async () => {
    setLoadingSummary(true);
    setLoading(true);

    let requestUrl =
      url.API_BASE_URL + url.API_AI_GENERATE_PRESCRIPTION + `?token=${token}`;

    const body = {
      chatContent: chatContent,
    };

    try {
      const response = await postData(requestUrl, body);

      console.log("requestUrl", requestUrl);

      console.log("response of prescription", response);

      if (response.status) {
        setSummary(response.data.summary);
        setPlannedTasks(response.data.tasks);
        setSuggestedPrescription(response.data.prescription);
      }

      setLoadingSummary(false);
      setLoading(false);
    } catch (error) {
      console.error("Failed to get AI summary", error);
    }
  };

  const loadProjectOptions = async (inputValue) => {
    if (!inputValue || inputValue.length < 2) {
      return [];
    }

    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_PROJECTLIST_BY_SEARCH +
        `?token=${token}&searchtitle=${inputValue}`;
      const response = await getData(requestUrl);

      if (response.status && response.data) {
        return response.data.map((project) => ({
          value: project._id,
          label: project.label,
        }));
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch project list", error);
      return [];
    }
  };


  // ask follow-up questions
  const askFollowupQuestions = async () => {
    setLoading(true);

    // Reset prescription, planned tasks, and managed tasks as diagnosis is not yet finished
    setSuggestedPrescription([]);
    setPlannedTasks([]);
    setManagedTasks([]);

    // Call AI API to get follow-up questions based on chatContent
    const requestUrl = 
      url.API_BASE_URL + url.API_AI_FOLLOWUP_MEDICAL_QUESTIONS + `?token=${token}`;

    const body = { chatContent: chatContent };

    try {
      const response = await postData(requestUrl, body);

      console.log("requestUrl followup", requestUrl);

      console.log("response of follow-up questions", response);

      if (response.status) {
        setFollowupQuestions(response.data.questions);
        setSuggestSpecialization(response.data.specialist);
      } else {
        setError("Failed to fetch follow-up questions.");
      }
    } catch (error) {
      console.error("Error fetching follow-up questions", error);
      setError("Failed to fetch follow-up questions.");
    } finally {
      setLoading(false);
    }
  };

  // on change of chatContent, call getTaskAndSummary
  useEffect(() => {
    if (chatContent.length > 5) {

      console.log("purpose", purpose);

      if(purpose === "ask_followup_questions") {
        // Call AI API to get follow-up questions
        askFollowupQuestions();
      }

      if (purpose === "suggest_doctor") {
        // Call AI API to get suggested doctor list
        // show popup with suggested doctor having speciallization as suggested by AI
      }

      if (purpose === "generate_prescription") {
        // Call AI API to get suggested prescription
        getTaskAndPrescription();
      }
      
    }
  }, [chatContent]);

  // reset all when roomId changes
  useEffect(() => {
    setChatContent("");
    setSummary([]);
    setPlannedTasks([]);
    setError("");
    setSuggestSpecialization([]);
    setSuggestedDoctorList([]);
    setSuggestedPrescription([]);
  }, [roomId]);

  // Initialize managed tasks from planned tasks
  useEffect(() => {
    if (plannedTasks.length > 0) {
      const initialTasks = plannedTasks.map((task, index) => ({
        id: index + 1,
        title: task,
        startDate: getCurrentDate(),
        endDate: getCurrentDate(),
        hours: 0,
        priority: "Normal",
        rank: index + 1,
        project: null,
      }));
      setManagedTasks(initialTasks);
    }
  }, [plannedTasks]);

  const handleAddTask = () => {
    const newTask = {
      id: managedTasks.length + 1,
      title: "",
      startDate: getCurrentDate(),
      endDate: getCurrentDate(),
      hours: 0,
      priority: "Normal",
      rank: managedTasks.length + 1,
      project: null,
    };
    setManagedTasks([...managedTasks, newTask]);
  };

  const handleDeleteTask = (taskId) => {
    setManagedTasks(managedTasks.filter((task) => task.id !== taskId));
  };

  const handleTaskChange = (id, field, value) => {
    setManagedTasks(
      managedTasks.map((task) =>
        task.id === id ? { ...task, [field]: value } : task
      )
    );
  };

  const handleSubmitTasks = async () => {
    if (managedTasks.length === 0) return;

    setIsSubmitting(true);

    console.log("managedTasks", managedTasks);

    try {
      const requestUrl =
        url.API_BASE_URL + url.API_CREATE_TASKS_FROM_CHAT + `?token=${token}`;
      const response = await postData(requestUrl, { tasks: managedTasks });

      if (response.status) {
        setCreatedTasks(response.data);
        setManagedTasks([]); // Clear the form after successful creation
      }
    } catch (error) {
      setError("Failed to create tasks. Please try again.");
    }

    setIsSubmitting(false);
  };

  const resetHandlers = () => {
    setStartDate(getCurrentDate());
    setEndDate(getCurrentDate());
    setChatContent("");
    setSummary([]);
    setPlannedTasks([]);
    setError("");
    setManagedTasks([]);
    setCreatedTasks([]);
    // setPurpose("ask_followup_questions"); // <-- Remove this line
  };

  return (
    <div
      className="modal fade"
      id="chataimedicalhelp"
      tabIndex="-1"
      aria-labelledby="chataimedicalhelpLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
    >
      <div className="modal-dialog" style={{ minWidth: "90%" }}>
        <div className="modal-content shadow-xl">
          <div className="modal-header bg-primary text-white">
            <h5 className="modal-title" id="chataimedicalhelpLabel">
              AI Medical Help On Conversation
            </h5>
            <button
              type="button"
              className="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>

          <div className="modal-body p-4">
            {/* Date Range Selection */}
            <div className="mb-3">
              <div className="row align-items-end">
                {/* Date Range Section */}
                <div className="col-5">
                  <label className="form-label fw-semibold mb-1" style={{ fontSize: "1rem" }}>
                    Select Conversation Date Range:
                  </label>
                  <div className="d-flex gap-2">
                    <input
                      type="date"
                      className="form-control form-control-lg"
                      style={{ fontSize: "1rem" }}
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                    <input
                      type="date"
                      className="form-control form-control-lg"
                      style={{ fontSize: "1rem" }}
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                    />
                  </div>
                </div>
                {/* Purpose Section */}
                <div className="col-4">
                  <label className="form-label fw-semibold mb-1" style={{ fontSize: "1rem" }}>
                    Select Purpose:
                  </label>
                  <select
                    className="form-select form-select-lg w-100"
                    style={{ fontSize: "1rem" }}
                    value={purpose}
                    onChange={(e) => setPurpose(e.target.value)}
                    
                  >
                    <option value="ask_followup_questions">
                      🗂️ Ask Follow-Up Questions
                    </option>
                    <option value="suggest_doctor">
                      🔍 Suggest Suitable Doctor
                    </option>
                    
                    <option value="generate_prescription">
                      💊 Generate Preliminary Prescription
                    </option>
                  </select>
                </div>
                {/* Buttons Section */}
                <div className="col-3">
                  <div className="d-flex gap-2 mt-3">
                    <button
                      className="btn btn-sm btn-primary btn-lg w-50"
                      style={{ fontSize: "1rem" }}
                      onClick={handleGenerateSummary}
                      disabled={loading}
                    >
                      {loading ? "Generating..." : "Show"}
                    </button>
                    <button
                      className="btn btn-sm btn-secondary w-50"
                      type="button"
                      onClick={resetHandlers}
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {error && <p className="text-danger">{error}</p>}

            {/* show loadingSummary */}
            {loadingSummary && (
              <div className="text-center mt-3">
                <p>
                  ⏳ Please wait... while we generate summary and associated
                  tasks.
                </p>

                {/* Bootstrap Glow Placeholder */}
                <div className="placeholder-glow">
                  <span className="placeholder col-6"></span>
                  <span className="placeholder col-4"></span>
                  <span className="placeholder col-8"></span>
                </div>
              </div>
            )}

            {/* Follow-Up Questions Section */}
            {followupQuestions && followupQuestions.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Follow-Up Questions</h5>
                <ul className="list-unstyled mt-2">
                  {followupQuestions.map((q, idx) => (
                    <li key={idx} className="mb-2">
                      ❓ {q}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Summary Section */}
            {summary && summary.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Summary</h5>
                <ul className="list-unstyled mt-2">
                  {summary.map((item, index) => (
                    <li key={index} className="mb-2">
                      ✅ {item}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Suggested Specialization Section */}
            {suggestSpecialization && suggestSpecialization.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Suggested Specialization</h5>
                <ul className="list-unstyled mt-2">
                  {suggestSpecialization.map((spec, idx) => (
                    <li key={idx} className="mb-2">
                      🩺 {spec}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Suggested Doctor List Section */}
            {suggestedDoctorList && suggestedDoctorList.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Suggested Doctors</h5>
                <ul className="list-unstyled mt-2">
                  {suggestedDoctorList.map((doc, idx) => (
                    <li key={idx} className="mb-2">
                      👨‍⚕️ {doc}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Suggested Prescription Section */}
            {suggestedPrescription && suggestedPrescription.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Suggested Prescription</h5>
                <div className="table-responsive">
                  <table className="table table-bordered table-sm">
                    <thead className="bg-light">
                      <tr>
                        <th>Medicine</th>
                        <th>Dosage</th>
                        <th>Frequency</th>
                        <th>Duration</th>
                        <th>Notes</th>
                      </tr>
                    </thead>
                    <tbody>
                      {suggestedPrescription.map((item, idx) => (
                        <tr key={idx}>
                          <td>{item.medicine}</td>
                          <td>{item.dosage}</td>
                          <td>{item.frequency}</td>
                          <td>{item.duration}</td>
                          <td>{item.notes}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Planned Tasks Section */}
            {plannedTasks && plannedTasks.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Planned Tasks</h5>
                <ul className="list-unstyled mt-2">
                  {plannedTasks.map((task, index) => (
                    <li key={index} className="mb-2">
                      📌 {task}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Task Management Section */}
            {(summary?.length > 0 || plannedTasks?.length > 0) && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h5 className="fw-bold mb-0">Manage Tasks</h5>
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={handleAddTask}
                  >
                    + Add New Task
                  </button>
                </div>

                {managedTasks.length > 0 && (
                  <div style={{ overflow: "auto", maxWidth: "100%" }}>
                    <div style={{ minWidth: "1200px" }}>
                      <table className="table table-bordered">
                        <thead className="bg-light">
                          <tr>
                            <th style={{ width: "35%" }}>Task Title</th>
                            <th style={{ width: "25%" }}>Project</th>
                            <th style={{ width: "8%" }}>Start Date</th>
                            <th style={{ width: "8%" }}>End Date</th>
                            <th style={{ width: "6%" }}>Hours</th>
                            <th style={{ width: "8%" }}>Priority</th>
                            <th style={{ width: "6%" }}>Rank</th>
                            <th style={{ width: "4%" }}>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {managedTasks.map((task) => (
                            <tr key={task.id}>
                              <td>
                                <textarea
                                  className="form-control form-control-sm"
                                  value={task.title}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "title",
                                      e.target.value
                                    )
                                  }
                                  rows="3"
                                  style={{ width: "100%", resize: "vertical" }}
                                  placeholder="Enter task description..."
                                />
                              </td>
                              <td>
                                <AsyncSelect
                                  cacheOptions
                                  defaultOptions
                                  value={task.project}
                                  loadOptions={loadProjectOptions}
                                  onChange={(selected) =>
                                    handleTaskChange(
                                      task.id,
                                      "project",
                                      selected
                                    )
                                  }
                                  placeholder="Search project..."
                                  styles={customSelectStyles}
                                  isClearable
                                />
                              </td>
                              <td>
                                <input
                                  type="date"
                                  className="form-control form-control-sm"
                                  value={task.startDate}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "startDate",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td>
                                <input
                                  type="date"
                                  className="form-control form-control-sm"
                                  value={task.endDate}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "endDate",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td>
                                <input
                                  type="number"
                                  className="form-control form-control-sm"
                                  value={task.hours}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "hours",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td>
                                <select
                                  className="form-select form-select-sm"
                                  value={task.priority}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "priority",
                                      e.target.value
                                    )
                                  }
                                >
                                  <option value="High">High</option>
                                  <option value="Normal">Normal</option>
                                  <option value="Low">Low</option>
                                </select>
                              </td>
                              <td>
                                <input
                                  type="number"
                                  className="form-control form-control-sm"
                                  value={task.rank}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "rank",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td className="text-center">
                                <Link
                                  to="#"
                                  className="text-danger"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    handleDeleteTask(task.id);
                                  }}
                                >
                                  <i className="material-icons-outlined">
                                    delete
                                  </i>
                                </Link>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>

                      {/* Add Submit Button */}
                      <div className="d-flex justify-content-end mt-3">
                        <button
                          className="btn btn-success"
                          onClick={handleSubmitTasks}
                          disabled={isSubmitting || managedTasks.length === 0}
                        >
                          {isSubmitting ? "Creating Tasks..." : "Add to Task"}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Display Created Tasks */}
                {createdTasks.length > 0 && (
                  <div className="mt-4">
                    <h6 className="fw-bold mb-3">Created Tasks</h6>
                    <div className="table-responsive">
                      <table className="table table-sm">
                        <thead className="bg-light">
                          <tr>
                            <th>Title</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {createdTasks.map((task) => (
                            <tr key={task.id}>
                              <td>{task.title}</td>
                              <td>
                                <span className="badge bg-success">
                                  Created
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatAiMedicalHelp;
