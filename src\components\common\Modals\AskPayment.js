/* eslint-disable */
import React, { useState } from "react";
import { Link, useParams } from "react-router-dom";

import * as url from "helper/UrlHelper";
import { postData } from "utils/Gateway";

const AskPayment = ({ 
  userid,  
  userName,
  userImage,
  userEmail,
}) => {

    const params = useParams();
    const token = localStorage.getItem("token");
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));

    const [amount, setAmount] = useState("");
    const [dueDate, setDueDate] = useState("");
    const [comments, setComments] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSendRequest = async () => {
    if (!userid || !amount || !dueDate) {
      alert("Please fill all required fields.");
      return;
    }

    setIsSubmitting(true);

    const requestData = {
      userid,
      amount,
      dueDate,
      comments,
    };

    console.log("Request Data:", requestData);

    try {

        const requestUrl = url.API_BASE_URL + url.API_REQUEST_PAYMENT + `?token=${token}`;

        const response = await postData(
            requestUrl,
            requestData
        );

        console.log("Response:", response);

      if (response.status) {
        alert("Payment request sent successfully!");
        setAmount("");
        setDueDate("");
        setComments("");
      } else {
        alert("Failed to send payment request.");
      }
    } catch (error) {
      console.error("Error sending payment request:", error);
      alert("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="modal fade"
      id="askPaymentModal"
      tabIndex="-1"
      aria-labelledby="askPaymentModalLabel"
      aria-hidden="true"
    >
      <div className="modal-dialog">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="askPaymentModalLabel">
              Request Payment
            </h5>
            <button
              type="button"
              className="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div className="modal-body">
            <div className="mb-3 text-center">
              <img
                src={url.SERVER_URL + userImage || "default-user-image.png"}
                alt={userName || "User"}
                className="rounded-circle"
                style={{ width: "80px", height: "80px" }}
              />
              <h5 className="mt-2">{userName || "Unknown User"}</h5>
              <p className="text-muted">{userEmail || "No Email Provided"}</p>
            </div>
            <div className="mb-3">
              <label className="form-label">Amount</label>
              <div className="input-group">
                <span className="input-group-text">€</span>
                <input
                  type="text"
                  className="form-control"
                  value={amount}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value)) {
                      setAmount(value);
                    }
                  }}
                  placeholder="Enter amount"
                />
              </div>
            </div>
            <div className="mb-3">
              <label className="form-label">Due Date</label>
              <input
                type="date"
                className="form-control"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
              />
            </div>
            <div className="mb-3">
              <label className="form-label">Comments / Items billed for</label>
              <textarea
                className="form-control"
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                placeholder="Add comments (optional)"
              ></textarea>
            </div>
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Close
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={handleSendRequest}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Sending..." : "Send Request"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AskPayment;
