/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

//import images
import { assetImages } from "constants";

const MemberTab = ({
  tabName = "",
  isApproved,
  isJoined,
  refreshEvent = false,
  setRefreshEvent = () => {},
}) => {
  const params = useParams();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const { t } = useTranslation(); // translation marker

  const [memberListLoading, setMemberListLoading] = useState(false);
  const [memberList, setMemberList] = useState([]);

  //function for get all event members
  const getAllGroupMembers = async () => {
    try {
      setMemberListLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_GROUP_MEMBER +
        `?token=${token}&group=${params.id}&modulefrom=frontend`;

      const response = await getData(requestUrl);

      console.log(response);
      setMemberListLoading(false);
      if (response.status) {
        setMemberList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "members") {
      getAllGroupMembers();
    }
  }, [params.id, tabName]);

  if (memberListLoading) {
    return (
      <div className="post_item_outer">
        <div className="post_item">
          {[...Array(5)].map((_, index) => (
            <div className="white_shadow_bx people_bx mb-4" key={index}>
              <div className="people_row">
                <div className="left_people d-flex">
                  {/* Profile Image Placeholder */}
                  <div className="people_profile placeholder-glow">
                    <div
                      className="placeholder rounded-circle"
                      style={{ width: "64px", height: "64px" }}
                    />
                  </div>

                  {/* Profile Info Placeholder */}
                  <div className="people_profile_info ms-3 flex-grow-1">
                    <h3 className="placeholder-glow">
                      <span
                        className="placeholder col-7"
                        style={{ width: "100px" }}
                      />
                    </h3>
                    <h5 className="placeholder-glow">
                      <span
                        className="placeholder col-5"
                        style={{ width: "100px" }}
                      />
                    </h5>
                  </div>
                </div>

                {/* Action Buttons Placeholder */}
                <div className="right_people_btn d-flex gap-3">
                  <div className="placeholder-glow">
                    <span
                      className="btn outline placeholder col-10"
                      style={{ height: "36px" }}
                    />
                  </div>
                  <div className="placeholder-glow">
                    <span
                      className="placeholder rounded-circle"
                      style={{ width: "36px", height: "36px" }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="post_item_outer">
      <div className="post_item">
        {memberList.map((memberData, index) => {
          return (
            <div className="white_shadow_bx people_bx" key={index}>
              <div className="people_row">
                <div className="left_people">
                  <div className="people_profile">
                    <img
                      width="64"
                      height="64"
                      className="rounded-circle"
                      src={
                        memberData.image == ""
                          ? assetImages.defaultUser
                          : url.SERVER_URL + memberData.image
                      }
                      alt=""
                    />
                  </div>
                  <div className="people_profile_info">
                    <h3>{memberData.name}</h3>
                    <h5>{memberData.rolestring}</h5>
                  </div>
                </div>
                <div className="right_people_btn">
                  <Link
                    to={`/peoples/details/${memberData.user}`}
                    className="btn outline"
                  >
                    {t("View Details")}
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MemberTab;
