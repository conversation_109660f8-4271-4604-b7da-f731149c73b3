/* eslint-disable */
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData, postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import { fileDownloadHandler } from "utils/DownloadFile";
import MostImportantPanel from "../../MostImportantPanel/MostImportantPanel";

const AboutTab = ({ activeTab = "" }) => {
  const token = localStorage.getItem("token");

  const params = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [courseAbout, setCourseAbout] = useState({});

  //get cuourse details
  const getCourseDetails = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_GET_COURSE_INFORMATION +
        `/${params.id}?token=${token}`;

      const response = await getData(requestURL);

      setIsLoading(false);

      // console.log(response);

      if (response.status) {
        setCourseAbout(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && activeTab === "about") {
      getCourseDetails();
    }
  }, [params.id, activeTab]);

  if (isLoading) {
    return (
      <div className="inner_pnl_outer">
        <div className="inner_leftpnl">
          <div className="people_detailspnl">
            {/* Skills You Gain */}
            <div className="people_details_bx white_shadow_bx">
              <div className="details_hdng">
                <h4 className="placeholder-glow">
                  <span className="placeholder col-4"></span>
                </h4>
              </div>
              <div className="experts_skill placeholder-glow">
                <ul className="list_stye_none">
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                </ul>
              </div>
            </div>

            {/* About This Course */}
            <div className="people_details_bx white_shadow_bx">
              <div className="details_hdng">
                <h4 className="placeholder-glow">
                  <span className="placeholder col-4"></span>
                </h4>
              </div>
              <div className="results_para placeholder-glow">
                <p>
                  <span className="placeholder col-12 mb-1 d-block"></span>
                  <span className="placeholder col-10 mb-1 d-block"></span>
                  <span className="placeholder col-8 mb-1 d-block"></span>
                  <span className="placeholder col-8 d-block"></span>
                </p>
              </div>
            </div>

            {/* Sample Contents */}
            <div className="people_details_bx white_shadow_bx">
              <div className="details_hdng">
                <h4 className="placeholder-glow">
                  <span className="placeholder col-4"></span>
                </h4>
              </div>

              {/* Sample Video Placeholder */}
              <div className="course_vdo mb-3 placeholder-glow">
                <div
                  className="placeholder w-100"
                  style={{ height: "400px", borderRadius: "0.5rem" }}
                ></div>
              </div>

              {/* Sample File Placeholder */}
              <div className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 mb-3 placeholder-glow">
                <p className="fw-medium placeholder col-4 mb-0"></p>
                <span className="placeholder col-1"></span>
              </div>

              <div className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 mb-3 placeholder-glow">
                <p className="fw-medium placeholder col-4 mb-0"></p>
                <span className="placeholder col-1"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="inner_pnl_outer">
      <div className="inner_leftpnl">
        <div className="people_detailspnl">
          <div className="people_details_bx white_shadow_bx">
            <div className="details_hdng">
              <h4>Skills You Gain</h4>
            </div>
            {courseAbout?.skillvalues ? (
              <div className="experts_skill">
                <ul className="list_stye_none">
                  {courseAbout?.skillvalues?.map((skill, index) => {
                    return (
                      <li key={index}>
                        <Link to="#">{skill.label}</Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ) : null}
          </div>
          <div className="people_details_bx white_shadow_bx">
            <div className="details_hdng">
              <h4>About This Course</h4>
            </div>
            <div className="results_para">
              <p
                dangerouslySetInnerHTML={{ __html: courseAbout?.description }}
              ></p>
            </div>
          </div>
          {courseAbout?.uploads ? (
            <div className="people_details_bx white_shadow_bx">
              <div className="details_hdng">
                <h4>Sample Contents</h4>
              </div>
              {courseAbout?.uploads
                ?.sort((a, b) => a.rank - b.rank)
                ?.map((content, index) => {
                  const file = content?.uploadid
                    ? {
                        ...content?.uploadid,
                        path: url.SERVER_URL + content?.uploadid?.path,
                      }
                    : null;
                  if (
                    content.uploadid?.name?.split(".").pop() === "mp4" ||
                    content.uploadid?.name?.split(".").pop() === "mov"
                  ) {
                    return (
                      <div className="course_vdo mb-3" key={index}>
                        <video
                          controls
                          width="100%"
                          height="100%"
                          src={url.SERVER_URL + content?.uploadid?.path}
                          className="object-fit-cover"
                        >
                          Your browser does not support the video tag.
                        </video>
                      </div>
                    );
                  } else {
                    return (
                      <div
                        className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 mb-3"
                        key={index}
                      >
                        <p className="fw-medium">{content?.uploadid?.name}</p>
                        <Link
                          to="#"
                          className={!file ? "d-none" : ""}
                          onClick={() => fileDownloadHandler(file)}
                        >
                          <span className="d-block material-icons-outlined">
                            download
                          </span>
                        </Link>
                      </div>
                    );
                  }
                })}
            </div>
          ) : null}
        </div>
      </div>
      <MostImportantPanel />
    </div>
  );
};

export default AboutTab;
