import {
  getConvertedDateText,
  getNextHourSlot,
} from "helper/EventHelper/EventHelper";
import React from "react";

const StepFour = ({
  componentStyle = {},
  personName = "",
  selectedTimeSlot,
  selectedMeetingDate,
}) => {
  return (
    <div className="step-4 text-center" style={componentStyle}>
      <div className="gretings_icon mb-3">
        <span className="material-icons-outlined">task_alt</span>
      </div>
      <h4 className="fw-bold mb-2">YOU ARE SCHEDULED</h4>
      <p>A calendar invitation has been sent to user's email address.</p>
      <div className="px-3 py-4 my-3 border rounded-3">
        <p className="fw-bold">{personName}</p>
        <h3 className="mb-2">60 Minute Meeting</h3>
        <p className="d-flex align-items-center justify-content-center gap-1 fw-bold mb-2">
          <span
            className="d-block material-icons-outlined text-primary"
            style={{ fontSize: "18px" }}
          >
            calendar_month
          </span>
          <span className="d-block">
            {selectedTimeSlot} - {getNextHourSlot(selectedTimeSlot)},{" "}
            {getConvertedDateText(selectedMeetingDate)}
          </span>
        </p>
        <p className="d-flex align-items-center justify-content-center gap-1 fw-bold">
          <span
            className="d-block material-icons-outlined text-primary"
            style={{ fontSize: "18px" }}
          >
            language
          </span>
          <span className="d-block">India Standard Time</span>
        </p>
      </div>
    </div>
  );
};

export default StepFour;
