/* eslint-disable */
import React, { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
// import use translation ------------------------------
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

const OverviewTab = ({ tabName = "" }) => {
  const { t } = useTranslation(); // translation marker
  const params = useParams();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [userDetails, setUserDetails] = useState({});

  //function for get user details
  const getUserDetails = async () => {
    try {
      setIsLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USER_DETAILS +
        `/${params.id}` +
        `?token=${token}`;

      const response = await getData(requestUrl);

      setIsLoading(false);

      // console.log("response in app user overview tab", response);

      if (response.status && response.data) {
        setUserDetails(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "overview") {
      getUserDetails();
    }
  }, [params.id, tabName]);

  if (isLoading) {
    return (
      <div className="people_detailspnl">
        {/* Details Block Placeholder */}
        <div className="people_details_bx white_shadow_bx">
          <div className="details_hdng">
            <h4>{t("Details")}</h4>
          </div>
          <div className="results_para placeholder-glow">
            <span className="placeholder col-12"></span>
            <span className="placeholder col-10"></span>
            <span className="placeholder col-11"></span>
            <span className="placeholder col-8"></span>
          </div>
        </div>

        {/* Education Block Placeholder */}
        <div className="people_details_bx white_shadow_bx">
          <div className="details_hdng">
            <h4>{t("Educations")}</h4>
          </div>
          <div className="experience_pnl">
            <div className="dates_pnlOuter placeholder-glow">
              {[...Array(2)].map((_, index) => (
                <div className="dates_pnlItem experice_item" key={index}>
                  <span className="dates_nmbr exprince_logo">
                    {/* Placeholder for the square logo */}
                    <span
                      className="placeholder"
                      style={{ width: "48px", height: "48px" }}
                    ></span>
                  </span>
                  <div className="experience_hdng w-100">
                    <h3 className="placeholder-glow">
                      <span className="placeholder col-6"></span>
                    </h3>
                    <h5 className="placeholder-glow">
                      <span className="placeholder col-3"></span>
                    </h5>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Experience Block Placeholder */}
        <div className="people_details_bx white_shadow_bx">
          <div className="details_hdng">
            <h4>{t("Experience")}</h4>
          </div>
          <div className="experience_pnl">
            <div className="dates_pnlOuter placeholder-glow">
              {[...Array(2)].map((_, index) => (
                <div className="dates_pnlItem experice_item" key={index}>
                  <span className="dates_nmbr exprince_logo">
                    {/* Placeholder for the square logo */}
                    <span
                      className="placeholder"
                      style={{ width: "48px", height: "48px" }}
                    ></span>
                  </span>
                  <div className="experience_hdng w-100">
                    <h3 className="placeholder-glow">
                      <span className="placeholder col-7"></span>
                    </h3>
                    <h5 className="placeholder-glow">
                      <span className="placeholder col-4"></span>
                    </h5>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Skills Block Placeholder */}
        <div className="people_details_bx white_shadow_bx">
          <div className="details_hdng">
            <h4>{t("Skills")}</h4>
          </div>
          <div className="experts_skill">
            <ul className="list_stye_none placeholder-glow">
              {/* Using inline-block spans to mimic the flow of tags */}
              <span className="placeholder col-2 me-2 mb-2 p-3 rounded-pill"></span>
              <span className="placeholder col-3 me-2 mb-2 p-3 rounded-pill"></span>
              <span className="placeholder col-2 me-2 mb-2 p-3 rounded-pill"></span>
              <span className="placeholder col-4 me-2 mb-2 p-3 rounded-pill"></span>
              <span className="placeholder col-3 me-2 mb-2 p-3 rounded-pill"></span>
            </ul>
          </div>
        </div>

        {/* Additional Information Block Placeholder */}
        <div className="people_details_bx white_shadow_bx">
          <div className="details_hdng">
            <h4>{t("Additional Information")}</h4>
          </div>
          <div className="expert_aditonal_outer">
            <div className="row">
              <div className="col-md-6">
                <div className="additional_info placeholder-glow">
                  <h4>
                    <i className="material-icons-outlined">language</i>
                    {t("Language")}
                  </h4>
                  <h3 className="placeholder col-4"></h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="people_detailspnl">
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Details")}</h4>
        </div>
        <div className="results_para">
          <p dangerouslySetInnerHTML={{ __html: userDetails?.about }}></p>
        </div>
      </div>
      {/* education block */}
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Educations")}</h4>
        </div>
        <div className="experience_pnl">
          <div className="dates_pnlOuter">
            {userDetails?.educationids?.map((education, index) => {
              return (
                <div className="dates_pnlItem experice_item" key={index}>
                  <span className="dates_nmbr exprince_logo">
                    <img src="/images/exprt-logo1.svg" alt="" />
                  </span>
                  <div className="experience_hdng">
                    <h3>
                      {education.degree}
                      <span>at {education.institute}</span>
                    </h3>
                    <h5>{education.year}</h5>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* experience block */}
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Experience")}</h4>
        </div>
        <div className="experience_pnl">
          <div className="dates_pnlOuter">
            {userDetails?.experienceids?.map((experience, index) => {
              return (
                <div className="dates_pnlItem experice_item" key={index}>
                  <span className="dates_nmbr exprince_logo">
                    <img src="/images/exprt-logo1.svg" alt="" />
                  </span>
                  <div className="experience_hdng">
                    <h3>
                      {experience.position}
                      <span>
                        at{" "}
                        {experience.companyid
                          ? experience.companyid.label
                          : experience.company != ""
                          ? experience.company
                          : ""}
                      </span>
                    </h3>
                    <h5>
                      {experience.start} to {experience.end}
                    </h5>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Skills")}</h4>
        </div>
        <div className="experts_skill">
          <ul className="list_stye_none">
            {userDetails?.skills?.map((skill, index) => {
              return (
                <li key={index}>
                  <Link to="#">{skill.name}</Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>

      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Additional Information")}</h4>
        </div>
        <div className="expert_aditonal_outer">
          <div className="row">
            {/* <div className="col-md-6">
              <div className="additional_info">
                <h4>
                  <i className="material-icons-outlined">email</i>
                  {t("Email")}
                </h4>
                <Link to="#">{expertEmail}</Link>
              </div>
            </div> */}
            <div className="col-md-6">
              <div className="additional_info">
                <h4>
                  <i className="material-icons-outlined">language</i>
                  {t("Language")}
                </h4>
                <h3>{userDetails?.language}</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;
