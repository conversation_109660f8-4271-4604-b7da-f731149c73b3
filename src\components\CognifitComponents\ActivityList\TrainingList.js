import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

const TrainingList = () => {
  const token = localStorage.getItem("token"); // user token after login

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [trainings, setTrainings] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [fetchingAccessToken, setFetchingAccessToken] = useState(false);
  const [selectedTrainingKey, setSelectedTrainingKey] = useState('');

    const [userToken, setUserToken] = useState('');
    const [accessToken, setAccessToken] = useState('');
    const [clientId, setClientId] = useState('');

    // get the cognifit token
    const getCognifitToken = async (key) => {

        setFetchingAccessToken(true);
        setErrorMessage('');

        setSelectedTrainingKey(key);

        try {
        let requestURL = url.API_BASE_URL + url.API_COGNIFIT_TOKEN + `?token=${token}`;
    
        console.log('requestURL braingame list: ', requestURL);
    
        // get token using post method
        const response = await getData(requestURL);
    
        console.log('Response for getCognifitToken: ', response);
    
        if (response.token) {
            setUserToken(response.token);
            setAccessToken(response.accesstoken);
    
            localStorage.setItem("cognifitToken", response.token);
            localStorage.setItem("cognifitAccessToken", response.accesstoken);
    
            console.log("Cognifit Token: ", response.token);
            console.log("Cognifit Access Token: ", response.accesstoken);
        }else{
            setErrorMessage('Failed to fetch Cognifit token. Please try again.');
            console.error("Error fetching Cognifit token: ", response);
        }
    
        } catch (error) {
        console.log(error.message);
        } finally {
        setFetchingAccessToken(false);
        }
    
    };


    const getTrainings = async () => {
        setIsLoading(true);
        setErrorMessage('');
        try {
        let requestURL = url.API_BASE_URL + url.API_COGNIFIT_ACTIVITY_LIST + `?token=${token}&type=training`;
        const response = await getData(requestURL);

        console.log('Response for getTrainings: ', response.data);

        if (response && response.data) {
            setTrainings(response.data);
            setClientId(response.clientid);
        } else {
            setErrorMessage('Failed to fetch trainings.');
        }
        } catch (error) {
        setErrorMessage('Error fetching trainings.');
        } finally {
        setIsLoading(false);
        }
    };

    useEffect(() => {
        getTrainings();
    }, []);

    useEffect(() => {
    
        console.log('useeffect clientId: ', clientId, 
            'accessToken: ', accessToken, 
            'selectedTrainingKey: ', selectedTrainingKey);

        if(clientId && accessToken && selectedTrainingKey) {
            window.location.href = `${url.COGNIFIT_LAUNCH_URL}/launch?clientId=${clientId}&accessToken=${accessToken}&trainingKey=${selectedTrainingKey}`;
        } 
    }, [clientId, accessToken, selectedTrainingKey]);

  return (
    <section className="">
      <div className="container-fluid">
        <div className="faq_pnl_wrapper">
          <div className="shadow-sm p-4 rounded-10 bg-white mb-3">
            <h2 className="fs-xl text-black mb-3 fw-semibold">Cognifit Training</h2>
            {errorMessage && (
              <div className="alert alert-danger" role="alert">
                {errorMessage}
              </div>
            )}
            <div className="row">
              {isLoading ? (
                Array.from({ length: 4 }).map((_, idx) => (
                  <div className="col-md-4 col-sm-6 mb-4" key={idx}>
                    <div className="card h-100 shadow-sm">
                      <div className="card-img-top placeholder-glow" style={{ height: "120px", background: "#e0e0e0", borderRadius: "8px" }} />
                      <div className="card-body">
                        <h5 className="card-title placeholder-glow">
                          <span className="placeholder col-8"></span>
                        </h5>
                        <p className="card-text placeholder-glow">
                          <span className="placeholder col-12"></span>
                        </p>
                        <span className="badge bg-secondary placeholder col-4"></span>
                        <div className="mt-3">
                          <span className="btn btn-outline-primary btn-sm disabled placeholder col-6"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : trainings && trainings.length > 0 ? (
                trainings.map((training) => (
                  <div className="col-md-4 col-sm-6 mb-4" key={training.key}>
                    <div className="card h-100 shadow-sm">
                      <img
                        src={training.assets?.images?.scareIconZodiac}
                        alt={training.assets?.titles?.es}
                        className="card-img-top"
                        style={{ height: "120px", objectFit: "cover" }}
                      />
                      <div className="card-body">
                        <h5 className="card-title">{training.assets?.titles?.es}</h5>
                        <p className="card-text">
                          {training.assets?.descriptions?.es
                            ? training.assets.descriptions.es.slice(0, 50) + (training.assets.descriptions.es.length > 50 ? "..." : "")
                            : ""}
                        </p>
                        <span className="badge bg-secondary">{training.key}</span>
                        <div className="mt-2">
                          <strong>Tasks:</strong>
                          <ul className="mb-1" style={{ fontSize: "0.95em" }}>
                            {training.tasks && training.tasks.slice(0, 5).map((task, idx) => (
                              <li key={idx}>{task}</li>
                            ))}
                            {training.tasks && training.tasks.length > 5 && (
                              <li>...and {training.tasks.length - 5} more</li>
                            )}
                          </ul>
                        </div>
                        <div className="mt-3 d-flex align-items-center">
                          <Link
                            to="#"
                            className="btn btn-outline-primary btn-sm"
                            onClick={() => getCognifitToken(training.key)}
                            disabled={fetchingAccessToken}
                            style={fetchingAccessToken ? { pointerEvents: "none", opacity: 0.7 } : {}}
                          >
                            Launch Training
                          </Link>
                          {fetchingAccessToken && selectedTrainingKey === training.key && (
                            <span className="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-12">
                  <p>No trainings found.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrainingList;
