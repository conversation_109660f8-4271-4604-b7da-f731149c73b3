/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

//import images
import { assetImages } from "constants";
import EventFeedbackModal from "../../Modals/EventFeedbackModal";
import NoFeedbackRuleModal from "../../Modals/NoFeedbackRuleModal";

const MembersTab = ({
  tabName = "",
  isApproved,
  isJoined,
  refreshEvent = false,
  setRefreshEvent = () => {},
}) => {
  const params = useParams();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const { t } = useTranslation(); // translation marker

  const [memberListLoading, setMemberListLoading] = useState(false);
  const [memberList, setMemberList] = useState([]);

  const [surveyIdForFeedback, setSurveyIdForFeedback] = useState(null);
  const [surveyInviteId, setSurveyInviteId] = useState(null);
  const [loadingFeedback, setLoadingFeedback] = useState(null); // Track which member is loading

  //function for get all event members
  const getAllEventMembers = async () => {
    try {
      setMemberListLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_EVENT_MEMBER +
        `?token=${token}&event=${params.id}&modulefrom=frontend`;

      const response = await getData(requestUrl);

      // console.log("response in event member list------>", response);

      setMemberListLoading(false);
      if (response.status) {
        setMemberList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  // function for get survey
  const getSurveyByFeedback = async (memberRoleId, memberId) => {
    setLoadingFeedback(memberId); // Set loading state for this specific member

    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_SURVEY_BY_FEEDBACK_RULE +
        `?token=${token}&event=${params.id}&recieverrole=${memberRoleId}`;

      const response = await getData(requestURL);

      console.log(response);

      if (response.status) {
        setSurveyIdForFeedback(response.data._id);
        saveSurveyHandler(response.data._id, memberId);
      } else {
        //show otpconfirm modal
        const myModal = new bootstrap.Modal(
          document.getElementById("no_feedback_modal")
        );
        myModal.show();
        setLoadingFeedback(null); // Reset loading state
      }
    } catch (error) {
      console.log(error.message);
      setLoadingFeedback(null); // Reset loading state on error
    }
  };

  //function for save survey
  const saveSurveyHandler = async (surveyId, memberId) => {
    try {
      let surveyData = {
        name: userInfo?.name,
        surname: userInfo?.surname,
        email: userInfo?.email,
        mobile: userInfo?.phone,
        surveyid: surveyId,
        eventmemberid: memberId,
      };

      let requestUrl = url.API_BASE_URL + url.API_SAVE_SURVEY_FEEDBACK;

      const response = await postData(requestUrl, surveyData);

      console.log("response in save survey", response);

      if (response.status) {
        setSurveyInviteId(response.data);
        //show otpconfirm modal
        let myModal = new bootstrap.Modal(
          document.getElementById("feedbackModal")
        );
        myModal.show();
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      setLoadingFeedback(null); // Reset loading state regardless of success/failure
    }
  };

  useEffect(() => {
    if (params.id && tabName === "members") {
      getAllEventMembers();
    }
  }, [params.id, tabName]);

  useEffect(() => {
    if (refreshEvent) {
      getAllEventMembers();
      setRefreshEvent(false);
    }
  }, [refreshEvent]);

  if (memberListLoading) {
    return (
      <div className="post_item_outer">
        <div className="post_item">
          {[...Array(5)].map((_, index) => (
            <div className="white_shadow_bx people_bx mb-4" key={index}>
              <div className="people_row">
                <div className="left_people d-flex">
                  {/* Profile Image Placeholder */}
                  <div className="people_profile placeholder-glow">
                    <div
                      className="placeholder rounded-circle"
                      style={{ width: "64px", height: "64px" }}
                    />
                  </div>

                  {/* Profile Info Placeholder */}
                  <div className="people_profile_info ms-3 flex-grow-1">
                    <h3 className="placeholder-glow">
                      <span
                        className="placeholder col-7"
                        style={{ width: "100px" }}
                      />
                    </h3>
                    <h5 className="placeholder-glow">
                      <span
                        className="placeholder col-5"
                        style={{ width: "100px" }}
                      />
                    </h5>
                  </div>
                </div>

                {/* Action Buttons Placeholder */}
                <div className="right_people_btn d-flex gap-3">
                  <div className="placeholder-glow">
                    <span
                      className="btn outline placeholder col-10"
                      style={{ height: "36px" }}
                    />
                  </div>
                  <div className="placeholder-glow">
                    <span
                      className="placeholder rounded-circle"
                      style={{ width: "36px", height: "36px" }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="post_item_outer">
      <div className="post_item">
        {memberList.map((memberData, index) => {
          const isCurrentMemberLoading = loadingFeedback === memberData._id;

          return (
            <div className="white_shadow_bx people_bx" key={index}>
              <div className="people_row">
                <div className="left_people">
                  <div className="people_profile">
                    <img
                      width="64"
                      height="64"
                      src={
                        memberData.image == ""
                          ? assetImages.defaultUser
                          : url.SERVER_URL + memberData.image
                      }
                      alt=""
                    />
                  </div>
                  <div className="people_profile_info">
                    <h3>{memberData.name}</h3>
                    <h5>{memberData.rolestring}</h5>
                  </div>
                </div>

                <div className="right_people_btn d-flex gap-3">
                  <Link
                    to="#"
                    className={
                      memberData?.user?.toString() === userInfo._id.toString()
                        ? "d-none"
                        : isApproved
                        ? memberData.role
                          ? `btn outline ${
                              isCurrentMemberLoading ? "disabled" : ""
                            }`
                          : "d-none"
                        : "d-none"
                    }
                    type="button"
                    onClick={() => {
                      if (!isCurrentMemberLoading) {
                        getSurveyByFeedback(memberData.role, memberData._id);
                      }
                    }}
                    style={{
                      pointerEvents: isCurrentMemberLoading ? "none" : "auto",
                      opacity: isCurrentMemberLoading ? 0.6 : 1,
                    }}
                  >
                    {isCurrentMemberLoading ? (
                      <span className="d-flex align-items-center gap-2">
                        <span
                          className="spinner-border spinner-border-sm"
                          role="status"
                          aria-hidden="true"
                        ></span>
                        Loading...
                      </span>
                    ) : memberData.isfeedbackgiven ? (
                      t("Feedback Given")
                    ) : (
                      t("Give Feedback")
                    )}
                  </Link>
                  <Link
                    to={`/peoples/details/${memberData.user}`}
                    className="d-flex align-items-center justify-content-center flex-shrink-0 bg-primary text-white rounded-circle"
                    style={{ width: "36px", height: "36px" }}
                  >
                    <span className="d-block material-icons fs-lg">
                      account_box
                    </span>
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <NoFeedbackRuleModal />
      <EventFeedbackModal
        setSurveyIdForFeedback={setSurveyIdForFeedback}
        surveyIdForFeedback={surveyIdForFeedback}
        surveyInviteId={surveyInviteId}
        setSurveyInviteId={setSurveyInviteId}
      />
    </div>
  );
};

export default MembersTab;
