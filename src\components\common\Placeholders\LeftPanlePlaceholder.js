import React from "react";

const LeftPanlePlaceholder = () => {
  return (
    <div className="pnl_leftpnl">
      <div className="white_shadow_bx people_leftusr">
        <div className="groups_info">
          <div className="top_grp_info d-flex justify-content-between align-items-start">
            <div className="experts_holdertext w-100">
              <h3 className="placeholder-glow">
                <span className="placeholder col-6"></span>
              </h3>
              <div className="event_hdng placeholder-glow">
                <h5>
                  <i className="material-icons-outlined">event_note</i>
                  <em className="ms-2 placeholder col-4"></em>
                </h5>
              </div>
            </div>
            <div className="share_btn placeholder-glow">
              <span className="placeholder col-2"></span>
            </div>
          </div>

          <div className="event_tags mt-3">
            <ul className="list_stye_none placeholder-glow">
              <li className="mb-2 d-flex align-items-center gap-2">
                <i className="material-icons-outlined ornge_icon">star</i>
                <span className="placeholder col-2"></span>
              </li>
              <li className="d-flex align-items-center gap-2">
                <i className="material-icons-outlined">place</i>
                <span className="placeholder col-4"></span>
              </li>
            </ul>
          </div>

          <div className="event_user grps_user mt-3">
            <ul className="list_stye_none d-flex align-items-center gap-2">
              {[...Array(4)].map((_, index) => (
                <li key={index} className="placeholder-glow">
                  <div
                    className="rounded-circle bg-secondary placeholder"
                    style={{ width: "40px", height: "40px" }}
                  ></div>
                </li>
              ))}
              <li className="placeholder-glow">
                <span className="placeholder col-2"></span>
              </li>
            </ul>
          </div>

          <div className="feedback_jointbtnOuter mt-4 d-flex gap-2 flex-wrap">
            <div className="feed_btn placeholder-glow">
              <span className="btn gray_btn disabled placeholder col-5"></span>
            </div>
            <div className="feed_btn placeholder-glow">
              <span className="btn gray_btn disabled placeholder col-4"></span>
            </div>
            <div className="tag_btn placeholder-glow">
              <span className="btn gray_btn disabled placeholder col-2"></span>
            </div>
          </div>

          <div className="connected_text mt-3 placeholder-glow">
            <p className="d-flex align-items-center gap-2">
              <i className="material-icons">check_circle</i>
              <span className="placeholder col-5"></span>
            </p>
          </div>

          <div className="tags_links mt-4 mb-2">
            <ul className="list_stye_none d-flex flex-wrap gap-2 placeholder-glow">
              {[...Array(3)].map((_, index) => (
                <li key={index}>
                  <span className="placeholder col-3"></span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeftPanlePlaceholder;
