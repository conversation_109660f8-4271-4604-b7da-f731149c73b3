/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

import ViewPublicDataCvModal from "components/JobsComponents/JobDetailsComponents/Modals/ViewPublicDataCvModal";
import ViewPublicDataCqModal from "components/JobsComponents/JobDetailsComponents/Modals/ViewPublicDataCqModal";

import SaveIdea from "components/JobsComponents/JobDetailsComponents/Popups/SaveIdea";
import SaveOpportunity from "components/JobsComponents/JobDetailsComponents/Popups/SaveOpportunity";

const AppliedTab = ({ tabName = "" }) => {
  const { t } = useTranslation();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const params = useParams();

  const [componentId, setComponentId] = useState(null); // cvid, cqId so on
  const [challengeType, setChallengeType] = useState("talenthunt");

  const [selectedResponseId, setSelectedResponseId] = useState(null);
  const [selectedUserId, setSelectedUserId] = useState(null);

  const [showPublicData, setShowPublicData] = useState(false);

  const [isIdeaSaved, setIsIdeaSaved] = useState(false); // to show thanks modal
  const [showIdea, setShowIdea] = useState(false); // to show save form

  const [isOpportunitySaved, setIsOpportunitySaved] = useState(false);
  const [showOpportunity, setShowOpportunity] = useState(false);

  const [isTaskSaved, setIsTaskSaved] = useState(false);
  const [showGoal, setShowGoal] = useState(false);

  const [responseList, setResponseList] = useState([]);
  const [winnerList, setwinnerList] = useState([]);

  const [isLoading, setisLoading] = useState(false);

  //function for get response
  const getAllChallengeResponse = async () => {
    try {
      setisLoading(true);

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_CHALLENGE_RESPONSE +
        `?token=${token}&challenge=${params.id}`;

      console.log("get challenge responses", requestUrl);

      const response = await getData(requestUrl);

      console.log("challenge responses", response);
      setisLoading(false);

      if (response.status) {
        setResponseList(response.data);

        const filteredResults = response.data.filter((resp) => resp.iswinner);

        setwinnerList(filteredResults);
      }
    } catch (error) {
      console.log(error.message);
      setisLoading(false);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "applied") {
      getAllChallengeResponse();
    }
  }, [params.id, tabName]);

  const showPublicDataHandler = (type = null, id) => {
    if (type) {
      setChallengeType(type);
    }

    setComponentId(id);
  };

  // show idea popup -------------------
  const showIdeaHandler = (responseId, submittedbyid) => {
    console.log("showIdeaHandler responseId", responseId);
    console.log("showIdeaHandler submittedbyid", submittedbyid);

    setSelectedResponseId(responseId);
    setSelectedUserId(submittedbyid);
    setShowIdea(true);

    setTimeout(() => {
      const offcanvasElement = document.getElementById("saveIdeaOffcanvas");
      const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
      bsOffcanvas.show();
    }, 100); // delay to ensure state is set

    setShowPublicData(false);
    setShowOpportunity(false);
    setShowGoal(false);
  };

  // show invoice popup ---------------
  const showOpportunityHandler = (responseId, submittedbyid) => {
    console.log("invoice responseId", responseId);
    console.log("invoice submittedbyid", submittedbyid);

    setSelectedResponseId(responseId);
    setSelectedUserId(submittedbyid);
    setShowOpportunity(true);

    setTimeout(() => {
      const offcanvasElement = document.getElementById(
        "saveOpportunityOffcanvas"
      );
      const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
      bsOffcanvas.show();
    }, 100); // delay to ensure state is set
  };

  // Loading placeholder component
  const LoadingPlaceholder = () => (
    <div className="post_item">
      {[1, 2, 3].map((item, index) => (
        <div
          className="white_shadow_bx people_bx position-relative overflow-hidden"
          key={index}
        >
          <div className="people_row">
            <div className="left_people">
              <div className="people_profile">
                <div
                  className="glow-placeholder"
                  style={{
                    width: "60px",
                    height: "60px",
                    borderRadius: "50%",
                  }}
                ></div>
              </div>
              <div className="people_profile_info">
                <h3
                  className="glow-placeholder"
                  style={{ width: "180px", height: "20px" }}
                ></h3>
                <h5
                  className="glow-placeholder"
                  style={{
                    width: "220px",
                    height: "18px",
                    marginTop: "8px",
                  }}
                ></h5>
                <h6
                  className="glow-placeholder"
                  style={{
                    width: "150px",
                    height: "16px",
                    marginTop: "8px",
                  }}
                ></h6>
                <div className="event_tags mt-3">
                  <ul className="list_stye_none mb-0">
                    <li
                      className="btn outline me-2 glow-placeholder"
                      style={{ width: "80px", height: "30px" }}
                    ></li>
                    <li
                      className="btn outline me-2 glow-placeholder"
                      style={{ width: "60px", height: "30px" }}
                    ></li>
                    <li
                      className="btn outline glow-placeholder"
                      style={{ width: "110px", height: "30px" }}
                    ></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <style jsx>{`
            .glow-placeholder {
              animation: glow 1.5s ease-in-out infinite;
              background: #f6f7f8;
              background: linear-gradient(
                to right,
                #f6f7f8 8%,
                #edeef1 18%,
                #f6f7f8 33%
              );
              background-size: 800px 104px;
              border-radius: 4px;
              display: inline-block;
            }
            @keyframes glow {
              0% {
                background-position: -200px 0;
              }
              100% {
                background-position: 200px 0;
              }
            }
          `}</style>
        </div>
      ))}
    </div>
  );

  return (
    <>
      <div className="post_item_outer">
        {isLoading ? (
          <LoadingPlaceholder />
        ) : responseList.length === 0 ? (
          <div className="pnl_mdlpnl w-100">
            <div className="empty_access text-center">
              <div className="empty_pic mb-4">
                {" "}
                <img src={assetImages.emptyVector} alt="" />
              </div>
              <div className="empty_text">
                <p className="fs-md text-gray fw-semibold">
                  Sorry....! No application submitted for this job
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="post_item">
            {responseList.map((jobResponse, index) => {
              return (
                <div className="white_shadow_bx people_bx" key={index}>
                  <div className="people_row">
                    <div className="left_people">
                      <div className="people_profile">
                        <img
                          src={
                            jobResponse.applicantimage === ""
                              ? assetImages.defaultUser
                              : url.SERVER_URL + jobResponse.applicantimage
                          }
                          alt={jobResponse.applicantname}
                        />
                      </div>
                      <div className="people_profile_info">
                        <h3>
                          {jobResponse.applicantname
                            ? jobResponse.applicantname
                            : jobResponse.submittedbyname}
                        </h3>
                        <h5>{jobResponse.responsecode}</h5>
                        <h6 className="fw-semibold text-gray">
                          {jobResponse ? jobResponse.applieddate : ""}
                        </h6>
                        <div className="event_tags mt-2">
                          <ul className="list_stye_none mb-0">
                            <li className="btn outline me-2">
                              {jobResponse.candidatecvid ? (
                                <Link
                                  to="#"
                                  onClick={() =>
                                    showPublicDataHandler(
                                      "talenthunt",
                                      jobResponse.candidatecvid
                                    )
                                  }
                                  data-bs-toggle="modal"
                                  data-bs-target="#view_publicdata_CV_modal"
                                >
                                  Public Profile
                                </Link>
                              ) : (
                                ""
                              )}

                              {jobResponse.capitalquestid ? (
                                <Link
                                  to="#"
                                  onClick={() =>
                                    showPublicDataHandler(
                                      "capitalquest",
                                      jobResponse.capitalquestid
                                    )
                                  }
                                  data-bs-toggle="modal"
                                  data-bs-target="#view_publicdata_CQ_modal"
                                >
                                  Public Profile
                                </Link>
                              ) : (
                                ""
                              )}
                            </li>

                            <li className="btn outline me-2">
                              <Link
                                to="#"
                                onClick={() =>
                                  showIdeaHandler(
                                    jobResponse._id,
                                    jobResponse.submittedbyid
                                  )
                                }
                              >
                                Suggest Idea
                              </Link>
                            </li>

                            <li className="btn outline me-2">
                              <Link
                                to="#"
                                onClick={() =>
                                  showOpportunityHandler(
                                    jobResponse._id,
                                    jobResponse.submittedbyid
                                  )
                                }
                              >
                                Suggest Opportunity
                              </Link>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      <ViewPublicDataCvModal
        challengeType={challengeType}
        responseCvId={componentId}
      />

      <ViewPublicDataCqModal
        challengeType={challengeType}
        responseCqId={componentId}
      />

      <SaveIdea
        idea={""}
        challengeId={params.id}
        challengeResponseId={selectedResponseId}
        receiverId={selectedUserId}
        setIsIdeaSaved={setIsIdeaSaved}
        showIdea={showIdea}
        setShowIdea={setShowIdea}
      />

      <SaveOpportunity
        opportunity={""}
        challengeId={params.id}
        challengeResponseId={selectedResponseId}
        receiverId={selectedUserId}
        setIsOpportunitySaved={setIsOpportunitySaved}
        showOpportunity={showOpportunity}
        setShowOpportunity={setShowOpportunity}
      />
    </>
  );
};

export default AppliedTab;
