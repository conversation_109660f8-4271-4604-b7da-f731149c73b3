/* eslint-disable */
import React, { useEffect, useState, useContext } from "react";
import { Link, useParams, useHistory } from "react-router-dom";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

//import context
import { GlobalProvider } from "context/GlobalContext";

import AppUserLeftPanel from "../LeftPanel/AppUserLeftPanel";
import AppUserMiddlePanel from "../MiddlePanel/AppUserMiddlePanel";
// import AppUserRightPanel from "../RightPanel/AppUserRightPanel";

import { assetImages } from "constants";
import GiveReviewModal from "components/common/Modals/GiveReviewModal";
import ConnectModal from "../Modals/ConnectModal";
import GreetingsModal from "../Modals/GreetingsModal";
import TagsModal from "components/common/Modals/TagsModal";
import TagsAddedSuccessModal from "components/common/Modals/TagsAddedSuccessModal";
import ReviewAddedSuccessModal from "components/common/Modals/ReviewAddedSuccessModal";
// import InitialMeetingModal from "../Modals/InitialMeetingModal";
// import SetupMeetingModal from "../Modals/SetupMeetingModal";
// import MeetingDetailsModal from "../Modals/MeetingDetailsModal";
// import ScheduleConfirmModal from "../Modals/ScheduleConfirmModal";

const AppUserDetailsBody = () => {
  const { t } = useTranslation(); // translation marker

  const params = useParams();
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess");

  const { reloadContactList, setReloadContactList } =
    useContext(GlobalProvider);

  /* use states for top section */
  const [userName, setUserName] = useState("");

  //meeting requirements
  const [comments, setcomments] = useState("");
  const [meetingReason, setmeetingReason] = useState("");

  const [selectedMeetingDate, setSelectedMeetingDate] = useState("");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("");

  const [leftPanelReload, setLeftPanelReload] = useState(false);
  const [reviewTabReload, setReviewTabReload] = useState(false);

  //resetScheduleMeet
  const resetScheduleMeet = () => {
    setmeetingReason("");
    setcomments("");
    setSelectedMeetingDate("");
    setSelectedTimeSlot("");
  };

  //fucntion for get api
  const reloadUserDetailsComponent = () => {
    setLeftPanelReload(true);
    setReviewTabReload(true);
  };

  useEffect(() => {
    if (reloadContactList) {
      reloadUserDetailsComponent();
      setReloadContactList(false);
    }
  }, [reloadContactList]);

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    moduleAccess.includes("MOD_EXPERT")
  ) {
    return (
      <section className="feed_pnltotal">
        <div className="container-fluid">
          <div className="back_btn">
            <Link to="/peoples">
              <i className="material-icons-outlined">arrow_back</i>
              <span>{t("Back")}</span>
            </Link>
          </div>

          <div className="pnl_outer">
            {/* -------- left panel -------- */}
            <AppUserLeftPanel
              leftPanelReload={leftPanelReload}
              setLeftPanelReload={setLeftPanelReload}
            />

            {/* ------ mdddle panel ------- */}
            <AppUserMiddlePanel
              reviewTabReload={reviewTabReload}
              setReviewTabReload={setReviewTabReload}
            />

            {/* ------ right panel ------- */}
            {/* <AppUserRightPanel similarProfileList={similarProfileList} /> */}
          </div>
        </div>

        {/* ====== popup and modals area ======== */}

        {/* ------- give review modal ---- */}
        <GiveReviewModal moduleName="appuser" />

        <ReviewAddedSuccessModal
          afterModalClose={() => {
            setLeftPanelReload(true);
            setReviewTabReload(true);
          }}
        />

        {/* ------ connect modal -------- */}
        <ConnectModal />

        {/* -------- greetings modal -------- */}
        <GreetingsModal afterModalClose={reloadUserDetailsComponent} />

        {/* --------- tags modal ----------- */}
        <TagsModal moduleName="appuser" selectedId={params.id} />

        {/* --------- tags added success / greetings modal ------------- */}
        <TagsAddedSuccessModal
          afterTagModalClose={() => {
            setLeftPanelReload(true);
          }}
        />

        {/* <InitialMeetingModal
          personName={userName}
          setcomments={setcomments}
          setmeetingReason={setmeetingReason}
        />

        <SetupMeetingModal
          personName={userName}
          setSelectedMeetingDate={setSelectedMeetingDate}
          setSelectedTimeSlot={setSelectedTimeSlot}
          selectedTimeSlot={selectedTimeSlot}
          setcomments={setcomments}
          setmeetingReason={setmeetingReason}
        />

        <MeetingDetailsModal
          personName={userName}
          setSelectedMeetingDate={setSelectedMeetingDate}
          selectedMeetingDate={selectedMeetingDate}
          setSelectedTimeSlot={setSelectedTimeSlot}
          selectedTimeSlot={selectedTimeSlot}
          meetingReason={meetingReason}
          comments={comments}
        />

        <ScheduleConfirmModal
          personName={userName}
          selectedTimeSlot={selectedTimeSlot}
          selectedMeetingDate={selectedMeetingDate}
          onModalClose={resetScheduleMeet}
        /> */}
      </section>
    );
  } else {
    return (
      <section className="feed_pnltotal">
        <div className="empty_access text-center">
          <div className="empty_pic mb-4">
            {" "}
            <img src={assetImages.emptyVector} alt="" />
          </div>
          <div className="empty_text">
            <p className="fs-lg text-gray fw-semibold mb-4">
              Sorry....! You don't have privilege to see this content
            </p>
          </div>
        </div>
      </section>
    );
  }
};

export default AppUserDetailsBody;
