
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
/* font-family: 'Inter', sans-serif;*/
@import url('https://fonts.googleapis.com/css2?family=Tilt+Warp&display=swap');
 /* font-family: 'Tilt Warp', cursive; */
 @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap');
 /* font-family: 'Roboto', sans-serif; */

.material-icons-outlined, .material-icons, .material-icons-round{vertical-align: middle;}
/* ===common-css---- */
/*----------buttons--------------*/
.btn-check:focus+.btn, .btn:focus{box-shadow: none;}
.btn {display: inline-flex;align-items: center;justify-content: center;height: 60px;padding: 0 30px;border-radius: 90px;color: var(--bs-color-00);background-color: var(--bs-color-4E);border: 1px solid transparent;box-shadow: none;outline: none;font-weight: 600;    font-size: 18px;line-height: 28px;position: relative;min-width: 90px;gap: 5px;}
.btn:hover{background-color: var(--bs-color-9A);}
.btn.btn-primary{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.btn.outline{border: 1px solid var(--bs-color-38);color: var(--bs-color-38);background-color: transparent;height: 36px;font-size: 14px;padding: 0 15px;}
.btn.outline span{display: inline-block;vertical-align: middle;}
.btn.outline:hover{border-color: var(--bs-color-BB);}
.btn.outline:hover span img{filter: brightness(100);}
.btn.black_btn{background-color: var(--bs-color-00);color: var(--bs-color-FF);}
.btn.black_btn:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.btn.white_btn{background-color: var(--bs-color-FF);color: var(--bs-color-38);}
.btn.white_btn:hover{background-color: var(--bs-color-DC);}
.btn.gray_btn{border: 1px solid var(--bs-color-5A);color: var(--bs-color-5A);background-color: transparent;height: 36px;font-size: 14px;padding: 0 15px;}
.btn.gray_btn:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);border-color: var(--bs-color-38);}
.btn.gray{background-color: var(--bs-color-ED);color: var(--bs-color-5A);}
.btn.gray:hover{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
.requested_btn span{opacity: 0.6;font-weight: 400;}
.btn .material-icons-outlined, .btn .material-icons{font-size: 20px;}
/*----------buttons--------------*/
/* =============selectbox============== */

.select2-container{width: 100% !important;display: block;}
.select2-container--open{z-index: 9999;}
.select2-container--default .select2-selection--single {background-color: var(--bs-color-ED);border: 1px solid transparent;border-radius: 5px;font-size: 16px;color: var(--bs-color-8E);height: 50px;display: flex;align-items: center; font-weight: 400; text-transform: capitalize;padding: 0 20px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{padding: 0;color: var(--bs-color-00);}
.select2-container--default .select2-selection--single .select2-selection__arrow b{border: 0;}
.select2-container--default .select2-selection--single .select2-selection__arrow{height: 12px;width: 12px;background-image: url(../images/select-arw.svg);background-repeat: no-repeat;background-position: center;margin: 0;top: 50%;transform: translateY(-50%);right: 15px;}

.select2-dropdown {background-color: var(--bs-color-FF);border: 1px solid transparent !important;border-radius: 10px;box-shadow: 0 0 20px rgba(0,0,0,.15);padding: 10px 0;}

.select2-container--default .select2-search--dropdown .select2-search__field {border: 1px solid var(--bs-color-ED);border-radius: 5px;font-weight: 500;
    height: 35px;}
.select2-container--default .select2-search--dropdown .select2-search__field:focus{border-color: var(--bs-color-4E);}
.select2-results__option{padding: 8px 20px;color: var(--bs-body-color);transition: 0.3s;font-weight: 500;font-size: 14px;}
.select2-container--default .select2-results__option--selected {background: var();color: var(--bs-color-24);font-size: 14px;
    transition: 0.3s;font-weight: 500;}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable{background-color: transparent;color: var(--bs-body-color);}
.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable:hover{background-color: var(--bs-color-ED);color: var(--bs-color-00);}

/* =============selectbox============== */

/* ====toggle====default */
.toggle_switch{display: flex;align-items: center;gap: 10px;margin-bottom: 24px;}
.toggle_switch span{font-size: 14px;line-height: 18px;color: var(--bs-color-00);}
.toggle_switch label:after{display: flex;align-items: center;}
.toggle_switch input[type=checkbox]{height: 0;width: 0;visibility: hidden;display: none;}
.toggle_switch label {cursor: pointer;text-indent: -9999px;width: 31px;height: 19px;background: var(--bs-color-ED);display: block;border-radius: 100px;position: relative;}
.toggle_switch label:after {content: '';position: absolute;top: 2.5px;left: 3px;width: 14px;height: 14px;background: var(--bs-color-FF);border-radius: 90px;transition: 0.3s;}
.toggle_switch input:checked + label {background: var(--bs-color-F0);}
.toggle_switch input:checked + label:after {left: calc(100% - 2px);transform: translateX(-100%);}

/* ====toggle====default */

/* Header==start */

/* =====================start--home--header =====*/
.overlay{position: fixed;left: 0;right: 0;top: 0;bottom: 0;background-color: rgba(0, 0, 0, 0.8);z-index: 100;display: none;}
.hamberger_menu, .menu_cross{display: none;}
.home_header{position: absolute;left: 0;right: 0;top: 0;transition: 0.5s;z-index: 99;}
.header_top{background: var(--bs-color-2B);padding: 10px 0;text-align: center;}
.top_text p, .top_text p a{font-weight: 500;font-size: 14px;line-height: 14px;color: var(--bs-color-FF);}
.top_text p a{text-decoration: underline;}
.top_text p a:hover{color: var(--bs-color-F0);}
.header_bttm{padding: 12px 0;background-color: transparent;transition: 0.4s;}
.bttm_innr{display: flex;align-items: center;justify-content: space-between;}
.home_hdrlogo a{display: inline-block;}
.header_right{display: flex;gap: 30px;align-items: center;}
.menu_list ul{display: flex;align-items: center;gap: 30px;}
.menu_list ul li a{font-weight: 500;font-size: 16px;line-height: 28px;color: var(--bs-color-FF);}
.menu_list ul li.active a{color: var(--bs-color-4E);}
.login_btns{display: flex;gap: 10px;align-items: center;}
.login_btns .btn{height: 34px;}
.shrink{position: fixed;background-color:var(--bs-color-38);left: 0;right: 0;top: 0;z-index: 100;}
.menu_list ul li a:hover{color: var(--bs-color-4E);}
.my_accountbtn .btn{padding: 0 20px;height: 50px;}

.news_header{background-position: center;background-repeat: no-repeat;background-size: cover;}
.news_bottm_menu{background-color: rgba(0,0,0,0.1);padding: 24px 0;}
.news_bottm_menu ul{display: flex;align-items: center;gap: 40px;}
.news_bottm_menu ul li a{font-size: 18px;color: var(--bs-color-FF);font-weight: 600;}
/* =====================end--home--header =====*/

.language_row{border-radius: 8px;overflow: hidden;}
.language_row a{width: 33.33%;text-align: center;height: 40px;padding: 0 8px;background-color: var(--bs-color-E5);border-right: 2px solid var(--bs-color-FF);display: flex;align-items: center;gap: 4px;justify-content: center;font-size: 14px;font-weight: 600;color: var(--bs-color-5A);text-transform: uppercase;}
.language_row a:last-child{border-right: 0;}
.language_row a.active{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
/* ====end--header--part====== */


/* =====start---footer==== */

.footer_outer{background-position: center;background-size: cover;background-repeat: no-repeat;background-color: var(--bs-color-00);padding: 100px 0;position: relative;}
.copyright p{font-weight: 400;font-size: 18px;line-height: 28px;color: var(--bs-color-FF);}
.ftr_item h3{font-weight: 600; font-size: 26px;line-height: 38px;margin-bottom: 29px;color: var(--bs-color-FF);}
.ftr_links li{margin-bottom: 15px;width: 50%;}
.ftr_links li:last-child{margin: 0;}
.ftr_links li a{font-weight: 400;font-size: 18px;line-height: 28px;color: var(--bs-color-FF);}
.ftr_links li a:hover{color: var(--bs-color-4E);}
.footer_top{padding-bottom: 100px;}
.ftr_socialpnl{display:flex;align-items:center;justify-content: flex-end;gap: 20px;}
.ftr_social ul{display:flex;align-items:center}
.ftr_social ul li{margin-right:15px}
.ftr_social ul li a{border-radius:6px;height:60px;width:60px;display:flex;align-items:center;justify-content:center;background: rgba(255, 255, 255, 0.1);border-radius: 50%;}
.ftr_social ul li a:hover{background-color: var(--bs-color-4E);border-radius: 50% 0;}
.ftr_social ul li a:hover img{filter: invert(1);}

.flg_selctBtn .select2-container--default .select2-selection--single{min-width:153px;background-color: rgba(255, 255, 255, 0.10);height: 40px;border-radius: 10px;}
.flg_selctBtn .select2-container--default .select2-selection--single .select2-selection__rendered{color: var(--bs-color-FF);}
.flg_selctBtn .select2-container--default .select2-selection--single .select2-selection__arrow{background-image: url(../images/select-arw-w.svg);}
.footer_bttm .row{align-items: center;}
.browse_text{text-align: right;}
.browse_text p, .browse_text p a{font-weight: 500;font-size: 14px;line-height: 18px;color: var(--bs-color-FF);}
.browse_text p a{text-decoration: underline;display: inline-block;}
.browse_text p a:hover{color: var(--bs-color-4E);}
.footer_logo{margin-bottom: 40px;}
.ftr_dots1{left: 0;}
.ftr_dots2{right: 0;}
.footer_outer .help_dots {bottom: 0;top: inherit;display: none;}

/* =end--footer---- */


/*===checkbox--radio-button--======*/
.radio-check{margin:0;cursor:pointer;display:flex;align-items:center}
.radio-check input[type='checkbox'], .radio-check input[type='radio'] {display: none;}
.radio-check input[type=checkbox]+span{width:19px;min-width:19px;height:19px;border:2px solid var(--bs-color-8E);border-radius:4px;background:var(--bs-color-FF);position:relative;margin-right:10px}
.radio-check input[type=checkbox]:checked+span:after{content:"";position:absolute;left:1px;top:1px;right:1px;bottom:1px;background:url(../images/check.svg) no-repeat center center;background-size:15px}
.radio-check input[type=radio]+span {width: 16px;min-width: 16px;height: 16px;border: 2px solid var(--bs-body-color);border-radius: 50%;    background: var(--bs-color-FF);position: relative;margin-right: 10px;background-color: transparent;}
.radio-check input[type=radio]:checked+span:after {content: "";position: absolute; left: 50%;top: 50%; background-color: var(--bs-color-F0);transform: translate(-50%, -50%);border-radius: 50%;height: 8px;width: 8px; 
}
.radio-check input[type=radio]:checked+span{border-color: var(--bs-color-F0);}

/*===checkbox-radio-button-======*/


/* ===common-css---- */

/* -------pagination----- */
.job_pagination {padding: 30px 0 0;}
.job_pagination ul {display: flex;align-items: center;justify-content: center;gap: 20px;}
.job_pagination ul li.prev_pagnation a, .job_pagination ul li.next_pagination a {background: var(--bs-color-FF);box-shadow: 0px 2px 12px rgb(29 28 76 / 12%);border-radius: 50%;height: 45px;width: 45px;}
.job_pagination ul li a {height: 35px;width: 38px; border-radius: 50%;font-size: 16px; color: var(--bs-color-00); display: flex;align-items: center;justify-content: center;border: 1px solid transparent;}
.job_pagination ul li.active a {background: var(--bs-color-38); color: var(--bs-color-FF);font-weight: 600;border-color: var(--bs-color-38);}

/* end-pagination */


@media(min-width:1600px){
    .footer_outer .help_dots{display: block;}
}
@media(max-width:1600px){
    .btn{font-size: 16px;height: 55px;}
    .ftr_social ul li a{height: 44px;width: 44px;}
    .ftr_social ul li a img{transform: scale(0.7);}
    .ftr_item h3 {font-size: 18px;line-height: 28px;margin-bottom: 20px;}
    .ftr_links li a{font-size: 15px;line-height: 24px;}
    .copyright p{font-size: 16px;}
    .select2-container--default .select2-selection--single{font-size: 14px;}
    .footer_outer{padding: 70px 0;}
}
@media(max-width:1199px){
    .menu_list ul, .header_right {gap: 16px;}
    .menu_list ul li a{font-size: 16px;line-height: 24px;}
    .footer_top {padding-bottom: 40px;}
}

@media(max-width:991px){
    .menu_list{position: fixed;background-color: var(--bs-color-38);box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);width: 260px;transition: 0.4s;transform: translateX(100%); top: 0;bottom: 0;right: 0;padding: 60px 0 15px;z-index: 101;}
    .hamberger_menu a{display: inline-block;}
    .header_right{gap: 15px;}
    .menu_list ul{display: block;gap: 0;}
    .menu_list ul li{display: block;}
    .hamberger_menu, .menu_cross{display: flex;}
    .add_menupnl .overlay{display: block;}
    .add_menupnl .menu_list{transform: translateX(0);}
    .add_menupnl{height: 100vh;overflow: hidden;}
    .menu_cross{background-color: var(--bs-color-4E);color: var(--bs-color-24);height: 35px;width: 35px;display: flex;align-items: center;justify-content: center;border-radius: 50%;position: absolute;right: 15px;top: 15px;}
    .menu_list ul li a{padding: 8px 20px;display: block;color: var(--bs-color-FF);}
    .menu_list ul li a:hover, .menu_list ul li.active a{background-color: var(--bs-color-4E);color: var(--bs-color-00);}
    .addons_tabLinks ul li a{padding: 0 16px;}
    .footer_outer{padding: 40px 0;}
    .btn {padding: 0 20px;height: 44px;font-size: 14px;}
    .footer_bttm .col-lg-6:first-child{order: 2;}
    .footer_bttm .col-lg-6:last-child{order: 1;}
    .ftr_socialpnl{justify-content: center;margin-bottom: 20px;}
    .copyright{text-align: center;}
    .ftr_logopnl{text-align: center;margin-bottom: 30px;}
    .ftr_social ul{justify-content: center;}
    .news_bottm_menu {padding: 12px 0;}
    .news_bottm_menu ul li a {font-size: 14px;}
    .news_bottm_menu ul {overflow-x: auto;overflow-y: hidden;}
}

@media(max-width:767px){
    .job_pagination ul{gap: 6px;}
    .job_pagination ul li.prev_pagnation a, .job_pagination ul li.next_pagination a {height: 36px; width: 36px;}
    .job_pagination ul li a {height: 36px; width: 36px;font-size: 14px;}
    .ftr_socialpnl{justify-content: center;}
    .hdr_srchbar{display: none;}
    .srch_icon{display: block;margin-left: auto;color: var(--bs-color-FF);}
    .loginuser_area{margin: 0 0 0 15px;}
    .srch_icon button i{color: var(--bs-color-FF);}
    .hdr_logo {margin-left: 5px;}
    .addons_tabLinks ul li a{padding: 0 15px;white-space: nowrap;}
    .addons_tabLinks ul{flex-wrap: nowrap;overflow-x: auto;}
    .border_btn, .basic_btn{height: 40px;}
    .menu_bar a span {font-size: 26px;}
    .header_top{display: none;}
    .ftr_links li {margin-bottom: 20px;}
    .ftr_item h3{margin-bottom: 20px;}
    .ftr_item{margin-bottom: 20px}
    .footer_top {padding-bottom: 0;}
    .browse_text {text-align: center;}
    .btn {font-size: 14px;}
    .news_bottm_menu ul {gap: 24px;}
    .top_filter_row{flex-wrap: wrap;gap: 16px 0;}
    .left_text{width: 100%;}
    .filter_right{min-width: 100%;width: 100%;}
    .filter_select {min-width: auto;width: 100%;}
}
@media(max-width:575px){
    .inner_tab_links li a span em{display: none;}
    .login_btns .btn { height: 34px; padding: 0 10px; font-size: 12px;}
    .home_hdrlogo a{max-width: 175px;}
    .btn{padding: 0 20px;font-size: 14px;height: 42px;min-width: 85px;}
    .ftr_socialpnl{flex-wrap: wrap;}
    .flg_selctBtn {width: 100%;}
}