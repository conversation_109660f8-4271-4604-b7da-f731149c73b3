/* eslint-disable */
import { useState } from "react";
import { Link, useParams } from "react-router-dom";

import OverviewTab from "./CompanyDetailsRightPanelTabs/OverviewTab/OverviewTab";
import TeamTab from "./CompanyDetailsRightPanelTabs/TeamTab/TeamTab";
import ReviewsTab from "./CompanyDetailsRightPanelTabs/ReviewsTab/ReviewsTab";
import PeoplesTab from "./CompanyDetailsRightPanelTabs/PeoplesTab/PeoplesTab";
import ProductTab from "./CompanyDetailsRightPanelTabs/ProductTab/ProductTab";

/**
 * CompanyDetailsRightPanel - Main component for company detail tabs
 *
 * Features:
 * 1. Tab-based navigation between company information sections
 * 2. Persistent tab state using React Router hash links
 * 3. Responsive tab rendering with active state management
 */
const CompanyDetailsRightPanel = ({
  companyPanelReload = false,
  setcompanyPanelReload = () => {},
}) => {
  // Tab configuration - centralized for easy maintenance
  const tabs = [
    { id: "overview", label: "Overview", component: OverviewTab },
    { id: "team", label: "Team", component: TeamTab },
    { id: "peoples", label: "Peoples", component: PeoplesTab },
    { id: "products", label: "Portfolio", component: ProductTab },
    { id: "reviews", label: "Reviews", component: ReviewsTab },
  ];

  // Active tab state with 'overview' as default
  const [activeTab, setActiveTab] = useState("overview");

  /**
   * Handle tab selection
   * @param {string} tabId - ID of the selected tab
   */
  const tabSelectionHandler = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="pnl_mdlpnl">
      <div className="people_item">
        {/* Tab Navigation Bar */}
        <div className="tab_links people_tablinks">
          <ul className="nav nav-tabs list_stye_none">
            {tabs.map((tab) => (
              <li className="nav-item" key={tab.id}>
                <Link
                  className={`nav-link ${
                    activeTab === tab.id ? "active show" : ""
                  }`}
                  to="#"
                  onClick={() => {
                    tabSelectionHandler(tab.id);
                  }}
                >
                  {tab.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Tab Content Area */}
        <div className="tab-content">
          {tabs.map((tab) => {
            const TabComponent = tab.component;
            return (
              <div
                key={tab.id}
                className={`tab-pane fade ${
                  activeTab === tab.id ? "show active" : ""
                }`}
                id={tab.id}
              >
                <TabComponent
                  tabName={activeTab === tab.id ? tab.id : ""}
                  {...(activeTab === "reviews" && {
                    companyPanelReload,
                    setcompanyPanelReload,
                  })}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsRightPanel;
