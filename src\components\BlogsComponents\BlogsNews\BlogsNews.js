import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const BlogsNews = () => {
  // getting the route location
  const location = useLocation();
  return (
    <>
      {location.pathname === '/bloglisting' ? (
        <div className="landing_hdng_row">
          <div className="page_hdng">
            <div className="details_arw green_back">
              <Link to="/bloglanding">
                <span className="material-icons-outlined">arrow_back</span>
              </Link>
            </div>
            <h2>NEWS</h2>
          </div>
        </div>
      ) : (
        <div className="landing_hdng_row">
          <div className="page_hdng">
            <h2>NEWS</h2>
          </div>
          <div className="details_arw">
            <Link to="/bloglisting">
              <span className="material-icons-outlined">arrow_forward</span>
            </Link>
          </div>
        </div>
      )}
    </>
  );
};

export default BlogsNews;
