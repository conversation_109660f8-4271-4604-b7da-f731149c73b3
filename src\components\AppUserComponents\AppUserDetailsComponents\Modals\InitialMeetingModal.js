/* eslint-disable */
import React, { useState } from "react";
import { Link } from "react-router-dom";
import Select from "react-select";

import MeetingReasonOptions from "Data/MeetingReason.json";

const InitialMeetingModal = ({
  personName = "",
  setcomments = () => {},
  setmeetingReason = () => {},
}) => {
  const [meetingReasonValue, setmeetingReasonValue] = useState(null);
  const [meetingReasonString, setmeetingReasonString] = useState("");
  const [commentMessage, setcommentMessage] = useState("");

  const [reasonError, setreasonError] = useState(false);
  const [commentError, setcommentError] = useState(false);

  const scheduleHandler = () => {
    if (meetingReasonString != "") {
      setreasonError(false);
      setmeetingReason(meetingReasonString);
    } else {
      setreasonError(true);
    }

    if (commentMessage != "") {
      setcommentError(false);
      setcomments(commentMessage);
    } else {
      setcommentError(true);
    }

    if (meetingReasonString != "" && commentMessage != "") {
      let meetingModal = document.querySelector("#intial_meet_modal");
      let modal = bootstrap.Modal.getInstance(meetingModal);
      modal.hide();

      //show register modal
      let myModal = new bootstrap.Modal(
        document.getElementById("meeting_setup")
      );
      myModal.show();
    }

    setmeetingReasonString("");
    setcommentMessage("");
    setmeetingReasonValue(null);
  };

  const resetHandler = () => {
    setcommentError(false);
    setreasonError(false);
    setmeetingReasonString("");
    setcommentMessage("");
    setcomments("");
    setmeetingReason("");
    setmeetingReasonValue(null);
  };

  return (
    <div className="post_modal request_modal">
      <div
        className="modal fade"
        id="intial_meet_modal"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="modal-title">{personName}</h3>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <span className="material-icons-outlined">close</span>
              </button>
            </div>
            <div className="modal-body">
              <label className="fw-bold mb-2 text-black fs-sm">
                Reason of meet
              </label>

              <Select
                placeholder="Select reason of meet"
                options={MeetingReasonOptions}
                isClearable
                value={meetingReasonValue}
                onChange={(val) => {
                  setreasonError(false);
                  setmeetingReasonValue(val);
                  setmeetingReasonString(val.value);
                }}
              />
              {reasonError && (
                <p className="error">
                  <span className="material-icons-outlined">info</span>
                  Please select reason
                </p>
              )}
              <div className="post_description mt-3">
                <label className="fw-bold mb-2 text-black fs-sm">
                  Comments
                </label>
                <textarea
                  className="form-control"
                  placeholder="Comment to meet"
                  value={commentMessage}
                  onChange={(e) => {
                    setcommentMessage(e.target.value);
                    setcommentError(false);
                  }}
                ></textarea>
                {commentError && (
                  <p className="error">
                    <span className="material-icons-outlined">info</span>
                    Please enter comment
                  </p>
                )}
              </div>
              <div className="post_btn">
                <button className="btn outline w-100" onClick={scheduleHandler}>
                  Schedule Meeting
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InitialMeetingModal;
