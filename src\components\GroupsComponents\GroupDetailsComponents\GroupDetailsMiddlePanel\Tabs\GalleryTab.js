/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

// import use translation
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";
import ShowImageModal from "components/common/Modals/ShowImageModal";

const GalleryTab = ({ tabName = "" }) => {
  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const { t } = useTranslation(); // translation marker

  const [isLoading, setIsLoading] = useState(false);
  const [galleryImages, setGalleryImages] = useState([]);
  const [imagePath, setImagePath] = useState("");

  const getGroupDetails = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_GROUP_DETAILS +
        `/${params.id}` +
        `?token=${token}&modulefrom=frontendgallery`;

      // console.log("requestURL", requestURL);

      const response = await getData(requestURL);
      setIsLoading(false);

      // console.log("event details response in gallery tab", response);

      if (response.status) {
        setGalleryImages(
          response.data?.gallery.map(({ name, path, _id, ...other }) => ({
            name,
            path: url.SERVER_URL + path,
            _id,
          }))
        );
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "gallery") {
      getGroupDetails();
    }
  }, [params.id, tabName]);

  if (isLoading) {
    return (
      <div className="portfolio_outer">
        <div className="row">
          {Array.from({ length: 6 }).map((_, index) => (
            <div className="col-lg-4 col-6 mb-4" key={index}>
              <div className="portfolio_pic placeholder-glow">
                <div
                  className="placeholder w-100 h-100 rounded"
                  style={{ aspectRatio: "1/1", minHeight: "150px" }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="portfolio_outer">
      <div className="row">
        {galleryImages.map((image, index) => {
          return (
            <div className="col-lg-4 col-6" key={index}>
              <button
                className="portfolio_pic"
                data-bs-toggle="modal"
                data-bs-target="#chatimagemodal"
                onClick={() => setImagePath(image.path)}
              >
                <img src={image.path} alt="" />
              </button>
            </div>
          );
        })}
      </div>

      <ShowImageModal imagePath={imagePath} setImagePath={setImagePath} />
    </div>
  );
};

export default GalleryTab;
