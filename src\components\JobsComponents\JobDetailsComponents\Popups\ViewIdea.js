/* eslint-disable */
import { useState, useEffect } from "react";

//**------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

const ViewIdea = ({
  idea,
  receiverId,
  challengeId,
  challengeResponseId,
  setIsIdeaSaved,
  showIdea,
  setShowIdea,
}) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [attachments, setAttachments] = useState([]);

  useEffect(() => {
    console.log("idea", idea);
    console.log("showidea", showIdea);

    if (showIdea) {
      if (idea && idea._id) {
        setTitle(idea.title);
        setDescription(idea.description);

        if (idea.uploads && idea.uploads.length > 0) {
          setAttachments(
            idea.uploads.map((file) => ({
              name: file.name,
              path: url.SERVER_URL + file.path,
              _id: file._id
            }))
          );
        } else {
          setAttachments([]);
        }
      } else {
        resetIdea();
      }
    }
  }, [showIdea]);

  const resetIdea = () => {
    setTitle("");
    setDescription("");
    setAttachments([]);
  };
  
  const downloadFile = (fileUrl, fileName) => {
    const a = document.createElement("a");
    a.href = fileUrl;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <div
      className="offcanvas offcanvas-end offcanvas-lg"
      tabIndex="-1"
      id="updateIdeaOffcanvas"
    >
      <div className="offcanvas-header">
        <h3>Suggested Idea</h3>
        <button
          type="button"
          className="btn-close"
          data-bs-dismiss="offcanvas"
          onClick={() => {
            setShowIdea(false);
          }}
        ></button>
      </div>
      <div className="offcanvas-body">
        {/* Title section */}
        <div className="card mb-4">
          <div className="card-header bg-light">
            <h4 className="mb-0">Idea Title</h4>
          </div>
          <div className="card-body">
            <h3 className="text-primary">{title}</h3>
          </div>
        </div>

        {/* Description section */}
        <div className="card mb-4">
          <div className="card-header bg-light">
            <h4 className="mb-0">Idea Description</h4>
          </div>
          <div className="card-body">
            <div dangerouslySetInnerHTML={{ __html: description }} />
          </div>
        </div>

        {/* Attachments section */}
        {attachments && attachments.length > 0 && (
          <div className="card mb-4">
            <div className="card-header bg-light">
              <h4 className="mb-0">Attachments</h4>
            </div>
            <div className="card-body">
              <div className="row attachments">
                {attachments.map((file, index) => (
                  <div className="col-md-4 col-sm-6 mb-3" key={index}>
                    <div className="card h-100">
                      {file.path.match(/\.(jpeg|jpg|gif|png)$/) ? (
                        <img
                          src={file.path}
                          className="card-img-top"
                          alt={file.name}
                          style={{ height: "150px", objectFit: "cover" }}
                        />
                      ) : (
                        <div className="text-center py-4 bg-light">
                          <i className="material-icons-outlined" style={{ fontSize: "4rem" }}>file_copy</i>
                        </div>
                      )}
                      <div className="card-body d-flex flex-column">
                        <h5 className="card-title text-truncate">{file.name}</h5>
                        <div className="mt-auto">
                          <button 
                            className="btn btn-sm btn-outline-primary"
                            onClick={() => downloadFile(file.path, file.name)}
                          >
                            <i className="material-icons-outlined">download</i> Download
                          </button>
                          <a
                            href={file.path}
                            target="_blank"
                            rel="noreferrer"
                            className="btn btn-sm btn-outline-secondary ms-2"
                          >
                            <i className="material-icons-outlined">open_in_new</i> View
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ViewIdea;
