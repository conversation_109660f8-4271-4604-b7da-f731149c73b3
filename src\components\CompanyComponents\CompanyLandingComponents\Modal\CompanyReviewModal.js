import React from 'react';
import { Link } from 'react-router-dom';

const CompanyReviewModal = () => {
  return (
    <div className="process_modal esg_modal">
      <div className="modal fade show" id="company_rvw_modal">
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header text-center">
              <div className="process_hdng w-100">
                <h3>Company Review</h3>
                <p>Please add company details to check report.</p>
              </div>
            </div>
            <div className="modal-body">
              <form>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Your Rating</label>
                    <div className="course_rvw_row">
                      <ul className="list_stye_none">
                        <li className="active_star">
                          <Link to="#">
                            <span className="material-icons-outlined">
                              star
                            </span>
                          </Link>
                        </li>
                        <li>
                          <Link to="#">
                            <span className="material-icons-outlined">
                              star
                            </span>
                          </Link>
                        </li>
                        <li>
                          <Link to="#">
                            <span className="material-icons-outlined">
                              star
                            </span>
                          </Link>
                        </li>
                        <li>
                          <Link to="#">
                            <span className="material-icons-outlined">
                              star
                            </span>
                          </Link>
                        </li>
                        <li>
                          <Link to="#">
                            <span className="material-icons-outlined">
                              star
                            </span>
                          </Link>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="form-group">
                    <label>Prositive </label>
                    <textarea
                      className="form-control"
                      placeholder="Enter comments"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Negatives </label>
                    <textarea
                      className="form-control"
                      placeholder="Enter comments"
                    ></textarea>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <Link to="#" data-bs-dismiss="modal" className="btn gray">
                    Cancel
                  </Link>
                  <Link to="#" className="btn">
                    Save
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyReviewModal;
