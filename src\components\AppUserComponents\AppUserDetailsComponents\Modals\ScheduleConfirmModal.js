import React from "react";
import {
  getConvertedDateText,
  // getNextHourSlot,
} from "helper/EventHelper/EventHelper";

const ScheduleConfirmModal = ({
  personName,
  meetingStartTime = "",
  meetingEndTime = "",
  meetingDuration = "",
  selectedMeetingDate,
  onModalClose = () => {},
}) => {
  return (
    <div className="post_modal request_modal">
      <div
        className="modal fade"
        id="schedule_confirm"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-body text-center">
              <div className="gretings_icon mb-3">
                <span className="material-icons-outlined">task_alt</span>
              </div>
              <h4 className="fw-bold mb-2">YOU ARE SCHEDULED</h4>
              <p>
                A calendar invitation has been sent to user's email address.
              </p>
              <div className="px-3 py-4 my-3 border rounded-3">
                <p className="fw-bold">{personName}</p>
                <h3 className="mb-2">{meetingDuration} Hours Meeting</h3>
                <p className="d-flex align-items-center justify-content-center gap-1 fw-bold mb-2">
                  <span
                    className="d-block material-icons-outlined text-primary"
                    style={{ fontSize: "18px" }}
                  >
                    calendar_month
                  </span>
                  <span className="d-block">
                    {meetingStartTime} - {meetingEndTime},{" "}
                    {getConvertedDateText(selectedMeetingDate)}
                  </span>
                </p>
                <p className="d-flex align-items-center justify-content-center gap-1 fw-bold">
                  <span
                    className="d-block material-icons-outlined text-primary"
                    style={{ fontSize: "18px" }}
                  >
                    language
                  </span>
                  <span className="d-block">India Standard Time</span>
                </p>
              </div>
              <div className="post_btn">
                <button
                  className="btn outline w-auto"
                  data-bs-dismiss="modal"
                  onClick={onModalClose}
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleConfirmModal;
