/* eslint-disable */
import AuthHeader from "components/common/AuthHeader";
import React, { useContext, useEffect, useState } from "react";

import AppUserListBody from "components/AppUserComponents/AppUserListComponents/AppUserListBody/AppUserListBody";

const AppUserList = () => {
  useEffect(() => {
    document.title = "App User";
  }, []);

  return (
    <>
      <AuthHeader moduleName="appuser" />

      <AppUserListBody />
    </>
  );
};

export default AppUserList;
