/* eslint-disable */
import React from "react";
import { <PERSON> } from "react-router-dom";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

import { assetImages } from "constants";

import * as url from "helper/UrlHelper";

const GroupListCard = ({ groupData }) => {
  const { t } = useTranslation(); // translation marker

  const memberLimit = 5; //for displaying the limit number of members

  return (
    <div className="groups_item">
      <div className="top_company_row company_top">
        <div className="left_company">
          {/* ----- group name start ----- */}
          <div className="event_hdng">
            <h3>{groupData.title}</h3>
          </div>
          {/* ----- group name end ----- */}

          <div className="event_tags">
            <ul className="list_stye_none">
              {/* ----- group review and rating start ----- */}
              <li>
                <i className="material-icons-outlined ornge_icon">star</i>
                <span className="rating">
                  {groupData.totalratings ?? "0.0"}{" "}
                  <em>({groupData.totalreviews ?? "0"})</em>
                </span>
              </li>
              {/* ----- group review and rating end ----- */}

              {/* ----- group location start ----- */}
              <li
                className={
                  groupData.city === "" &&
                  groupData.state === "" &&
                  groupData.country === ""
                    ? "d-none"
                    : ""
                }
              >
                <i className="material-icons-outlined">place</i>
                <span>
                  {groupData.city !== "" ? groupData.city : ""} ,{" "}
                  {groupData.state !== "" ? groupData.state : ""} ,{" "}
                  {groupData.country !== "" ? groupData.country : ""}
                </span>
              </li>
              {/* ----- group location end ----- */}
            </ul>
          </div>
        </div>
        {/* ----- group logo start ----- */}
        {groupData.grouplogo === "" ? (
          <span
            className="placeholder rounded-8"
            style={{ width: "80px", height: "80px" }}
          ></span>
        ) : (
          <div className="company_logo">
            <img
              className="thumb_logo"
              src={url.SERVER_URL + groupData.grouplogo}
              alt=""
            />
          </div>
        )}
        {/* ----- group logo end ----- */}
      </div>

      {/* ----- group summary start ----- */}

      <div className="event_para">
        <p>{groupData.summary}</p>
      </div>

      {/* ----- group summary end ----- */}

      <div className="company_info_outer">
        {/* group category start  */}
        {groupData.categorylist.length > 0 ? (
          <div className="company_info_row">
            <label>{t("Categories")}</label>

            <div className="event_tags">
              <ul className="list_stye_none">
                {groupData.categorylist.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">{item.name}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ) : null}

        {/* ----- group search start ----- */}
        {groupData.searchinglist.length > 0 ? (
          <div className="company_info_row">
            <label>{t("Search")}</label>

            <div className="event_tags">
              <ul className="list_stye_none">
                {groupData.searchinglist.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">{item.name}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ) : null}
        {/* ----- group search end ----- */}

        {/* ----- group interest start ----- */}
        {groupData.interestlist.length > 0 ? (
          <div className="company_info_row">
            <label>Interest</label>

            <div className="event_tags">
              <ul className="list_stye_none">
                {groupData.interestlist.map((item, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">{item.name}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        ) : null}
        {/* ----- group interest end ----- */}
      </div>

      {/* ----- group members start ----- */}
      <div className="event_user grps_user">
        <ul className="list_stye_none">
          {groupData.groupmemberlist
            .slice(0, memberLimit)
            .map((member, index) => {
              return (
                <li key={index}>
                  <Link to="#">
                    <img
                      height={40}
                      width={40}
                      className="rounded-circle"
                      src={
                        member.groupmemberimage === ""
                          ? assetImages.defaultUser
                          : url.SERVER_URL + member.groupmemberimage
                      }
                      alt=""
                    />
                  </Link>
                </li>
              );
            })}
          <li>
            {groupData.groupmembers.length > memberLimit ? (
              <Link to="#">
                +{groupData.groupmembers.length - memberLimit} more
              </Link>
            ) : null}
          </li>
        </ul>
      </div>
      {/* ----- group members end ----- */}

      <div className="details_btn">
        <Link to={`/groups/details/${groupData._id}`} className="btn outline">
          View Details
        </Link>
      </div>
    </div>
  );
};

export default GroupListCard;
