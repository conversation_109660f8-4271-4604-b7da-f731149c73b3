/* eslint-disable */
import { useState, useEffect } from "react";

import { Link, useParams } from "react-router-dom";

//**------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData, putData, uploadMultipleFile } from "utils/Gateway";
import { Height } from "@mui/icons-material";

const ViewOpportunity = ({
  opportunity,
  receiverId,
  challengeId,
  challengeResponseId,
  setIsOpportunitySaved,
  showOpportunity,
  setShowOpportunity,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState(0);
  const [promisePercent, setPromisePercent] = useState(0);
  const [items, setItems] = useState([]);
  const [currency, setCurrency] = useState("euro");
  const [formattedDate, setFormattedDate] = useState("");

  // if show opportunity map the data
  useEffect(() => {
    if (showOpportunity && opportunity && opportunity._id) {
      setTitle(opportunity.title || "");
      setDescription(opportunity.description || "");
      setPrice(opportunity.amount || 0);
      setPromisePercent(opportunity.promisepercent || 0);
      setCurrency(opportunity.currency || "euro");

      // Format creation date
      if (opportunity.adddate) {
        const date = new Date(opportunity.adddate);
        setFormattedDate(date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'long', 
          year: 'numeric'
        }));
      }

      // Process invoice items
      if (opportunity.invoiceitems && opportunity.invoiceitems.length > 0) {
        setItems(opportunity.invoiceitems.map(item => ({
          description: item.description || "",
          quantity: item.unit || 0,
          price: item.price || 0,
          tax: item.taxpercent || 0,
          amount: item.totalprice || 0
        })));
      } else {
        setItems([]);
      }
    } else {
      resetHandler();
    }
  }, [showOpportunity, opportunity]);

  // reset all states
  const resetHandler = () => {
    setTitle("");
    setDescription("");
    setPrice(0);
    setPromisePercent(0);
    setItems([]);
    setFormattedDate("");
  };

  // Format currency display
  const formatCurrency = (amount) => {
    const currencySymbol = currency === "euro" ? "€" : "$";
    return `${currencySymbol} ${parseFloat(amount).toFixed(2)}`;
  };

  return (
    <div
      className="offcanvas offcanvas-end offcanvas-xl"
      tabIndex="-1"
      id="updateOpportunityOffcanvas"
    >
      <div className="offcanvas-header border-bottom">
        <h3 className="fw-bold">Opportunity Details</h3>
        <button
          type="button"
          className="btn-close"
          data-bs-dismiss="offcanvas"
          onClick={() => {
            setShowOpportunity(false);
          }}
        ></button>
      </div>
      <div className="offcanvas-body p-4">
        <div className="container-fluid px-0">
          {/* Invoice Header */}
          <div className="row mb-4">
            <div className="col-md-8">
              <div className="card border-0">
                <div className="card-body ps-0">
                  <h2 className="mb-1 text-primary fw-bold">{title}</h2>
                  {formattedDate && <p className="text-muted mb-2">Created on {formattedDate}</p>}
                  {description && (
                    <div className="mt-3">
                      <h5 className="text-secondary">Description</h5>
                      <p>{description}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="col-md-4 text-md-end">
              <div className="bg-light p-3 rounded">
                <h4 className="fw-bold mb-2">Total Amount</h4>
                <h2 className="text-primary fw-bold mb-0">{formatCurrency(price)}</h2>
                {promisePercent > 0 && (
                  <div className="mt-2 text-success">
                    <span className="badge bg-success-light text-success">
                      {promisePercent}% Promise
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Item Details */}
          <div className="card shadow-sm mb-4">
            <div className="card-header bg-light">
              <h4 className="mb-0 fw-bold">Opportunity Items</h4>
            </div>
            <div className="card-body p-0">
              <div className="table-responsive">
                <table className="table table-striped mb-0">
                  <thead className="table-light">
                    <tr>
                      <th style={{width: "40%"}}>Description</th>
                      <th style={{width: "15%"}}>Quantity</th>
                      <th style={{width: "15%"}}>Price</th>
                      <th style={{width: "15%"}}>Tax</th>
                      <th style={{width: "15%"}} className="text-end">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {items.length > 0 ? (
                      items.map((item, index) => (
                        <tr key={index}>
                          <td className="text-wrap">{item.description}</td>
                          <td>{item.quantity}</td>
                          <td>{formatCurrency(item.price)}</td>
                          <td>{item.tax}%</td>
                          <td className="text-end fw-bold">{formatCurrency(item.amount)}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="5" className="text-center py-3">
                          <em>No items available</em>
                        </td>
                      </tr>
                    )}
                  </tbody>
                  {items.length > 0 && (
                    <tfoot className="table-light">
                      <tr>
                        <td colSpan="4" className="text-end fw-bold">Subtotal:</td>
                        <td className="text-end fw-bold">{formatCurrency(
                          items.reduce((sum, item) => sum + (item.amount || 0), 0)
                        )}</td>
                      </tr>
                      {/* If we need to show tax separately
                      <tr>
                        <td colSpan="4" className="text-end">Tax:</td>
                        <td className="text-end">{formatCurrency(
                          items.reduce((sum, item) => {
                            const subtotal = (item.quantity || 0) * (item.price || 0);
                            return sum + (subtotal * ((item.tax || 0) / 100));
                          }, 0)
                        )}</td>
                      </tr>
                      */}
                      <tr className="table-primary">
                        <td colSpan="4" className="text-end fw-bold">Total Amount:</td>
                        <td className="text-end fw-bold">{formatCurrency(price)}</td>
                      </tr>
                    </tfoot>
                  )}
                </table>
              </div>
            </div>
          </div>

          {/* Payment Terms */}
          {promisePercent > 0 && (
            <div className="card shadow-sm mb-4">
              <div className="card-header bg-light">
                <h4 className="mb-0 fw-bold">Promise Terms</h4>
              </div>
              <div className="card-body">
                <p>
                  This opportunity includes a promise percentage of <strong>{promisePercent}%</strong> of the total amount.
                </p>
                <div className="d-flex align-items-center mt-3">
                  <div className="progress flex-grow-1" style={{height: "10px"}}>
                    <div 
                      className="progress-bar bg-success" 
                      role="progressbar" 
                      style={{width: `${promisePercent}%`}}
                      aria-valuenow={promisePercent} 
                      aria-valuemin="0" 
                      aria-valuemax="100"
                    ></div>
                  </div>
                  <span className="ms-2 fw-bold">{promisePercent}%</span>
                </div>
              </div>
            </div>
          )}
          
          {/* Additional terms or notes could go here */}

          <div className="text-end mt-4">
            <button type="button" className="btn btn-secondary" data-bs-dismiss="offcanvas">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewOpportunity;
