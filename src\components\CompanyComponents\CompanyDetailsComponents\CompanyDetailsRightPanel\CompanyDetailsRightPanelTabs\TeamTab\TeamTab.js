/* eslint-disable */
/*------ React imports ------*/
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*------ Helper imports ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

const TeamTab = ({ tabName = "" }) => {
  const { id: companyId } = useParams(); // Renamed for clarity
  const token = localStorage.getItem("token");

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [companyMemberList, setCompanyMemberList] = useState([]);

  //function for get all contacts
  const getAllCompanyMemberList = async () => {
    try {
      setIsLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_COMPANY_MEMBERS +
        `?token=${token}&company=${companyId}&modulefrom=frontend`;

      // console.log("url of company member list------>", requestUrl);

      const response = await getData(requestUrl);

      setIsLoading(false);

      // console.log("response in company member list------>", response);

      if (response.status) {
        setCompanyMemberList(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (companyId && tabName === "team") {
      getAllCompanyMemberList();
    }
  }, [companyId, tabName]);

  if (isLoading) {
    return (
      <div className="post_item_outer">
        <div className="post_item">
          {/* You can map over a dummy array to show multiple placeholders */}
          {[...Array(5)].map((_, index) => (
            <div className="card shadow-sm mb-3" key={index} aria-hidden="true">
              <div className="card-body">
                <div className="placeholder-glow">
                  <div className="d-flex justify-content-between align-items-center">
                    {/* Left Side: Profile Image and Info */}
                    <div className="d-flex align-items-center">
                      {/* Placeholder for the circular profile image */}
                      <span
                        className="placeholder rounded-circle"
                        style={{ width: "80px", height: "50px" }}
                      ></span>

                      <div className="ms-3 w-100">
                        {/* Placeholder for Name (h3) */}
                        <h3 className="card-title mb-2">
                          <span
                            className="placeholder"
                            style={{ width: "100px" }}
                          ></span>
                        </h3>
                        {/* Placeholder for Role (h5) */}
                        <h5 className="card-subtitle">
                          <span
                            className="placeholder"
                            style={{ width: "100px" }}
                          ></span>
                        </h5>
                      </div>
                    </div>

                    {/* Right Side: Button */}
                    <div className="ms-3">
                      {/* Placeholder for the "View Details" button */}
                      <Link
                        to="#"
                        tabIndex="-1"
                        className="placeholder rounded-10"
                        style={{ width: "110px", height: "40px" }}
                      ></Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="post_item_outer">
      <div className="post_item">
        {companyMemberList.map((member, index) => {
          return (
            <div className="white_shadow_bx people_bx" key={index}>
              <div className="people_row">
                <div className="left_people">
                  <div className="people_profile">
                    <img
                      src={
                        member.image !== ""
                          ? url.SERVER_URL + member.image
                          : assetImages.defaultUser
                      }
                      alt=""
                    />
                  </div>
                  <div className="people_profile_info">
                    <h3>{member.name}</h3>
                    <h5>{member.rolestring}</h5>
                  </div>
                </div>
                <div className="right_people_btn">
                  <Link
                    to={`/peoples/details/${member.userid}`}
                    className="btn outline"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TeamTab;
