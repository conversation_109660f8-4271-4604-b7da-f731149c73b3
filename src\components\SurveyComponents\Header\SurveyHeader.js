/* eslint-disable */
import React, { useState, useContext, useEffect } from "react";
import { Link } from "react-router-dom";

//import /images
import { assetImages } from "constants";

//import context
import { GlobalProvider } from "context/GlobalContext";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

const SurveyHeader = () => {
  return (
    <header className="aftre_login_header">
      <>
        <div className="container-fluid">
          <div className="header_inner">
            <div className="left_serch_logo">
              <div className="hdr_hamberger">
                <Link
                  to="#"
                  // onClick={() => {
                  //   document.body.classList.add("sidebar_open");
                  // }}
                >
                  <img src="/images/menu.svg" alt="" />
                </Link>
              </div>
              <Link to="#'" className="small_logo">
                <img src="/images/small-logo.svg" alt="" />
              </Link>
              <div className="header_serach">
                <input
                  type="text"
                  // value=""
                  className="form-control"
                  placeholder="Search "
                />
                <button type="button" className="srch_btn">
                  <span className="material-icons-outlined">search</span>
                </button>
              </div>
            </div>
            <div className="right_user_icon">
              <ul className="list_stye_none">
                <li className="search_btn">
                  <Link to="#">
                    <i className="material-icons-outlined">search</i>
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </>
    </header>
  );
};

export default SurveyHeader;
