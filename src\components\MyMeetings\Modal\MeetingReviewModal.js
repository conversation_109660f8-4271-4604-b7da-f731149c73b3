/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { postData, getData } from "utils/Gateway";

import MeetingReasonOptions from "Data/MeetingReason.json";
import MeetingReasonResponseOptions from "Data/MeetingReasonResponse.json";

const MeetingReviewModal = ({
  moduleName = "agenda",
  moduleId,
  meetingReason,
  onModalClose,
}) => {
  const token = localStorage.getItem("token");

  const starCount = [1, 2, 3, 4, 5];

  const [meetingReasonType, setMeetingReasonType] = useState("");
  const [selectedMeetingResponse, setSelectedMeetingResponse] = useState("");

  // initial useState to save the comment
  const [comments, setComments] = useState("");
  const [pointsOfDiscussion, setPointsOfDiscussion] = useState("");

  // initial useState to save star rating
  const [starRating, setstarRating] = useState("1");

  const [isReviewPosting, setIsReviewPosting] = useState(false);

  const meetingReasonRadioHandler = (event) => {
    setSelectedMeetingResponse(event.target.value);
  };

  //select star rating
  const starRatingHandler = (value) => {
    setstarRating(value);
  };

  // Function to submit the review to the API.
  const reviewSubmitHandler = async () => {
    try {
      setIsReviewPosting(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_SUBMIT_REVIEW +
        `?token=${token}&moduleslug=${moduleName}`;

      let reviewData = {
        comment: comments,
        rating: starRating,
        pointsofdiscussion: pointsOfDiscussion,
        meetingreason: meetingReasonType,
        meetingreasonresponse: selectedMeetingResponse,
        agendaid: moduleId,
      };

      console.log(reviewData);

      const response = await postData(requestURL, reviewData);

      setIsReviewPosting(false);
      console.log(response);

      if (response.status) {
        resetHandler();

        //hide modal
        let reviewModal = document.querySelector("#meeting_feedback_modal");
        let modal = bootstrap.Modal.getInstance(reviewModal);
        modal.hide();

        //display success modal
        let reviewSuccessModal = new bootstrap.Modal(
          document.getElementById("review_success_mdl")
        );
        reviewSuccessModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  // function to reset the star and comments
  const resetHandler = () => {
    setstarRating("1");
    setPointsOfDiscussion("");
    setComments("");
    setstarRating("1");
    onModalClose();
  };

  useEffect(() => {
    if (moduleId) {
      for (let meetingReasonData of MeetingReasonOptions) {
        if (meetingReasonData.value.toString() === meetingReason.toString()) {
          setMeetingReasonType(meetingReasonData.value.toString());
        }
      }
    }
  }, [moduleId]);

  return (
    <div className="post_modal">
      <div
        className="modal fade"
        id="meeting_feedback_modal"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog modal-dialog-centered" role="document">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="text-black fs-xl fw-medium">Feedback</h3>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <span className="material-icons-outlined">close</span>
              </button>
            </div>
            <div className="modal-body">
              <div className="feedback_innr_pnl">
                <div className="input_fill mb-4">
                  <label className="fw-bold mb-3 text-black fs-md">
                    Can it be your {meetingReasonType}?
                  </label>
                  <ul className="list_style_none checkbx_listing d-flex align-items-center gap-4">
                    {MeetingReasonResponseOptions.map((option, index) => {
                      return (
                        <li className="mb-0" key={index}>
                          <label className="radio-check">
                            <input
                              type="radio"
                              name={option.label}
                              value={option.value}
                              checked={selectedMeetingResponse === option.value}
                              onChange={meetingReasonRadioHandler}
                            />
                            <span></span>
                            <p className="fs-sm text-gray fw-regular">
                              {option.label}
                            </p>
                          </label>
                        </li>
                      );
                    })}
                  </ul>
                </div>

                <div className="input_fill mb-4">
                  <label className="fw-bold mb-3 text-black fs-md">
                    Rate how the meeting went on a scale of 1 to 5
                  </label>
                  <ul className="list_style_none rating_star d-flex align-items-center">
                    {starCount.map((star, index) => (
                      <li
                        key={index}
                        className={
                          star.toString() <= starRating.toString()
                            ? "active"
                            : ""
                        }
                      >
                        <Link
                          to="#"
                          onClick={() => starRatingHandler(star.toString())}
                        >
                          <span className="material-icons">star</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="input_fill description_fill mb-4">
                  <label className="fw-bold mb-3 text-black fs-md">
                    Points discussed
                  </label>
                  <textarea
                    className="form-control bg_white"
                    placeholder="Enter points"
                    value={pointsOfDiscussion}
                    onChange={(e) => {
                      setPointsOfDiscussion(e.target.value);
                    }}
                  ></textarea>
                </div>
                <div className="input_fill description_fill mb-4">
                  <label className="fw-bold mb-3 text-black fs-md">
                    Next steps
                  </label>
                  <textarea
                    className="form-control bg_white"
                    placeholder="Enter steps"
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                  ></textarea>
                </div>
                <div className="input_fill">
                  <button
                    className="btn primary-btn w-100"
                    style={{
                      cursor: isReviewPosting ? "not-allowed" : "pointer",
                    }}
                    disabled={isReviewPosting ? true : false}
                    onClick={reviewSubmitHandler}
                  >
                    Give Feedback
                    {isReviewPosting && (
                      <div
                        className="mx-2 spinner-border spinner-border-sm"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MeetingReviewModal;
