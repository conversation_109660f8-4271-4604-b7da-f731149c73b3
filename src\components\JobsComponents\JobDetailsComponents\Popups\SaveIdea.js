/* eslint-disable */
import { useState, useEffect, useRef, useMemo } from "react";
import Jo<PERSON><PERSON><PERSON><PERSON> from "jodit-react"; // Changed to Jodit Editor

//**------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData, putData, uploadMultipleFile } from "utils/Gateway";

const SaveIdea = ({
  idea,
  receiverId,
  challengeId,
  challengeResponseId,
  setIsIdeaSaved,
  showIdea,
  setShowIdea,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [ideaid, setIdeaid] = useState(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [attachments, setAttachments] = useState([]);
  const [uploadedImageIds, setUploadedImageIds] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showThankYouModal, setShowThankYouModal] = useState(false); // New state for thank you modal

  // error state for validation
  const [titleError, setTitleError] = useState(false);
  const [descriptionError, setDescriptionError] = useState(false);
  
  const editor = useRef(null);
  const titleInputRef = useRef(null); // Reference for the title input field

  // const editorCustomConfig = joditEditorConfig();
  const editorCustomConfig = {
    readonly: false,
    placeholder: "",
    minHeight: 400,
    uploader: {
      insertImageAsBase64URI: false,
    },
    defaultActionOnPaste: "insert_as_html",
    defaultLineHeight: 1.5,
    enter: "div",
    statusbar: false,
    sizeLG: 900,
    sizeMD: 700,
    sizeSM: 400,
    toolbarAdaptive: false,
    disablePlugins: [
      "image",
      "video",
      "about",
      "classSpan",
      "file",
      "speechRecognize",
      "copyformat",
      "spellcheck",
    ],
  };

  const editorConfig = useMemo(() => ({
    ...editorCustomConfig,
    // Add this to prevent editor from interfering with modals
    zIndex: 0,
    // Ensure editor releases focus properly
    events: {
      beforeDestruct: function() {
        this.selection && this.selection.removeMarkers();
      }
    }
  }), []);

  useEffect(() => {
    console.log("idea", idea);
    console.log("showidea", showIdea);

    if (showIdea) {
      if (idea && idea._id) {
        setIdeaid(idea._id);
        setTitle(idea.title);
        setDescription(idea.description);

        if (idea.uploads && idea.uploads.length > 0) {
          setUploadedImageIds(idea.uploads.map((file) => file._id));

          setAttachments(
            idea.uploads.map((file) => ({
              name: file.name,
              path: url.SERVER_URL + file.path,
              _id: file._id
            }))
          );
        } else {
          setUploadedImageIds([]);
          setAttachments([]);
        }
      } else {
        resetIdea();
      }
      
      // Focus the title input when the offcanvas is shown
      setTimeout(() => {
        if (titleInputRef.current) {
          titleInputRef.current.focus();
        }
      }, 300); // Short delay to ensure offcanvas is fully shown
    }
  }, [showIdea]);

  // Handle keyboard form submission
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // add validation for title and description
  const validateForm = () => {
    if (!title || title.trim() === "") {
      setTitleError(true);
      if (titleInputRef.current) {
        titleInputRef.current.focus();
      }
      return false;
    } else {
      setTitleError(false);
    }

    if (!description || description.trim() === "") {
      setDescriptionError(true);
      if (editor.current && editor.current.editor) {
        editor.current.editor.selection.focus();
      }
      return false;
    } else {
      setDescriptionError(false);
    }
    return true;
  };
      

  // Actual submission after confirmation
  const handleSubmit = async () => {

    // Validate form
    if (!validateForm()) {
      console.log("Form validation failed");
      return;
    }

    const ideaData = {
      id: ideaid,
      title,
      description,
      uploads: uploadedImageIds,
      receiver: receiverId,
      challenge: challengeId,
      challengeresponse: challengeResponseId,
    };

    console.log("ideaData", ideaData);

    try {
      let requestUrl = url.API_BASE_URL;

      // if ideaId present then update idea else add idea
      if (ideaid) {
        requestUrl += url.API_UPDATE_IDEA + `/${ideaid}?token=${token}`;
      } else {
        requestUrl += url.API_ADD_IDEA + `?token=${token}`;
      }

      const response = await postData(requestUrl, ideaData);

      console.log("idea save response", response);

      console.log("Response Status:", response.status);

      if (response.status) {
        // Show thank you modal
        setShowThankYouModal(true);
        
        // Update parent component if needed
        if(setIsIdeaSaved) setIsIdeaSaved(true);
        
        // close saveIdeaOffcanvas
        setTimeout(() => {
          console.log("closing offcanvas");

          const offcanvasElement = document.getElementById("saveIdeaOffcanvas");
          const bsOffcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
          if (bsOffcanvas) bsOffcanvas.hide();
        }, 100);
      }
    } catch (error) {
      console.error("Error saving idea:", error.message);
    }
  };

  // Close thank you modal and reset form
  const handleCloseThankYouModal = () => {
    setShowThankYouModal(false);
    resetIdea();
  };

  //function for image upload
  const imageUploadHandler = async (e) => {
    const file = e.target.files;
    setIsUploading(true);
    try {
      const requestUrl =
        url.API_BASE_URL + url.API_MULTIPLE_FILE_UPLOAD + `?token=${token}`;

      const response = await uploadMultipleFile(requestUrl, file);

      console.log("upload response-------->", response);

      setIsUploading(false);

      if (response.status) {
        /*----- assign uploaded files with path for display start ------*/
        const uploadedImageValues = response.data.map(
          ({ name, path, _id, ...other }) => ({
            name,
            path: path,
            _id,
          })
        );

        console.log("uploaded images values", uploadedImageValues);

        setAttachments([...attachments, ...uploadedImageValues]);
        setUploadedImageIds([...uploadedImageIds, ...response.data.map((file) => file._id)]);
      }
    } catch (error) {
      console.error(error.message);
      setIsUploading(false);
    }
  };

  const removeAttachment = (index, fileId) => {
    // Remove from state
    const newAttachments = [...attachments];
    newAttachments.splice(index, 1);
    setAttachments(newAttachments);
    
    // Remove from uploaded IDs
    const newUploadedIds = uploadedImageIds.filter(id => id !== fileId);
    setUploadedImageIds(newUploadedIds);
  };

  const resetIdea = () => {
    setIdeaid(null);
    setTitle("");
    setDescription("");
    setAttachments([]);
    setUploadedImageIds([]);
  };

  return (
    <>
      <div
        className="offcanvas offcanvas-end offcanvas-lg"
        tabIndex="-1"
        id="saveIdeaOffcanvas"
      >
        <div className="offcanvas-header">
          <h3>Suggest an Idea</h3>
          <button
            type="button"
            className="btn-close"
            data-bs-dismiss="offcanvas"
            onClick={() => {
              setShowIdea(false);
            }}
          ></button>
        </div>
        <div className="offcanvas-body">
          {/* Added onKeyDown handler to the form for keyboard submission */}
          <form onKeyDown={handleKeyDown}>
            <div className="mb-3">
              <label className="form-label" htmlFor="ideaTitleInput">Suggest a catchy title</label>
              <input
                id="ideaTitleInput"
                type="text"
                name="title"
                className="form-control"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                ref={titleInputRef}
                required
              />
              {titleError && (
                <div className="text-danger">
                  Title is required
                </div>
              )}

            </div>

            <div className="mb-4">
              <label className="form-label" htmlFor="ideaDescription">Describe your idea in details with attachments below</label>

              {descriptionError && (
                <div className="text-danger">
                  Description is required
                </div>
              )}
              
              <JoditEditor
                id="ideaDescription"
                ref={editor}
                value={description}
                config={editorConfig}
                onBlur={(newContent) => {
                  setDescription(newContent);
                }}
                tabIndex={0}
              />
            </div>

            <div className="mb-4">
              <label className="form-label">Add Attachments (if any)</label>
              <div className="file-upload-container">
                <div className="custom-file-upload">
                  <input
                    type="file"
                    id="doc_file_input"
                    onChange={imageUploadHandler}
                    multiple
                  />
                  <label htmlFor="doc_file_input" className="btn btn-sm btn-outline mt-2">
                    <i className="material-icons-outlined me-2">upload_file</i>
                    Select Files
                  </label>
                </div>
              </div>
            </div>

            {/* Preview uploaded images as thumbnail*/}
            {attachments.length > 0 && (
              <div className="mb-4">
                <h5>Uploaded Files</h5>
                <div className="row attachments">
                  {attachments.map((file, index) => (
                    <div className="col-md-4 mb-3" key={index}>
                      <div className="card h-100 attachment-card">
                        <div className="card-body p-2">
                          <div className="attachment-preview text-center">
                            {file.path.match(/\.(jpeg|jpg|gif|png)$/) ? (
                              <img
                                src={file.path.startsWith('http') ? file.path : url.SERVER_URL + '/' + file.path}
                                className="img-fluid attachment-img"
                                alt={file.name}
                              />
                            ) : (
                              <i className="material-icons-outlined fs-2 text-secondary">description</i>
                            )}
                          </div>
                          <h6 className="card-title mt-2 text-truncate">{file.name}</h6>
                          <div className="attachment-actions d-flex justify-content-between mt-2">
                            <a
                              href={file.path.startsWith('http') ? file.path : url.SERVER_URL + '/' + file.path}
                              target="_blank"
                              download
                              className="btn btn-sm btn-outline-primary"
                              title="Download file"
                            >
                              <i className="material-icons-outlined">download</i>
                            </a>
                            <button
                              type="button"
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => removeAttachment(index, file._id)}
                              title="Remove file"
                            >
                              <i className="material-icons-outlined">delete</i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="d-flex justify-content-end mb-3">
              {isUploading ? (
                <button className="btn btn-primary" disabled>
                  <i className="fas fa-spinner fa-spin me-2"></i> Uploading...
                </button>
              ) : (
                <button 
                  type="button" 
                  className="btn btn-primary"
                  onClick={() => {
                      handleSubmit();
                  }}
                >
                  Suggest Idea
                </button>
              )}
            </div>
          </form>
        </div>
      </div>

      {/* Thank You Modal */}
      <div className={`modal fade ${showThankYouModal ? 'show' : ''}`} 
           id="thankYouModal" 
           tabIndex="-1" 
           aria-labelledby="thankYouModalLabel" 
           style={{display: showThankYouModal ? 'block' : 'none'}}
           aria-hidden={!showThankYouModal}>
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title" id="thankYouModalLabel">Thank You!</h5>
              <button type="button" className="btn-close" onClick={handleCloseThankYouModal} aria-label="Close"></button>
            </div>
            <div className="modal-body text-center">
              <div className="mb-4">
                <i className="material-icons-outlined text-success" style={{fontSize: '4rem'}}>check_circle</i>
              </div>
              <h4>Your idea has been submitted successfully!</h4>
              <p className="mb-0">We appreciate your valuable contribution.</p>
            </div>
            <div className="modal-footer justify-content-center">
              <button type="button" className="btn btn-primary" onClick={handleCloseThankYouModal}>
                Continue
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Modal backdrop */}
      {showThankYouModal && (
        <div className="modal-backdrop fade show"></div>
      )}
    </>
  );
};

export default SaveIdea;
