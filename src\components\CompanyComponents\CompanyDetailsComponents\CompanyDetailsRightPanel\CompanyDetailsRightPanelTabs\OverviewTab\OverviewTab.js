/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*------ Helper imports ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { fileDownloadHandler } from "utils/DownloadFile";

const OverviewTab = ({ tabName = "" }) => {
  const { id: companyId } = useParams(); // Renamed for clarity
  const token = localStorage.getItem("token");

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [companyDetails, setCompanyDetails] = useState({
    about: "",
    services: [],
    searching: [],
    interest: [],
    uploads: [],
  });

  /**
   * Fetches company details from API
   * Only triggers when:
   *   - Valid company ID exists
   *   - Active tab is 'overview'
   */
  const fetchCompanyDetails = async () => {
    if (!companyId || tabName !== "overview") return;

    try {
      setIsLoading(true);
      const requestUrl = `${url.API_BASE_URL}${url.API_GET_COMPANY_DETAILS}/${companyId}?token=${token}&modulefrom=${tabName}`;

      const response = await getData(requestUrl);

      console.log("response of company details------>", response);

      if (response?.status && response.data) {
        setCompanyDetails({
          about: response.data.about || "",
          services: response.data.services || [],
          searching: response.data.searching || [],
          interest: response.data.interest || [],
          uploads: response.data.uploads || [],
        });
      }
    } catch (error) {
      console.error("Company details fetch error:", error.message);
      // Consider adding error state handling here
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount and when dependencies change
  useEffect(() => {
    fetchCompanyDetails();
  }, [companyId, tabName]);

  /**
   * Loading Skeleton Component
   * Shows placeholder content while data loads
   */
  const renderLoadingSkeleton = () => (
    <div className="people_detailspnl" aria-hidden="true">
      {/* Details Section Placeholder */}
      <div className="people_details_bx white_shadow_bx placeholder-glow">
        <div className="details_hdng">
          <h4>
            <span className="placeholder col-3"></span>
          </h4>
        </div>
        <div className="results_para">
          {Array.from({ length: 1 }).map((_, index) => (
            <p key={index}>
              <span className="placeholder col-7"></span>
              <span className="placeholder col-4"></span>
              <span className="placeholder col-4"></span>
              <span className="placeholder col-6"></span>
              <span className="placeholder col-8"></span>
              <span className="placeholder col-5"></span>
            </p>
          ))}
        </div>
      </div>

      {/* Accordion Section Placeholder */}
      <div className="people_details_bx white_shadow_bx placeholder-glow">
        <div className="accordian_area">
          <div className="accordion accordion-flush">
            {/* Accordion Item 1 */}
            <div className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed disabled"
                  type="button"
                >
                  <span className="placeholder col-4"></span>
                </button>
              </h2>
            </div>
            {/* Accordion Item 2 */}
            <div className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed disabled"
                  type="button"
                >
                  <span className="placeholder col-5"></span>
                </button>
              </h2>
            </div>
            {/* Accordion Item 3 */}
            <div className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed disabled"
                  type="button"
                >
                  <span className="placeholder col-4"></span>
                </button>
              </h2>
            </div>
          </div>
        </div>
      </div>

      {/* Docs Section Placeholder */}
      <div className="people_details_bx white_shadow_bx placeholder-glow">
        <div className="details_hdng">
          <h4>
            <span className="placeholder col-2"></span>
          </h4>
        </div>
        <ul className="uploaded-files d-flex flex-wrap gap-3 p-0 m-0 mt-3">
          {/* Placeholder File 1 */}
          <li className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 w-100">
            <span className="placeholder col-7"></span>
            <span
              className="placeholder"
              style={{ width: "24px", height: "24px" }}
            ></span>
          </li>
          {/* Placeholder File 2 */}
          <li className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 w-100">
            <span className="placeholder col-5"></span>
            <span
              className="placeholder"
              style={{ width: "24px", height: "24px" }}
            ></span>
          </li>
          {/* Placeholder File 3 */}
          <li className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 w-100">
            <span className="placeholder col-6"></span>
            <span
              className="placeholder"
              style={{ width: "24px", height: "24px" }}
            ></span>
          </li>
        </ul>
      </div>
    </div>
  );

  /**
   * Accordion Section Component
   * Renders collapsible sections for services, searchings, and interests
   */
  const renderAccordionSection = () => {
    // Configuration for accordion items
    const accordionConfig = [
      {
        id: "services",
        title: "Services",
        data: companyDetails.services,
        targetId: "flush-collapseOne",
      },
      {
        id: "searchings",
        title: "Searchings",
        data: companyDetails.searching,
        targetId: "flush-collapseTwo",
      },
      {
        id: "interest",
        title: "Interest",
        data: companyDetails.interest,
        targetId: "flush-collapseThree",
      },
    ];

    return (
      <div className="accordian_area">
        <div className="accordion accordion-flush" id="faq-accordian">
          {accordionConfig.map((section) => (
            <div key={section.id} className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target={`#${section.targetId}`}
                  aria-expanded="false"
                >
                  {section.title}
                </button>
              </h2>
              <div
                id={section.targetId}
                className="accordion-collapse collapse"
                data-bs-parent="#faq-accordian"
              >
                <div className="accordion-body">
                  <div className="experts_skill">
                    <ul className="list_stye_none">
                      {section.data.map((item, index) => (
                        <li key={index}>
                          <Link to="#">{item.name}</Link>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Main render
  return isLoading ? (
    renderLoadingSkeleton()
  ) : (
    <div className="people_detailspnl">
      {/* Company Details Section */}
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>Details</h4>
        </div>
        <div className="results_para">
          {/* Caution: Using dangerouslySetInnerHTML requires sanitized content */}
          {companyDetails.about ? (
            <p dangerouslySetInnerHTML={{ __html: companyDetails.about }} />
          ) : (
            <p>No company description available</p>
          )}
        </div>
      </div>

      {/* Services/Searchings/Interest Accordion */}
      <div className="people_details_bx white_shadow_bx">
        {renderAccordionSection()}
      </div>

      {/* Documents Section */}
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>Docs</h4>
        </div>
        {companyDetails.uploads?.length > 0 ? (
          <ul className="uploaded-files d-flex flex-wrap gap-3 p-0 m-0 mt-3">
            {companyDetails.uploads.map((file, index) => {
              const fileObj = {
                ...file,
                path: url.SERVER_URL + file.path,
              };
              return (
                <li
                  className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3"
                  key={index}
                >
                  <span class="d-block material-icons-outlined text-primary">
                    description
                  </span>
                  <p
                    className="fw-medium text-truncate"
                    style={{ maxWidth: "200px" }}
                  >
                    {file.name}
                  </p>
                  <Link
                    to="#"
                    onClick={(e) => {
                      e.preventDefault();
                      fileDownloadHandler(fileObj);
                    }}
                    aria-label={`Download ${file.name}`}
                  >
                    <span className="d-block material-icons-outlined">
                      download
                    </span>
                  </Link>
                </li>
              );
            })}
          </ul>
        ) : (
          <p className="mt-3">No documents available</p>
        )}
      </div>
    </div>
  );
};

export default OverviewTab;
