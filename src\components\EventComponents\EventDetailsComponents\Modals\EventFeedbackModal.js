/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";
import Select from "react-select";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";
import { ecosystemSlug } from "Config/Config";

const EventFeedbackModal = ({
  surveyInviteId = null,
  setSurveyInviteId = () => {},
  surveyIdForFeedback = null,
  setSurveyIdForFeedback = () => {},
}) => {
  const starCount = [1, 2, 3, 4, 5];

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [isStartingSurvey, setIsStartingSurvey] = useState(false);
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);
  const [userData, setUserData] = useState({});

  // Wizard state management
  const [currentStep, setCurrentStep] = useState(0); // 0 = start screen, 1-5 = questions
  const [surveyQuestions, setSurveyQuestions] = useState([]);
  const [totalNoOfQuestions, setTotalNoOfQuestions] = useState(0);

  // Answer states for current question
  const [answerOfQuestion, setAnswerOfQuestion] = useState("");
  const [starRating, setstarRating] = useState("1");
  const [singleSelectAnswer, setSingleSelectAnswer] = useState(null);
  const [multiSelectAnswer, setMultiSelectAnswer] = useState(null);

  // Store all submitted answers - key: questionId, value: answer data
  const [submittedAnswers, setSubmittedAnswers] = useState({});

  // Error states
  const [submitError, setSubmitError] = useState("");
  const [answerError, setAnswerError] = useState(false);

  const getSurveyInviteDetails = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_SURVEY_INVITE_DETAILS +
        `/${surveyInviteId}`;

      const response = await getData(requestURL);
      setIsLoading(false);

      console.log("response in survey invite details------->", response);

      if (response.status) {
        setUserData(response.data.componentobject);
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // Get survey questions
  const getSurveyQuestions = async () => {
    try {
      setIsStartingSurvey(true);
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_SURVEY_INVITE_DETAILS +
        `/${surveyInviteId}`;

      const response = await getData(requestURL);

      console.log("response in questions------->", response);

      if (response.status) {
        if (response.data.questionlist.length > 0) {
          setSurveyQuestions(response.data.questionlist);
          setTotalNoOfQuestions(response.data.questionlist.length);

          // Check and assign existing answers for all questions
          response.data.questionlist.forEach((question) => {
            if (
              question.surveyanswer &&
              question.surveyanswer !== null &&
              question.surveyanswer !== undefined &&
              question.surveyanswer !== ""
            ) {
              assignAnswerHandler(question);

              // Store the existing answer in submittedAnswers
              const answerData = getAnswerDataFromQuestion(question);
              setSubmittedAnswers((prev) => ({
                ...prev,
                [question._id]: answerData,
              }));
            }
          });

          setCurrentStep(1); // Move to first question
        }
      }
    } catch (error) {
      console.log(error.message);
    } finally {
      setIsStartingSurvey(false);
    }
  };

  // Helper function to extract answer data from question based on type
  const getAnswerDataFromQuestion = (question) => {
    const answerData = {
      response: question.surveyanswer,
      starRating: "1",
      singleSelectAnswer: null,
      multiSelectAnswer: null,
    };

    switch (question.questiontypeslug) {
      case "RATING":
        answerData.starRating = question.surveyanswer;
        break;
      case "SINGLE SELECT":
        answerData.singleSelectAnswer = {
          label: question.surveyanswer,
          value: question.surveyanswer,
        };
        break;
      case "MULTI SELECT":
        answerData.multiSelectAnswer = question.surveyanswer
          .split(", ")
          .map((item) => ({
            label: item,
            value: item,
          }));
        break;
      default:
        // For TEXT and COMMENTS, response is already set
        break;
    }

    return answerData;
  };

  // Submit individual answer to API
  const submitAnswer = async (questionId) => {
    try {
      setIsSubmittingAnswer(true);
      setSubmitError("");

      const requestData = {
        ecosystemidslug: ecosystemSlug,
        surveyid: surveyIdForFeedback,
        surveyinviteid: surveyInviteId,
        questionid: questionId,
        response: answerOfQuestion,
      };

      const requestURL = url.API_BASE_URL + url.API_SAVE_RESPONSE_SURVEY;
      const response = await postData(requestURL, requestData);

      console.log("Answer submission response:", response);

      if (response.status) {
        // Store the submitted answer
        setSubmittedAnswers((prev) => ({
          ...prev,
          [questionId]: {
            response: answerOfQuestion,
            starRating: starRating,
            singleSelectAnswer: singleSelectAnswer,
            multiSelectAnswer: multiSelectAnswer,
          },
        }));
        return true;
      } else {
        setSubmitError(response.message || "Failed to submit answer");
        return false;
      }
    } catch (error) {
      console.log("Error submitting answer:", error);
      setSubmitError("An error occurred while submitting your answer");
      return false;
    } finally {
      setIsSubmittingAnswer(false);
    }
  };

  // Load previously submitted answer for current question
  const loadPreviousAnswer = (question) => {
    const previousAnswer = submittedAnswers[question._id];

    if (previousAnswer) {
      setAnswerOfQuestion(previousAnswer.response || "");
      setstarRating(previousAnswer.starRating || "1");
      setSingleSelectAnswer(previousAnswer.singleSelectAnswer || null);
      setMultiSelectAnswer(previousAnswer.multiSelectAnswer || null);
    } else {
      // Reset to default values for new question
      setAnswerOfQuestion("");
      setstarRating("1");
      setSingleSelectAnswer(null);
      setMultiSelectAnswer(null);
    }
  };

  // Validate current answer based on question type
  const validateCurrentAnswer = (question) => {
    if (!question) return false;

    switch (question.questiontypeslug) {
      case "TEXT":
      case "COMMENTS":
        return answerOfQuestion.trim() !== "";
      case "SINGLE SELECT":
        return singleSelectAnswer !== null;
      case "MULTI SELECT":
        return multiSelectAnswer !== null && multiSelectAnswer.length > 0;
      case "RATING":
        return starRating !== null && starRating !== "";
      default:
        return false;
    }
  };

  //select star rating
  const starRatingHandler = (value) => {
    setstarRating(value);
    setAnswerOfQuestion(value);
    setAnswerError(false);
    setSubmitError("");
  };

  const handleStartSurvey = () => {
    getSurveyQuestions();
  };

  const handleNext = async () => {
    const currentQuestionIndex = currentStep - 1;
    const currentQuestion = surveyQuestions[currentQuestionIndex];

    if (!currentQuestion) return;

    // Validate answer before submitting
    if (!validateCurrentAnswer(currentQuestion)) {
      setAnswerError(true);
      setSubmitError("Please provide an answer before proceeding");
      return;
    }

    // Submit answer to API
    const isSubmitted = await submitAnswer(currentQuestion._id);

    if (!isSubmitted) {
      // Don't proceed if submission failed
      return;
    }

    // Move to next step or complete survey
    if (currentStep < totalNoOfQuestions) {
      setCurrentStep((prev) => prev + 1);
      // Clear error states for next question
      setAnswerError(false);
      setSubmitError("");
    } else {
      // All questions completed - show thank you screen
      setCurrentStep(totalNoOfQuestions + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
      setSubmitError(""); // Clear any submit errors
      setAnswerError(false);
    }
  };

  const closeHandler = () => {
    // Reset all states
    setUserData({});
    setCurrentStep(0);
    setSurveyQuestions([]);
    setSubmittedAnswers({});
    setAnswerOfQuestion("");
    setstarRating("1");
    setSingleSelectAnswer(null);
    setMultiSelectAnswer(null);
    setSubmitError("");
    setAnswerError(false);
    setSurveyInviteId(null);
    setSurveyIdForFeedback(null);

    //hide login modal
    const surveyFeedbackModal = document.querySelector("#feedbackModal");
    const modal = bootstrap.Modal.getInstance(surveyFeedbackModal);
    modal.hide();
  };

  const calculateProgress = () => {
    if (currentStep === 0) return 0;
    if (currentStep > totalNoOfQuestions) return 100;
    return (currentStep / totalNoOfQuestions) * 100;
  };

  const getCurrentQuestion = () => {
    if (currentStep === 0 || surveyQuestions.length === 0) return null;
    return surveyQuestions[currentStep - 1];
  };

  useEffect(() => {
    if (surveyInviteId) {
      getSurveyInviteDetails();
    }
  }, [surveyInviteId]);

  // Load submitted answer when step changes
  useEffect(() => {
    if (currentStep > 0 && surveyQuestions.length > 0) {
      const currentQuestion = getCurrentQuestion();
      if (currentQuestion) {
        loadPreviousAnswer(currentQuestion);
      }
    }
  }, [currentStep, surveyQuestions]);

  const renderStartScreen = () => (
    <div className="d-flex flex-column align-items-center justify-content-center flex-fill text-center p-5">
      <div style={{ maxWidth: "350px" }}>
        <img
          src="/images/lobees-blue.svg"
          alt="Lobees"
          className="img-fluid mb-3"
        />
        <h3>
          Hello{" "}
          {userInfo ? (
            <span>
              {userInfo.name} {userInfo.surname}
            </span>
          ) : (
            <span>Guest</span>
          )}
        </h3>

        <p className="fs-md text-6a">Give feedback on</p>

        <h2 className="fw-semibold text-dark mb-3">
          {userData.name} {userData.surname}
        </h2>

        <p className="text-muted mb-4">
          This survey contains {totalNoOfQuestions || 5} questions and will take
          approximately 3-5 minutes to complete.
        </p>

        <button
          className="btn btn-primary mt-4"
          onClick={handleStartSurvey}
          disabled={isLoading || isStartingSurvey}
        >
          {isStartingSurvey ? (
            <>
              <span
                className="spinner-border spinner-border-sm me-2"
                role="status"
                aria-hidden="true"
              ></span>
              Starting...
            </>
          ) : isLoading ? (
            "Loading..."
          ) : (
            "Start Now"
          )}
        </button>
      </div>
    </div>
  );

  const renderQuestion = () => {
    const question = getCurrentQuestion();
    if (!question) return null;

    const isLastQuestion = currentStep === totalNoOfQuestions;

    return (
      <div className="d-flex flex-column justify-content-between flex-fill p-5">
        <div className="flex-fill d-flex flex-column justify-content-center">
          <div className="text-center mb-4">
            <div className="mb-3">
              <span className="badge bg-primary fs-6 px-3 py-2">
                Question {currentStep} of {totalNoOfQuestions}
              </span>
            </div>
            <h3 className="fw-semibold mb-4">{question.name}</h3>
          </div>

          <div
            className="mb-4"
            style={{ maxWidth: "500px", margin: "0 auto", width: "100%" }}
          >
            {question?.questiontypeslug ? (
              <div className="form-group">
                {/* Text type question */}
                {question?.questiontypeslug === "TEXT" ? (
                  <input
                    type="text"
                    className={`form-control ${
                      answerError ? "is-invalid" : ""
                    }`}
                    placeholder="Enter your answer"
                    value={answerOfQuestion}
                    onChange={(e) => {
                      setAnswerOfQuestion(e.target.value);
                      setAnswerError(false);
                      setSubmitError("");
                    }}
                    disabled={isSubmittingAnswer}
                  />
                ) : null}

                {/* Comments type question */}
                {question.questiontypeslug === "COMMENTS" ? (
                  <textarea
                    className={`form-control ${
                      answerError ? "is-invalid" : ""
                    }`}
                    placeholder="Type your comments here..."
                    value={answerOfQuestion}
                    onChange={(e) => {
                      setAnswerOfQuestion(e.target.value);
                      setAnswerError(false);
                      setSubmitError("");
                    }}
                    rows="4"
                    style={{ resize: "vertical", minHeight: "120px" }}
                    disabled={isSubmittingAnswer}
                  />
                ) : null}

                {/* Single select question */}
                {question.questiontypeslug === "SINGLE SELECT" ? (
                  <Select
                    placeholder="Select answer"
                    options={question.questionoptions}
                    value={singleSelectAnswer}
                    onChange={(val) => {
                      setSingleSelectAnswer(val);
                      setAnswerOfQuestion(val.value);
                      setAnswerError(false);
                      setSubmitError("");
                    }}
                    isDisabled={isSubmittingAnswer}
                    className={answerError ? "is-invalid" : ""}
                  />
                ) : null}

                {/* Multi select question */}
                {question.questiontypeslug === "MULTI SELECT" ? (
                  <Select
                    isMulti
                    placeholder="Select answer"
                    options={question.questionoptions}
                    value={multiSelectAnswer}
                    onChange={(val) => {
                      setMultiSelectAnswer(val);
                      const answerString = val
                        .map((item) => {
                          return item.value;
                        })
                        .join(", ");
                      setAnswerOfQuestion(answerString);
                      setAnswerError(false);
                      setSubmitError("");
                    }}
                    isDisabled={isSubmittingAnswer}
                    className={answerError ? "is-invalid" : ""}
                  />
                ) : null}

                {/* Rating question */}
                {question?.questiontypeslug === "RATING" ? (
                  <ul className="list_style_none rating_star d-flex align-items-center justify-content-center">
                    {starCount.map((star, index) => {
                      return (
                        <li
                          key={index}
                          className={
                            star.toString() <= starRating.toString()
                              ? "active"
                              : ""
                          }
                        >
                          <Link
                            to="#"
                            onClick={() => starRatingHandler(star.toString())}
                            style={{
                              pointerEvents: isSubmittingAnswer
                                ? "none"
                                : "auto",
                              opacity: isSubmittingAnswer ? 0.6 : 1,
                            }}
                          >
                            <span className="material-icons">star</span>
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                ) : null}

                {/* Error message */}
                {(submitError || answerError) && (
                  <div className="text-danger small mt-2">
                    {submitError || "Please provide an answer"}
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>

        <div className="d-flex justify-content-between align-items-center pt-3 border-top">
          <button
            type="button"
            className="btn btn-outline-secondary"
            onClick={handlePrevious}
            disabled={currentStep === 1 || isSubmittingAnswer}
          >
            Previous
          </button>

          <div className="text-muted small">
            {currentStep} of {totalNoOfQuestions}
          </div>

          <button
            type="button"
            className="btn btn-primary"
            onClick={handleNext}
            disabled={isSubmittingAnswer}
          >
            {isSubmittingAnswer ? (
              <>
                <span
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                  aria-hidden="true"
                ></span>
                Submitting...
              </>
            ) : (
              <>{isLastQuestion ? "Submit" : "Next"}</>
            )}
          </button>
        </div>
      </div>
    );
  };

  const renderThankYou = () => (
    <div className="d-flex flex-column align-items-center justify-content-center flex-fill text-center p-5">
      <div style={{ maxWidth: "350px" }}>
        <div className="mb-4">
          <div className="text-success" style={{ fontSize: "4rem" }}>
            ✓
          </div>
        </div>
        <h3 className="fw-semibold mb-3">Thank You!</h3>
        <p className="text-muted mb-4">
          Your feedback has been submitted successfully. Thank you for taking
          the time to complete this survey.
        </p>
        <button className="btn btn-primary" onClick={closeHandler}>
          Close
        </button>
      </div>
    </div>
  );

  //function for assing answer to input fields
  const assignAnswerHandler = (surveyQuestion) => {
    //answer for rating
    if (surveyQuestion?.questiontypeslug === "RATING") {
      surveyQuestion.surveyanswer &&
        surveyQuestion.surveyanswer !== "" &&
        setstarRating(surveyQuestion.surveyanswer);
    }

    //answer for text
    if (surveyQuestion?.questiontypeslug === "TEXT") {
      surveyQuestion.surveyanswer &&
        surveyQuestion.surveyanswer !== "" &&
        setAnswerOfQuestion(surveyQuestion.surveyanswer);
    }

    //answer for single select
    if (surveyQuestion?.questiontypeslug === "SINGLE SELECT") {
      if (surveyQuestion.surveyanswer && surveyQuestion.surveyanswer !== "") {
        const selectValue = {
          label: surveyQuestion.surveyanswer,
          value: surveyQuestion.surveyanswer,
        };
        setSingleSelectAnswer(selectValue);
        setAnswerOfQuestion(surveyQuestion.surveyanswer);
      }
    }

    //answer for multi select
    if (surveyQuestion?.questiontypeslug === "MULTI SELECT") {
      if (surveyQuestion.surveyanswer && surveyQuestion.surveyanswer !== "") {
        const selectValueArr = surveyQuestion.surveyanswer
          .split(", ")
          .map((item) => {
            return {
              label: item,
              value: item,
            };
          });
        setMultiSelectAnswer(selectValueArr);
        setAnswerOfQuestion(surveyQuestion.surveyanswer);
      }
    }

    //answer for comments
    if (surveyQuestion?.questiontypeslug === "COMMENTS") {
      surveyQuestion.surveyanswer &&
        surveyQuestion.surveyanswer !== "" &&
        setAnswerOfQuestion(surveyQuestion.surveyanswer);
    }
  };

  return (
    <div
      className="modal fade"
      id="feedbackModal"
      tabIndex="-1"
      aria-labelledby="feedbackModalLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
    >
      <div className="modal-dialog modal-xl modal-dialog-centered">
        <div className="modal-content border-0 overflow-hidden">
          <div className="modal-body p-0">
            <div className="d-flex flex-xl-row flex-column-reverse">
              {/* Left Panel - User Profile */}
              {isLoading ? (
                <div
                  className="col-xl-5 bg-light p-4 overflow-auto"
                  style={{ maxHeight: "600px" }}
                >
                  {/* Profile Section Skeleton */}
                  <div className="d-flex align-items-center gap-3 pb-4 border-bottom mb-4">
                    <div
                      className="rounded-circle bg-secondary placeholder col-3 flex-shrink-0"
                      style={{ width: "100px", height: "100px" }}
                    ></div>
                    <div className="flex-grow-1">
                      <h3 className="placeholder-glow">
                        <span className="placeholder col-6"></span>
                      </h3>
                      <h5 className="placeholder-glow">
                        <span className="placeholder col-4"></span>
                      </h5>
                      <ul className="d-flex align-items-center flex-wrap gap-2 mt-2 list-unstyled">
                        <li className="d-flex align-items-center gap-2 bg-white p-2 rounded-3">
                          <span className="placeholder col-2"></span>
                          <span className="placeholder col-5"></span>
                        </li>
                        <li className="d-flex align-items-center gap-2 bg-white p-2 rounded-3">
                          <span className="placeholder col-2"></span>
                          <span className="placeholder col-5"></span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* Skills Skeleton */}
                  <div className="mb-4">
                    <h4 className="placeholder-glow mb-3">
                      <span className="placeholder col-4"></span>
                    </h4>
                    <div className="d-flex gap-2 flex-wrap">
                      <span className="placeholder col-3 py-2 rounded-3"></span>
                      <span className="placeholder col-4 py-2 rounded-3"></span>
                      <span className="placeholder col-5 py-2 rounded-3"></span>
                    </div>
                  </div>

                  {/* Resume Summary Skeleton */}
                  <div className="mb-4">
                    <h4 className="placeholder-glow mb-3">
                      <span className="placeholder col-5"></span>
                    </h4>
                    <p className="placeholder-glow">
                      <span className="placeholder col-12"></span>
                      <span className="placeholder col-10"></span>
                      <span className="placeholder col-8"></span>
                    </p>
                  </div>
                </div>
              ) : (
                <div
                  className="col-xl-5 bg-light p-4 overflow-auto"
                  style={{ maxHeight: "600px" }}
                >
                  <div className="d-flex align-items-center gap-3 pb-4 border-bottom mb-4">
                    <div
                      className="avatar rounded-circle overflow-hidden flex-shrink-0"
                      style={{ width: "100px", height: "100px" }}
                    >
                      <img
                        src={
                          userData.photoimage
                            ? url.SERVER_URL + userData?.photoimage?.path
                            : "/images/default-user.png"
                        }
                        alt={`${userData.name} ${userData.surname}`}
                        className="w-100 h-100 object-cover object-center"
                      />
                    </div>
                    <div>
                      <h3 className="fw-semibold">
                        {userData.name} {userData.surname}
                      </h3>
                      <h5 className="fw-medium text-muted">
                        {userData.position}
                      </h5>
                      <ul className="d-flex align-items-center flex-wrap gap-2 mt-2 list-unstyled">
                        <li className="d-flex align-items-center gap-2 bg-white p-2 rounded-3">
                          <span className="material-icons-outlined text-primary fs-6">
                            email
                          </span>
                          <Link
                            to="#"
                            className="text-black text-decoration-none small"
                          >
                            {userData.email}
                          </Link>
                        </li>
                        <li className="d-flex align-items-center gap-2 bg-white p-2 rounded-3">
                          <span className="material-icons-outlined text-primary fs-6">
                            phone_in_talk
                          </span>
                          <Link
                            to="#"
                            className="text-black text-decoration-none small"
                          >
                            {userData.phone}
                          </Link>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="resume-summary mb-4">
                    <h4 className="fw-semibold mb-3">Details</h4>
                    <div
                      className="small text-muted"
                      dangerouslySetInnerHTML={{ __html: userData.about }}
                    />
                  </div>

                  <div className="skills mb-4">
                    <h4 className="fw-semibold mb-3">Skills</h4>
                    <ul className="list-inline d-flex gap-2 flex-wrap fw-medium mb-2">
                      {userData?.skills?.map((skill, index) => (
                        <li
                          key={index}
                          className="px-3 py-2 bg-white border rounded-4 small"
                        >
                          {skill.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Right Panel - Survey Content */}
              <div className="col-xl-7 d-flex flex-column">
                {/* Header with close button */}
                <div className="d-flex justify-content-end p-3">
                  <button
                    type="button"
                    className="btn-close"
                    aria-label="Close"
                    onClick={closeHandler}
                  ></button>
                </div>

                {/* Progress Bar */}
                <div
                  className="progress"
                  role="progressbar"
                  aria-label="Survey Progress"
                  aria-valuenow={calculateProgress()}
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style={{ height: "4px" }}
                >
                  <div
                    className="progress-bar bg-primary transition-all"
                    style={{
                      width: `${calculateProgress()}%`,
                      transition: "width 0.3s ease-in-out",
                    }}
                  ></div>
                </div>

                {/* Survey Content */}
                {currentStep === 0 && renderStartScreen()}
                {currentStep > 0 &&
                  currentStep <= totalNoOfQuestions &&
                  renderQuestion()}
                {currentStep > totalNoOfQuestions && renderThankYou()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventFeedbackModal;
