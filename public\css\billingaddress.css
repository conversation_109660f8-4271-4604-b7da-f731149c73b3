/* ------cart--page------- */
.pt-70 {
  padding-top: 70px;
}
.fs-sm {
  font-size: 14px;
}
.fs-md {
  font-size: 18px;
}
.fs-lg {
  font-size: 20px;
}
.fs-xl {
  font-size: 30px;
}
.text-black {
  color: #000;
}
.text-gray {
  color: #5a6471;
}
.text-green {
  color: #31a61e;
}
.text-ornge {
  color: #e8604d;
}
.fw-medium {
  font-weight: 500;
}
.fw-semibold {
  font-weight: 600;
}
.rounded-8 {
  border-radius: 8px;
}
.rounded-10 {
  border-radius: 10px;
}
.light-green-bg {
  background-color: #e2efe6;
}
.light-ornge-bg {
  background-color: #ffeedf;
}
.bg-white {
  background-color: #fff;
}
.bg-light {
  background-color: #d3e3fd !important;
}
.bg-extra-light {
  background-color: #f2f6fd !important;
}
.cart_leftpnl {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1),
    0px 0px 1px 0px rgba(0, 0, 0, 0.25);
}
.number .plus,
.number .minus {
  height: 34px;
  width: 34px;
  min-width: 34px;
  cursor: pointer;
}
.number .form-control {
  height: 34px;
  width: 63px;
  box-shadow: none;
  border-color: #d3e3fd;
}
.cart_item {
  border-bottom: 1px solid #d3e3fd;
}
.cart_item:last-child {
  border-bottom: 0;
}
.right_cart_info {
  padding-left: 40px;
  text-align: right;
}
.form-select {
  width: 115px;
}
.form-select:focus {
  box-shadow: none;
}
.cart_rightPnl {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1),
    0px 0px 1px 0px rgba(0, 0, 0, 0.25);
  border: 1px solid #99a1aa;
}
.oredr_row.pt-3 {
  border-top: 1px solid #d3e3fd;
}
/* ------cart--page------- */
.address_row {
  border-bottom: 1px solid #d3e3fd;
}
.billing_form .form-control,
.billing_form .form-select {
  height: 50px;
  border-color: transparent;
  box-shadow: none !important;
}
.billing_form .form-group label {
  font-size: 16px;
}
@media (max-width: 991px) {
  .cart_rightPnl {
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .right_cart_info {
    padding-left: 0;
    text-align: left;
  }
  .right_cart_info {
    margin-top: 20px;
  }
  .selectbox,
  .payment_info h3 {
    justify-content: flex-start !important;
  }
  .fs-xl {
    font-size: 22px;
  }
}
