/* eslint-disable */
import React, { useState, useEffect, useContext } from "react";
import { Link, useParams } from "react-router-dom";

//import context
import { GlobalProvider } from "context/GlobalContext";

/*import url and gateway methods */
import { getData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

//import images
import { assetImages } from "constants";
import ContactModal from "../Modals/ContactModal";
import GroupModal from "../Modals/GroupModal";
import CreateNewGroupModal from "../Modals/CreateNewGroupModal";
import AiSummaryModal from "../Modals/ChatAiSummaryModal";

import { playAudioTone } from "helper/Common/CommonHelper";
import ChatLeftPanelPlaceholder from "./Placeholder/ChatLeftPanelPlaceholder";
import ChatAiSummaryModal from "../Modals/ChatAiSummaryModal";
import ChatAiMedicalHelp from "../Modals/ChatAiMedicalHelpModal";
import SendEmailPopup from "components/common/Popup/SendEmailPopup";
import TaskModal from "../Modals/TaskModal";
import AskPayment from "components/common/Modals/AskPayment";

const ConversationLeftPanel = ({
  selectedConversationId = null,
  io,
  updateDefaultScreen,
  updateSelectedChat,
  updateConversationName,
  updateSelectedChatLogo,
  updateSelectedChatEmail,
  updatedSelectedChatRoom,
  updatedSelectedChatDetails,
  updatedGroupModeratorName,
  updatedGroupModeratorImage,
  updatedGroupMembers,
  updatedGroupFiles,
  updateGroupModeratorId,
  updateGroupMemberString,
  setchatcomponentSlug = () => {},
  setchatComponentTitle = () => {},
  setchatComponentId = () => {},
  socket,
  allContacts,
  setAllContacts,
}) => {
  // const socket = io(url.SERVER_URL);

  const {
    setreloadChatPanel,
    reloadConversationLeftPanel,
    setReloadConversationLeftPanel,
    afterLeaveGroupReload,
    setafterLeaveGroupReload,
    isUnreadActive,
    setisUnreadActive,
    reloadContactList,
    setReloadContactList,
    requireResponseChatIds,
    setRequireResponseChatIds,
  } = useContext(GlobalProvider);

  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const glowPlaceholderCount = [1, 2, 3, 4, 5, 6, 7];

  const { t } = useTranslation(); // translation marker

  const [contactChatList, setContactChatList] = useState([]);
  const [channelChatList, setChannelChatList] = useState([]);

  const [groupLoading, setGroupLoading] = useState(false);
  const [allGroups, setAllGroups] = useState([]);

  const [taskLoading, setTaskLoading] = useState(false);
  const [allTasks, setAllTasks] = useState([]);

  const [isChatPanelLoading, setIsChatPanelLoading] = useState(false);

  const [showChatPopup, setShowChatPopup] = useState(false);

  const [selectedChatParams, setselectedChatParams] = useState(null);

  const [selectedChatContactId, setselectedChatContactId] = useState(null); //state for remove require response and update require response
  const [selectedChatIndex, setSelectedChatIndex] = useState(null);
  const [isClosingResponse, setisClosingResponse] = useState(false);

  const [selectedGroupId, setselectedGroupId] = useState(null);

  const [newMessageNotification, setnewMessageNotification] = useState(false);

  const [unreadMessages, setunreadMessages] = useState([]);

  const [selectedRoomId, setSelectedRoomId] = useState(null);
  const [isGroupChat, setIsGroupChat] = useState(false); // turn true if click on a group

  // selected user name and image for display
  const [selectedUserName, setSelectedUserName] = useState("");
  const [selectedUserImage, setSelectedUserImage] = useState("");
  const [selectedUserEmail, setSelectedUserEmail] = useState("");

  const [emails, setEmails] = useState("");

  const [reloadChatList, setReloadChatList] = useState(false);

  const [showUnreadChats, setShowUnreadChats] = useState(false);
  const [showReadChats, setShowReadChats] = useState(false);
  const [showRequireResponseChats, setShowRequireResponseChats] =
    useState(false);

  const [closeResponseMessageIds, setCloseResponseMessageIds] = useState([]);
  const [closeResponseRoomId, setCloseResponseRoomId] = useState(null);
  const [closeResponseReciverList, setCloseResponseReciverList] = useState([]);

  //select contact chat
  const chatSelectionHandler = (
    conversationId, //mongo id of contact account
    conversationName, // contact name or group name
    conversationLogo, // contact image or group logo
    conversationEmail, // contact email . If gorup is empty string
    screen = "contact", //it can be contact or group
    conversationChatId, // contact mongo id or group mongo id. It will use for chat room id in socket
    conversationChatPhone,
    conversationChatAddress,
    conversationChatCompany,
    componentSlug = "",
    componentTitle = "",
    groupModeratorName = "",
    groupModeratorImage = "",
    groupMembers = [],
    groupFiles = [],
    groupModeratorId = null,
    groupMemberString = "",
    componentId = null
  ) => {
    document.body.classList.toggle("chatmsg_open");

    console.log("selected chat conversationChatId", conversationChatId);

    updateDefaultScreen(screen);
    updateSelectedChat(conversationId);
    updateConversationName(conversationName);
    updateSelectedChatLogo(conversationLogo);
    updateSelectedChatEmail(conversationEmail);
    updatedSelectedChatRoom(conversationChatId);
    updatedSelectedChatDetails({
      conversationChatPhone,
      conversationChatAddress,
      conversationChatCompany,
    });

    updatedGroupModeratorName(groupModeratorName);
    updatedGroupModeratorImage(groupModeratorImage);
    updatedGroupMembers(groupMembers);
    updatedGroupFiles(groupFiles);
    updateGroupModeratorId(groupModeratorId);
    updateGroupMemberString(groupMemberString);

    if (componentSlug !== "") {
      setchatcomponentSlug(componentSlug);
    } else {
      setchatcomponentSlug("");
    }

    if (componentTitle !== "") {
      setchatComponentTitle(componentTitle);
    } else {
      setchatComponentTitle("");
    }

    if (componentId !== null) {
      setchatComponentId(componentId);
    } else {
      setchatComponentId(null);
    }

    updateChatStatusHandler(conversationChatId, conversationId, screen);
  };

  //function for get all contacts for mapping in contact modal
  const getAllContactList = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL + url.API_GET_ALL_CONTACTS + `?token=${token}`;

      // console.log("url of contact list------>", requestUrl);

      const response = await getData(requestUrl);

      // console.log("response in contact list------>", response);

      if (response.status) {
        setAllContacts(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get all group records for mapping in group modal
  const getAllGroups = async () => {
    try {
      setGroupLoading(true);

      let requestUrl =
        url.API_BASE_URL + url.API_GET_GROUP_LIST_FOR_CHAT + `?token=${token}`;

      // console.log("url of group list---------->", requestUrl);

      const response = await getData(requestUrl);

      setGroupLoading(false);

      // console.log("response in group list------->", response);

      if (response.status) {
        setAllGroups(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get all tasks for mapping in task modal
  const getAllTasks = async () => {
    try {
      setTaskLoading(true);

      let requestUrl =
        url.API_BASE_URL + url.API_GET_TASK_LIST_FOR_CHAT + `?token=${token}`;

      // console.log("url of task list---------->", requestUrl);

      const response = await getData(requestUrl);

      setTaskLoading(false);

      // console.log("response in task list------->", response);

      if (response.status) {
        setAllTasks(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get all chats
  const getAllUserChats = async (isLoading = false) => {
    try {
      isLoading && setIsChatPanelLoading(true);

      let requestUrl =
        url.API_BASE_URL + url.API_GET_ALL_CHAT_LIST + `?token=${token}`;

      if (showUnreadChats) {
        requestUrl += `&showunreadmessages=${true}`;
      }

      if (showReadChats) {
        requestUrl += `&showreadmessages=${true}`;
      }

      if (showRequireResponseChats) {
        requestUrl += `&showrequireresponse=${true}`;
      }

      // console.log("url of chat list---------->", requestUrl);

      const response = await getData(requestUrl);

      // console.log(response);

      setIsChatPanelLoading(false);

      if (response.status) {
        setContactChatList(response.data.contactchats);
        setChannelChatList(response.data.channelchats);

        if (response.data.unreadchats.length > 0) {
          setisUnreadActive(true);
          setunreadMessages(response.data.unreadchats);
        } else {
          setisUnreadActive(false);
          setunreadMessages([]);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for remove require response
  const removeRequireResponseHandler = async (contactChat = {}) => {
    try {
      setisClosingResponse(true);

      setCloseResponseRoomId(contactChat._id);
      setCloseResponseReciverList([contactChat.userid]);

      let chatData = {
        chatpersonid: contactChat.userid,
        contactid: contactChat._id,
      };

      let requestURL =
        url.API_BASE_URL + url.API_REMOVE_REQUIRE_RESPONSE + `?token=${token}`;

      const response = await putData(requestURL, chatData);

      // console.log("response in remove require response", response);

      setisClosingResponse(false);

      if (response.status) {
        if (
          selectedChatContactId &&
          selectedChatContactId === contactChat._id
        ) {
          // if right panel is open this function will be called from right panel
          if (response.data.length > 0) {
            const requireResponseChatIdList = response.data.map((item) =>
              item._id.toString()
            );
            setCloseResponseMessageIds(requireResponseChatIdList);
            setRequireResponseChatIds(requireResponseChatIdList);
          }

          // updated contact chat list
          setContactChatList((prevChatList) =>
            prevChatList.map((contact) => {
              if (contact._id.toString() === contactChat._id.toString()) {
                return {
                  ...contact,
                  requireresponse: false,
                };
              }
              return contact;
            })
          );
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get module details
  const getModuleDetails = async (
    conversationType = "",
    roomId = null,
    componentSlug = "",
    componentTitle = "",
    componentId = null
  ) => {
    if (conversationType !== "" && roomId) {
      try {
        let requestUrl = url.API_BASE_URL;

        if (conversationType === "contact") {
          requestUrl += `${url.API_GET_CONTACT_DETAILS}/${roomId}?token=${token}`;

          const response = await getData(requestUrl);

          // console.log(response);

          if (response.status) {
            const data = response.data;

            setselectedChatParams({
              contactpersonid: data.contactpersonid,
              contactname: data.contactname,
              contactimage: data.contactimage,
              contactemail: data.contactemail,
              conversationType,
              id: data._id,
              contactphone: data.contactphone,
              address: data.contactcity + ", " + data.contactcountry,
              contactcompany: data.contactcompany,
              componentSlug,
              componentTitle,
              groupModeratorName: "",
              groupModeratorImage: "",
              groupMembers: [],
              groupFiles: [],
              groupModeratorId: null,
              groupMemberString: "",
              componentId,
            });
          }
        }

        if (conversationType === "task") {
          requestUrl += `${url.API_GET_TASK_DETAILS}/${roomId}?token=${token}`;

          const response = await getData(requestUrl);

          // console.log("task details", response);

          if (response.status) {
            const data = response.data;

            setselectedChatParams({
              contactpersonid: data._id,
              contactname: `${data.customid} : ${data.title}`,
              contactimage: "",
              contactemail: "",
              conversationType,
              id: data._id,
              contactphone: "",
              address: "",
              contactcompany: "",
              componentSlug,
              componentTitle,
              groupModeratorName: "",
              groupModeratorImage: "",
              groupMembers: [],
              groupFiles: [],
              groupModeratorId: data.moderator,
              groupMemberString: "",
              componentId,
            });
          }
        }

        if (conversationType === "group") {
          requestUrl += `${url.API_GET_GROUP_DETAILS}/${roomId}?token=${token}&modulefrom=chatleftpanel`;

          const response = await getData(requestUrl);

          console.log("group details", response);

          if (response.status) {
            const data = response.data;

            setselectedChatParams({
              contactpersonid: data.userid,
              contactname: data.contactname,
              contactimage: data.contactimage,
              contactemail: "",
              conversationType,
              id: data.userid,
              contactphone: "",
              address: "",
              contactcompany: "",
              componentSlug,
              componentTitle,
              groupModeratorName: data.moderatorname,
              groupModeratorImage: data.moderatorimage,
              groupMembers: data.groupmemberlist,
              groupFiles: data.uploads,
              groupModeratorId: data.moderatorid,
              groupMemberString: data.groupmemberstring,
              componentId,
            });
          }
        }
      } catch (error) {
        console.log(error.message);
      }
    }
  };

  //function for after create group modal close
  const afterGroupModalClose = (groupResponse = {}) => {
    // console.log(groupResponse);
    chatSelectionHandler(
      groupResponse.userid,
      groupResponse.contactname,
      groupResponse.contactimage,
      groupResponse.contactemail,
      "group",
      groupResponse.userid,
      groupResponse.contactphone,
      groupResponse.location,
      groupResponse.contactcompany,
      "",
      "",
      groupResponse.moderatorname,
      groupResponse.moderatorimage,
      groupResponse.groupmemberlist,
      groupResponse.uploads,
      groupResponse.moderatorid,
      groupResponse.groupmemberstring,
      null
    );
    getAllGroups();
  };

  //update chat status
  const updateChatStatusHandler = async (
    conversationChatId = null,
    conversationId = null,
    screen = ""
  ) => {
    if (conversationChatId && conversationId) {
      setisUnreadActive(false);
      document.title = "Conversation"; // Reset to default title
      try {
        let chatStatusData = {
          group: null,
          contactid: null,
          personid: null,
          taskid: null,
        };

        if (conversationChatId.toString() === conversationId.toString()) {
          if (screen === "group") {
            chatStatusData.group = conversationId;
          }

          if (screen === "task") {
            chatStatusData.taskid = conversationId;
          }
        } else {
          chatStatusData.contactid = conversationChatId;
          chatStatusData.personid = conversationId;
        }

        // console.log(chatStatusData);

        let requestURL =
          url.API_BASE_URL + url.API_UPDATE_TRACKERS + `?token=${token}`;

        const response = await putData(requestURL, chatStatusData);

        // console.log("response", response);

        if (response.status) {
          getAllUserChats();
        }
      } catch (error) {
        console.log(error.message);
      }
    }
  };

  // generate AI summary
  const generateAISummary = async (roomid, isgroup = false) => {
    // show the modal ChatAiSummarModal
    setSelectedRoomId(roomid);
    setIsGroupChat(isgroup);

    //show chataisummary modal
    let myModal = new bootstrap.Modal(document.getElementById("chataisummary"));
    myModal.show();
  };


  // generate AI medical help
  const generateAIMedicalHelp = async (roomid, isgroup = false) => {
    // show the modal ChatAiMedicalHelpModal
    setSelectedRoomId(roomid);
    setIsGroupChat(isgroup);

    //show chataimedicalhelp modal
    let myModal = new bootstrap.Modal(document.getElementById("chataimedicalhelp"));
    myModal.show();
  };

  // request for payment
  const requestPaymentHandler = async (contactId) => {
    // show the modal AskPayment
    setSelectedRoomId(contactId);
    //show ask payment modal
    let myModal = new bootstrap.Modal(
      document.getElementById("askPaymentModal")
    );
    myModal.show();
  };

  //reset all states
  const resetAll = () => {
    setShowUnreadChats(false);
    setShowReadChats(false);
    setShowRequireResponseChats(false);

    setselectedChatContactId(null);
    setchatcomponentSlug("");
    setchatComponentTitle("");
    setchatComponentId(null);

    setReloadChatList(true);
  };

  useEffect(() => {
    if (reloadChatList) {
      getAllUserChats(true);
      setReloadChatList(false);
    }
  }, [reloadChatList]);

  useEffect(() => {
    if (selectedChatParams) {
      chatSelectionHandler(
        selectedChatParams.contactpersonid,
        selectedChatParams.contactname,
        selectedChatParams.contactimage,
        selectedChatParams.contactemail,
        selectedChatParams.conversationType,
        selectedChatParams.id,
        selectedChatParams.contactphone,
        selectedChatParams.address,
        selectedChatParams.contactcompany,
        selectedChatParams.componentSlug,
        selectedChatParams.componentTitle,
        selectedChatParams.groupModeratorName,
        selectedChatParams.groupModeratorImage,
        selectedChatParams.groupMembers,
        selectedChatParams.groupFiles,
        selectedChatParams.groupModeratorId,
        selectedChatParams.groupMemberString,
        selectedChatParams.componentId
      );
      setselectedChatParams(null);
    }
  }, [selectedChatParams]);

  useEffect(() => {
    setselectedChatContactId(null);
    setchatcomponentSlug("");
    setchatComponentTitle("");
    setchatComponentId(null);
    getAllContactList();
    getAllGroups();
    getAllTasks();

    getAllUserChats(true);

    if (
      params.conversationtype ||
      params.roomid ||
      params.componentslug ||
      params.componenttitle ||
      params.componentid
    ) {
      if (
        params.conversationtype === "contact" ||
        params.conversationtype === "task" ||
        params.conversationtype === "group"
      ) {
        getModuleDetails(
          params.conversationtype,
          params.roomid,
          params.componentslug,
          params.componenttitle,
          params.componentid
        );
      }

      if (params.conversationtype === "ai") {
        chatSelectionHandler(
          null,
          "",
          "",
          "",
          "ai",
          null,
          "",
          "",
          "",
          "",
          "",
          "",
          "",
          [],
          [],
          null,
          "",
          null
        );
      }
    }
  }, []);

  useEffect(() => {
    if (reloadConversationLeftPanel) {
      getAllUserChats();
      setReloadConversationLeftPanel(false);
    }
  }, [reloadConversationLeftPanel]);

  useEffect(() => {
    if (afterLeaveGroupReload) {
      setselectedChatContactId(null);

      document.body.classList.toggle("chatmsg_open");

      let chatDetailsObj = {
        conversationChatPhone: "",
        conversationChatAddress: "",
        conversationChatCompany: "",
      };

      updateDefaultScreen("welcome");
      updateSelectedChat(null);
      updateConversationName("");
      updateSelectedChatLogo("");
      updateSelectedChatEmail("");
      updatedSelectedChatRoom("");
      updatedSelectedChatDetails(chatDetailsObj);

      updatedGroupModeratorName("");
      updatedGroupModeratorImage("");
      updatedGroupMembers([]);
      updatedGroupFiles([]);

      setchatcomponentSlug("");
      setchatComponentTitle("");
      setchatComponentId(null);

      getAllUserChats();
      setafterLeaveGroupReload(false);
    }
  }, [afterLeaveGroupReload]);

  useEffect(() => {
    socket.on("newmessagereload", (data = []) => {
      if (data) {
        if (data.includes(userInfo._id.toString())) {
          setTimeout(() => {
            getAllUserChats(false);
            if (selectedConversationId) {
              playAudioTone();
            }
          }, 500);
        }
      }
    });

    socket.on("chatleftpanelreload", (data = []) => {
      if (data) {
        if (data.includes(userInfo._id.toString())) {
          setTimeout(() => {
            getAllUserChats(false);
          }, 500);
        }
      }
    });

    socket.on("seentickreload", (data) => {
      if (data) {
        if (data.includes(userInfo._id.toString())) {
          setTimeout(() => {
            getAllUserChats(false);
          }, 500);
        }
      }
    });

    return () => {
      socket.off("chatlistreload"); // Remove the listener
      socket.disconnect(); // Disconnect the socket
    };
  }, [socket]);

  useEffect(() => {
    let intervalId;
    let index = 0;

    const updateTitle = () => {
      if (index < unreadMessages.length) {
        const contactName = unreadMessages[index].contactname;

        document.title = `${contactName} sent you message`;

        index++;
      } else {
        index = 0;
        // clearInterval(intervalId);
        document.title = "Conversation"; // Reset to default title
      }
    };

    if (isUnreadActive && unreadMessages.length > 0) {
      intervalId = setInterval(updateTitle, 1000);
    }

    return () => {
      clearInterval(intervalId); // Cleanup on component unmount
    };
  }, [unreadMessages, isUnreadActive]);

  useEffect(() => {
    if (reloadContactList) {
      getAllContactList();
      setReloadContactList(false);
    }
  }, [reloadContactList]);

  useEffect(() => {
    if (showUnreadChats || showReadChats || showRequireResponseChats) {
      getAllUserChats();
    }
  }, [showUnreadChats, showReadChats, showRequireResponseChats]);

  return (
    <div className="chat_lftpnl">
      <div className="contact_header">
        {/* <div className="contact_hdng_row d-flex align-items-center justify-content-between">
          <div className="post_hdng">
            <h4>Contacts</h4>
          </div>
          <div className="contacts_srch_right">
            <ul className="list_stye_none d-flex align-items-center gap-2">
              <li>
                <Link to="#">
                  <i className="material-icons-outlined">search</i>
                </Link>
              </li>
            </ul>
          </div>
        </div> */}
      </div>

      <div className="contacts_pnl">
        <div className="chat_usrPnl">
          <div className="chat_innrScroll">
            {/* <!-- ========== Start AI chat Block Section ========== --> */}
            <div className="ai_assistant">
              <div className="chat_hdng">
                <span>AI Assistant</span>
              </div>
              <div className="chat_usrRow send_msg_row active_user">
                <Link
                  to="#"
                  onClick={() => {
                    chatSelectionHandler(
                      null,
                      "",
                      "",
                      "",
                      "ai",
                      null,
                      "",
                      "",
                      "",
                      "",
                      "",
                      "",
                      "",
                      [],
                      [],
                      null,
                      "",
                      null
                    );
                  }}
                >
                  <div className="usr_lftPnl">
                    <div className="chatting_user">
                      <img src={assetImages.aiLogoSvg} alt="" />
                    </div>
                    <div className="user_name">
                      <h3>LobeesAI</h3>
                      {/* <p>I’m looking forward</p> */}
                    </div>
                  </div>
                  <div className="msg_statusRight">
                    {/* <span className="msg_time">now</span> */}
                    {/* <span className="pending_msg">5</span> */}
                  </div>
                </Link>
              </div>
            </div>
            {/* <!-- ========== End AI chat Block Section ========== --> */}

            {/* <!-- ========== Start contact and group chat block Section ========== --> */}
            <div className="left_msgbody">
              <div className="d-flex align-items-center justify-content-end px-3">
                <Link to="#" className="text-gray" onClick={resetAll}>
                  <i className="material-icons-outlined fs-lg">refresh</i>
                </Link>

                {/* <Link to="#" className="text-gray" data-bs-toggle="dropdown">
                  <i className="material-icons-outlined fs-lg">more_vert</i>
                </Link> */}

                <div className="option_more dropdown">
                  <Link
                    to="#"
                    className="p-0 more_dots"
                    data-bs-toggle="dropdown"
                  >
                    <i className="material-icons-outlined">more_vert</i>
                  </Link>
                  <ul className="list_stye_none dropdown-menu">
                    <li
                      className="px-3 py-2"
                      style={{ cursor: "pointer" }}
                      onClick={() => setShowUnreadChats(true)}
                    >
                      <Link to="#" className="">
                        Show Unread Chats
                      </Link>
                    </li>
                    <li
                      className="px-3 py-2"
                      style={{ cursor: "pointer" }}
                      onClick={() => setShowReadChats(true)}
                    >
                      <Link to="#" className="">
                        Show Read Chats
                      </Link>
                    </li>
                    <li
                      className="px-3 py-2"
                      style={{ cursor: "pointer" }}
                      onClick={() => setShowRequireResponseChats(true)}
                    >
                      <Link to="#">Require Response Chats</Link>
                    </li>
                    {/* <li className="px-3 py-2">
                      <Link to="#" className="">
                        Module
                      </Link>
                    </li> */}
                  </ul>
                </div>
              </div>

              <div className="accordion" id="accordionPanelsStayOpenExample">
                {/* contact chats  */}
                <div className="accordion-item border-0">
                  <h2 className="accordion-header">
                    <button
                      className="accordion-button text-dark fw-medium shadow-none"
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#panelsStayOpen-collapseOne"
                      aria-expanded="true"
                      aria-controls="panelsStayOpen-collapseOne"
                    >
                      Contacts
                    </button>
                  </h2>
                  <div
                    id="panelsStayOpen-collapseOne"
                    className="accordion-collapse collapse show"
                  >
                    <div className="accordion-body p-0">
                      {isChatPanelLoading
                        ? glowPlaceholderCount.map((count, index) => {
                            return <ChatLeftPanelPlaceholder key={index} />;
                          })
                        : contactChatList.map((chat, index) => {
                            return (
                              <div
                                className="chat_usrRow send_msg_row"
                                key={index}
                              >
                                <div className="d-flex align-items-center justify-content-between py-2 px-3 chat_user_item">
                                  <div
                                    className="usr_lftPnl"
                                    style={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setselectedChatContactId(chat._id);
                                      chatSelectionHandler(
                                        chat.userid,
                                        chat.contactname,
                                        chat.contactimage,
                                        chat.contactemail,
                                        "contact",
                                        chat._id,
                                        chat.contactphone,
                                        chat.location,
                                        chat.contactcompany,
                                        "",
                                        "",
                                        chat.moderatorname,
                                        chat.moderatorimage,
                                        chat.groupmemberlist,
                                        chat.uploads,
                                        chat.moderatorid,
                                        chat.groupmemberstring,
                                        null
                                      );
                                    }}
                                  >
                                    {/* image  */}
                                    <div className="chatting_user">
                                      <img
                                        height="40"
                                        className="rounded-circle"
                                        src={
                                          chat.contactimage === ""
                                            ? assetImages.defaultUser
                                            : url.SERVER_URL + chat.contactimage
                                        }
                                        alt={chat.contactname}
                                        //id={chat.userid}
                                      />
                                    </div>

                                    {/* name and message  */}
                                    <div className="user_name">
                                      <h3
                                        className={
                                          chat.lastmessagesender.toString() ===
                                          userInfo._id.toString()
                                            ? ""
                                            : chat.islastemessageseen
                                            ? ""
                                            : "fw-bold"
                                        }
                                      >
                                        {chat.contactname}

                                        {chat.requireresponse ? (
                                          <i className="ms-2 material-icons-outlined text-danger fs-md">
                                            forum
                                          </i>
                                        ) : null}
                                      </h3>
                                      <p>
                                        {chat.lastmessagesender.toString() ==
                                        userInfo._id.toString() ? (
                                          <span>You : </span>
                                        ) : null}
                                        {chat.lastmessagetext == "" ? null : (
                                          <span
                                            className={
                                              chat.lastmessagesender.toString() ===
                                              userInfo._id.toString()
                                                ? ""
                                                : chat.islastemessageseen
                                                ? ""
                                                : "fw-bold text-success"
                                            }
                                          >
                                            {chat.lastmessagetext.slice(0, 25)}
                                          </span>
                                        )}{" "}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="chat_option_right d-flex align-items-center gap-2">
                                    <div className="chat_option_right d-flex align-items-center gap-2">
                                      <div className="msg_statusRight">
                                        <span className="msg_time">
                                          {chat.lastmessagetimestring}
                                        </span>

                                        {chat.lastmessagesender.toString() ==
                                        userInfo._id.toString() ? (
                                          <span className="msg_status">
                                            <i
                                              className={`material-icons-round ${
                                                chat.moduleslug ===
                                                "MOD_CONTACT"
                                                  ? chat.tracker === "2"
                                                    ? "text-primary"
                                                    : ""
                                                  : ""
                                              } `}
                                            >
                                              done_all
                                            </i>
                                          </span>
                                        ) : chat.unreadmessagecount ===
                                          0 ? null : (
                                          <p className="pending_msg bg-primary text-white">
                                            {chat.unreadmessagecount}
                                          </p>
                                        )}
                                      </div>
                                      <div className="option_more dropdown">
                                        <Link
                                          to="#"
                                          className="p-0 more_dots"
                                          data-bs-toggle="dropdown"
                                        >
                                          <i className="material-icons-outlined">
                                            more_vert
                                          </i>
                                        </Link>
                                        <ul className="list_stye_none dropdown-menu">
                                          {chat.moduleslug === "MOD_CONTACT" ? (
                                            <li>
                                              {selectedChatIndex === index &&
                                              isClosingResponse ? (
                                                <div
                                                  className="mx-2 spinner-border spinner-border-sm"
                                                  role="status"
                                                >
                                                  <span className="visually-hidden">
                                                    Loading...
                                                  </span>
                                                </div>
                                              ) : (
                                                <Link
                                                  to="#"
                                                  className=""
                                                  onClick={() => {
                                                    setSelectedChatIndex(index);
                                                    removeRequireResponseHandler(
                                                      chat
                                                    );
                                                  }}
                                                >
                                                  Close Response
                                                </Link>
                                              )}
                                            </li>
                                          ) : null}

                                          <li className="fw-small">
                                            <Link
                                              to="#"
                                              onClick={() => {
                                                chat.moduleslug ===
                                                "MOD_CONTACT"
                                                  ? generateAISummary(
                                                      chat.userid,
                                                      false
                                                    )
                                                  : generateAISummary(
                                                      chat.userid,
                                                      true
                                                    );
                                              }}
                                            >
                                              Ai Summary
                                            </Link>
                                          </li>

                                          <li className="fw-small">
                                            <Link
                                              to="#"
                                              onClick={() => {
                                                chat.moduleslug ===
                                                "MOD_CONTACT"
                                                  ? generateAIMedicalHelp(
                                                      chat.userid,
                                                      false
                                                    )
                                                  : generateAIMedicalHelp(
                                                      chat.userid,
                                                      true
                                                    );
                                              }}
                                            >
                                              Medical Help
                                            </Link>
                                          </li>

                                          <li>
                                            <Link
                                              to="#"
                                              className=""
                                              onClick={() => {
                                                if (
                                                  chat.moduleslug ===
                                                    "MOD_GROUP" ||
                                                  chat.moduleslug === "MOD_TASK"
                                                ) {
                                                  if (
                                                    chat.groupmemberlist &&
                                                    chat.groupmemberlist
                                                      .length > 0
                                                  ) {
                                                    const extractedEmails =
                                                      chat.groupmemberlist
                                                        .map(
                                                          (member) =>
                                                            member.groupmemberemail
                                                        )
                                                        .join(",");

                                                    setEmails(extractedEmails);
                                                  }
                                                } else {
                                                  setEmails(chat.contactemail);
                                                }
                                                document.body.classList.toggle(
                                                  "emailpopup_open"
                                                );
                                              }}
                                            >
                                              Send Mail
                                            </Link>
                                          </li>
                                          <li>
                                            <Link
                                              to="#"
                                              onClick={() => {
                                                setSelectedUserName(
                                                  chat.contactname
                                                );
                                                setSelectedUserImage(
                                                  chat.contactimage
                                                );
                                                setSelectedUserEmail(
                                                  chat.contactemail
                                                );
                                                requestPaymentHandler(
                                                  chat.userid
                                                );
                                              }}
                                            >
                                              Ask Payment
                                            </Link>
                                          </li>
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                    </div>
                  </div>
                </div>

                {/* group and task chats  */}
                <div className="accordion-item border-0">
                  <h2 className="accordion-header">
                    <button
                      className="accordion-button text-dark fw-medium shadow-none"
                      type="button"
                      data-bs-toggle="collapse"
                      data-bs-target="#panelsStayOpen-collapseTwo"
                      aria-expanded="true"
                      aria-controls="panelsStayOpen-collapseTwo"
                    >
                      Channels
                    </button>
                  </h2>
                  <div
                    id="panelsStayOpen-collapseTwo"
                    className="accordion-collapse collapse show"
                  >
                    <div className="accordion-body p-0">
                      {isChatPanelLoading
                        ? glowPlaceholderCount.map((count, index) => {
                            return <ChatLeftPanelPlaceholder key={index} />;
                          })
                        : channelChatList.map((chat, index) => {
                            return (
                              <div
                                className="chat_usrRow group_msg_row"
                                key={index}
                              >
                                <div className="d-flex align-items-center justify-content-between py-2 px-3 chat_user_item">
                                  <div
                                    className="usr_lftPnl"
                                    style={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      setselectedChatContactId(chat._id);
                                      chatSelectionHandler(
                                        chat.userid,
                                        chat.contactname,
                                        chat.contactimage,
                                        chat.contactemail,
                                        chat.moduleslug === "MOD_GROUP"
                                          ? "group"
                                          : "task",
                                        chat._id,
                                        chat.contactphone,
                                        chat.location,
                                        chat.contactcompany,
                                        "",
                                        "",
                                        chat.moderatorname,
                                        chat.moderatorimage,
                                        chat.groupmemberlist,
                                        chat.uploads,
                                        chat.moderatorid,
                                        chat.groupmemberstring,
                                        null
                                      );
                                    }}
                                  >
                                    {/* image  */}
                                    <div className="chatting_user">
                                      {chat.contactimage === "" ? (
                                        <>
                                          {" "}
                                          <figure className="group_usr">
                                            <img
                                              src={
                                                chat.moderatorimage === ""
                                                  ? assetImages.defaultUser
                                                  : url.SERVER_URL +
                                                    chat.moderatorimage
                                              }
                                              alt=""
                                            />
                                          </figure>
                                          {chat.groupmemberlist
                                            .slice(0, 2)
                                            .map((member, index2) => {
                                              return (
                                                <figure
                                                  className="group_usr"
                                                  key={index2}
                                                >
                                                  <img
                                                    src={
                                                      member.groupmemberimage ===
                                                      ""
                                                        ? assetImages.defaultUser
                                                        : url.SERVER_URL +
                                                          member.groupmemberimage
                                                    }
                                                    alt=""
                                                  />
                                                </figure>
                                              );
                                            })}
                                          {chat.groupmemberlist.length > 2 ? (
                                            <span className="group_usr plus_usr">
                                              {chat.groupmemberlist.length - 2}{" "}
                                              <em>+</em>
                                            </span>
                                          ) : null}
                                        </>
                                      ) : (
                                        <img
                                          height="40"
                                          className="rounded-circle"
                                          src={
                                            chat.contactimage === ""
                                              ? assetImages.defaultUser
                                              : url.SERVER_URL +
                                                chat.contactimage
                                          }
                                          alt={chat.contactname}
                                        />
                                      )}
                                    </div>

                                    {/* name and message  */}
                                    <div className="user_name">
                                      <h3
                                        className={
                                          chat.lastmessagesender.toString() ===
                                          userInfo._id.toString()
                                            ? ""
                                            : chat.islastemessageseen
                                            ? ""
                                            : "fw-bold"
                                        }
                                      >
                                        {chat.contactname}
                                      </h3>
                                      <p>
                                        {chat.lastmessagesender.toString() ==
                                        userInfo._id.toString() ? (
                                          <span>You : </span>
                                        ) : null}
                                        {chat.lastmessagetext == "" ? null : (
                                          <span
                                            className={
                                              chat.lastmessagesender.toString() ===
                                              userInfo._id.toString()
                                                ? ""
                                                : chat.islastemessageseen
                                                ? ""
                                                : "fw-bold text-success"
                                            }
                                          >
                                            {chat.lastmessagetext.slice(0, 25)}
                                          </span>
                                        )}{" "}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="chat_option_right d-flex align-items-center gap-2">
                                    <div className="chat_option_right d-flex align-items-center gap-2">
                                      <div className="msg_statusRight">
                                        <span className="msg_time">
                                          {chat.lastmessagetimestring}
                                        </span>

                                        {chat.lastmessagesender.toString() ==
                                        userInfo._id.toString() ? (
                                          <span className="msg_status">
                                            <i
                                              className={`material-icons-round ${
                                                chat.moduleslug ===
                                                "MOD_CONTACT"
                                                  ? chat.tracker === "2"
                                                    ? "text-primary"
                                                    : ""
                                                  : ""
                                              } `}
                                            >
                                              done_all
                                            </i>
                                          </span>
                                        ) : chat.unreadmessagecount ===
                                          0 ? null : (
                                          <p className="pending_msg bg-primary text-white">
                                            {chat.unreadmessagecount}
                                          </p>
                                        )}
                                      </div>
                                      <div className="option_more dropdown">
                                        <Link
                                          to="#"
                                          className="p-0 more_dots"
                                          data-bs-toggle="dropdown"
                                        >
                                          <i className="material-icons-outlined">
                                            more_vert
                                          </i>
                                        </Link>
                                        <ul className="list_stye_none dropdown-menu">
                                          <li className="fw-small">
                                            <Link
                                              to="#"
                                              onClick={() => {
                                                generateAISummary(
                                                  chat.userid,
                                                  true
                                                );
                                              }}
                                            >
                                              Ai Summary
                                            </Link>
                                          </li>
                                          <li>
                                            <Link
                                              to="#"
                                              className=""
                                              onClick={() => {
                                                if (
                                                  chat.moduleslug ===
                                                    "MOD_GROUP" ||
                                                  chat.moduleslug === "MOD_TASK"
                                                ) {
                                                  if (
                                                    chat.groupmemberlist &&
                                                    chat.groupmemberlist
                                                      .length > 0
                                                  ) {
                                                    const extractedEmails =
                                                      chat.groupmemberlist
                                                        .map(
                                                          (member) =>
                                                            member.groupmemberemail
                                                        )
                                                        .join(",");

                                                    setEmails(extractedEmails);
                                                  }
                                                } else {
                                                  setEmails(chat.contactemail);
                                                }
                                                document.body.classList.toggle(
                                                  "emailpopup_open"
                                                );
                                              }}
                                            >
                                              Send Mail
                                            </Link>
                                          </li>
                                        </ul>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* <!-- ========== End contact and group chat block Section ========== --> */}
          </div>

          <div
            className={
              showChatPopup ? "chat_cmntBx show_chatpopup" : "chat_cmntBx"
            }
          >
            <Link
              to="#"
              className="chat_btn"
              onClick={() => {
                setShowChatPopup(!showChatPopup);
              }}
            >
              <span className="material-icons-round">add_comment</span>
            </Link>

            <div className="lft_cht_popup">
              <ul>
                {/* <!-- ========== Start new chat Section ========== --> */}
                <li>
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#start_newchat"
                    onClick={() => {
                      setShowChatPopup(!showChatPopup);
                    }}
                  >
                    <i className="material-icons-round">person</i>
                    <span>Add Contact</span>
                  </Link>
                </li>
                {/* <!-- ========== End new chat Section ========== --> */}

                {/* <!-- ========== Start new group chat Section ========== --> */}
                <li>
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#group_modal"
                    onClick={() => {
                      setShowChatPopup(!showChatPopup);
                    }}
                  >
                    <i className="material-icons-round">people</i>
                    <span>Add Group</span>
                  </Link>
                </li>
                {/* <!-- ========== End new group chat Section ========== --> */}

                <li>
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#task_modal"
                    onClick={() => {
                      setShowChatPopup(!showChatPopup);
                    }}
                  >
                    <i className="material-icons-round">people</i>
                    <span>Add Task</span>
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <ContactModal
        myContacts={allContacts}
        chatSelectionHandler={chatSelectionHandler}
      />

      <GroupModal
        myGroups={allGroups}
        chatSelectionHandler={chatSelectionHandler}
      />

      <TaskModal
        myTasks={allTasks}
        chatSelectionHandler={chatSelectionHandler}
        taskLoading={taskLoading}
        glowPlaceholderCount={glowPlaceholderCount}
      />

      <CreateNewGroupModal
        myContacts={allContacts}
        afterModalClose={afterGroupModalClose}
      />

      <ChatAiSummaryModal roomId={selectedRoomId} isGroupChat={isGroupChat} />

      <ChatAiMedicalHelp roomId={selectedRoomId} isGroupChat={isGroupChat} />

      <SendEmailPopup emails={emails} setEmails={setEmails} />

      <AskPayment
        userid={selectedRoomId}
        userName={selectedUserName}
        userImage={selectedUserImage}
        userEmail={selectedUserEmail}
      />
    </div>
  );
};

export default ConversationLeftPanel;
