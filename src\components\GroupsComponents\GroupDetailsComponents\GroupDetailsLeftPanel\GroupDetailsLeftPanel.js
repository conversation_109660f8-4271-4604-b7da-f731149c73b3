/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams, useHistory, useLocation } from "react-router-dom";
// import use translation
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

//import images
import { assetImages } from "constants";
import { getDateFormatInSpanish, todayValue } from "helper/Common/CommonHelper";
import { generateRandomString } from "utils/GetRandomString";
import LeftPanlePlaceholder from "components/common/Placeholders/LeftPanlePlaceholder";

const GroupDetailsLeftPanel = ({
  isJoined = false,
  setisJoined = () => {},
  setIsLoading = () => {},
  setCopyMessage = () => {},
  setisApproved = () => {},
  setIsModerator = () => {},
  refreshGroup = false,
  setRefreshGroup = () => {},
}) => {
  const fullURL = window.location.href;

  const parsedURL = new URL(fullURL);
  const searchParams = parsedURL.searchParams;
  const secretParam = searchParams.get("secret");
  const emailParams = searchParams.get("memberemail");
  const memberShipIdParams = searchParams.get("membershipid");

  const history = useHistory();
  const location = useLocation();

  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const { t } = useTranslation(); // translation marker

  const memberLimit = 5; //for displaying the limit number of members

  const [isDetailsLoading, setIsDetailsLoading] = useState(false);
  const [groupDetails, setGroupDetails] = useState({});
  const [isJoining, setIsJoining] = useState(false);

  //share function
  const shareHandler = async () => {
    const baseURL = `${parsedURL.protocol}//${parsedURL.host}`;

    const secretString = generateRandomString(4); // Change 10 to the desired length

    try {
      let groupData = {
        secret: secretString,
      };
      let requestUrl =
        url.API_BASE_URL +
        url.API_UPDATE_GROUP +
        `/${params.id}` +
        `?token=${token}`;

      const response = await putData(requestUrl, groupData);

      if (response.status) {
        const redirectUrl = baseURL + `?ref=${fullURL}&secret=${secretString}`;

        const bootstrapModal = new bootstrap.Modal(
          document.getElementById("share_mdl")
        );
        bootstrapModal.show();

        setCopyMessage(redirectUrl);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get group details
  const getgroupDetails = async () => {
    try {
      setIsDetailsLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_GROUP_DETAILS +
        `/${params.id}?token=${token}&modulefrom=frontendleftpanel`;

      console.log("url of group details------>", requestUrl);

      const response = await getData(requestUrl);

      console.log("response of group details------>", response);

      setIsDetailsLoading(false);
      if (response.status) {
        setGroupDetails(response.data);
        setisJoined(response.data?.isjoined);
        setisApproved(response.data?.isapproved);

        if (
          userInfo?.role?.slug === "ADMIN" ||
          userInfo?.role?.slug === "SUPER_ADMIN" ||
          userInfo._id.toString() == response.data?.moderator._id?.toString()
        ) {
          setIsModerator(true);
        } else {
          setIsModerator(false);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for join group
  const joinGroupHandler = async () => {
    try {
      setIsJoining(true);
      let groupData = {
        group: params.id,
        useremail: userInfo.email,
        secret: secretParam ? secretParam : groupDetails.secret,
      };

      console.log("Join group Data------>", groupData);

      let requestURL =
        url.API_BASE_URL + url.API_JOIN_GROUP + `?token=${token}`;

      const response = await postData(requestURL, groupData);

      setIsJoining(false);
      console.log(response);
      if (response.status) {
        const bootstrapModal = new bootstrap.Modal(
          document.getElementById("join_success_mdl")
        );
        bootstrapModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for join group by mail
  const joinGroupByMail = async (memberShipId) => {
    if (!isJoined) {
      try {
        let groupData = {
          user: userInfo._id,
          memberstatus: "1",
          joiningdate: todayValue,
          group: params.id,
        };

        console.log("Join group Data------>", groupData);

        let requestURL =
          url.API_BASE_URL +
          url.API_JOIN_GROUP_BY_MAIL +
          `/${memberShipId}?token=${token}`;

        const response = await putData(requestURL, groupData);

        console.log("response in join group by mail------->", response);

        if (response.status) {
          setRefreshGroup(true);

          const currentPath = location.pathname;

          if (location.search) {
            // replace current entry with clean URL
            history.replace(currentPath);
          }
        }
      } catch (error) {
        console.log(error.message);
      }
    } else {
      console.log("already joined");
    }
  };

  useEffect(() => {
    if (params.id) {
      getgroupDetails();
    }
  }, [params.id]);

  useEffect(() => {
    if (refreshGroup) {
      getgroupDetails();
      setRefreshGroup(false);
    }
  }, [refreshGroup]);

  useEffect(() => {
    if (emailParams && memberShipIdParams) {
      if (userInfo.email === emailParams) {
        joinGroupByMail(memberShipIdParams);
      }
    }
  }, [emailParams, memberShipIdParams]);

  useEffect(() => {
    if (secretParam) {
      joinGroupHandler();
    }
  }, [secretParam]);

  if (isDetailsLoading) {
    return <LeftPanlePlaceholder />;
  }

  return (
    <div className="pnl_leftpnl">
      <div className="white_shadow_bx people_leftusr">
        <div className="groups_info">
          <div className="top_grp_info">
            <div className="experts_holdertext">
              <h3>{groupDetails?.title}</h3>
              <div className="event_hdng mt-2">
                <h5>
                  {groupDetails?.isprivate ? t("Private") : t("Public")} Group
                </h5>
              </div>
            </div>
            <div className="share_btn">
              <Link to="#" onClick={shareHandler}>
                <span className="material-icons">share</span>
              </Link>
            </div>
          </div>

          <div className="event_tags">
            <ul className="list_stye_none">
              <li>
                <i className="material-icons-outlined ornge_icon">star</i>
                <span className="rating">
                  {groupDetails?.totalratings}{" "}
                  <em>({groupDetails?.totalreviews})</em>
                </span>
              </li>
              <li>
                <i className="material-icons-outlined">place</i>
                <span>
                  {groupDetails?.city}, {groupDetails?.country}
                </span>
              </li>
            </ul>
          </div>

          {/* event members  */}
          <div className="event_user grps_user">
            <ul className="list_stye_none">
              {groupDetails?.groupmemberlist
                ?.slice(0, memberLimit)
                .map((member, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">
                        <img
                          className="rounded-circle"
                          width="40"
                          height="40"
                          src={
                            member.userimage == ""
                              ? assetImages.defaultUser
                              : url.SERVER_URL + member.userimage
                          }
                          alt=""
                        />
                      </Link>
                    </li>
                  );
                })}
              <li>
                {groupDetails?.groupmemberlist?.length > memberLimit ? (
                  <Link to="#">
                    +{groupDetails?.groupmemberlist?.length - memberLimit} more
                  </Link>
                ) : null}
              </li>
            </ul>
          </div>

          <div className="feedback_jointbtnOuter">
            {groupDetails?.moderator?._id?.toString() ===
              userInfo._id.toString() ||
            userInfo.role.slug === "ADMIN" ||
            userInfo.role.slug === "SUPER_ADMIN" ? null : (
              <div className="feed_btn">
                {groupDetails?.isapproved ? (
                  <Link to="#" className="btn gray_btn accepted_btn">
                    <i className="material-icons-outlined"> check_circle </i>
                    <span>{t("Joined")}</span>
                  </Link>
                ) : groupDetails?.isjoined ? null : (
                  <button
                    className="btn outline"
                    onClick={joinGroupHandler}
                    disabled={isJoining ? true : false}
                    style={{
                      cursor: isJoining ? "not-allowed" : "pointer",
                    }}
                  >
                    {t("Join Now")}
                    {isJoining && (
                      <div
                        className="mx-2 spinner-border spinner-border-sm"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    )}
                  </button>
                )}
              </div>
            )}

            {userInfo.role.slug === "ADMIN" ||
            userInfo.role.slug === "SUPER_ADMIN" ||
            groupDetails?.moderator?._id?.toString() ===
              userInfo._id.toString() ||
            groupDetails?.isapproved ? (
              <div className="feed_btn">
                <Link
                  to="#"
                  className="btn gray_btn"
                  data-bs-toggle="modal"
                  data-bs-target="#feedback_mdl"
                >
                  {t("Give Review")}
                </Link>
              </div>
            ) : null}

            <div className="tag_btn">
              <Link
                data-bs-target="#tags_modal"
                data-bs-toggle="modal"
                to="#"
                className="btn gray_btn"
              >
                <i className="material-icons-outlined">more</i>
              </Link>
            </div>
            {/* add tag button end */}
          </div>

          {groupDetails?.moderator?._id?.toString() ===
          userInfo._id.toString() ? (
            <div className="connected_text">
              <p className="d-flex align-items-center justify-content-start">
                <i className="material-icons">check_circle</i>
                <span>{t("Moderator")}</span>
              </p>
            </div>
          ) : (
            <div className="connected_text">
              {groupDetails?.isapproved ? (
                <p className="d-flex align-items-center justify-content-start">
                  <i className="material-icons">check_circle</i>
                  <span>
                    {t("Joined on")}{" "}
                    {getDateFormatInSpanish(groupDetails?.approvaldate)}
                  </span>
                </p>
              ) : groupDetails?.isjoined ? (
                <p className="d-flex align-items-center justify-content-start">
                  <i className="material-icons text-danger">error</i>
                  <span>
                    {t("Request on")}{" "}
                    {getDateFormatInSpanish(groupDetails?.requestdate)}
                  </span>
                </p>
              ) : null}
            </div>
          )}

          <div className="tags_links mt-4 mb-2">
            <ul className="list_stye_none d-flex flex-wrap gap-2 ">
              {groupDetails?.grouptags?.map((tag, index) => {
                return (
                  <li key={index}>
                    <Link to="#" className="d-flex align-items-center gap-2">
                      <span>{tag.title}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GroupDetailsLeftPanel;
