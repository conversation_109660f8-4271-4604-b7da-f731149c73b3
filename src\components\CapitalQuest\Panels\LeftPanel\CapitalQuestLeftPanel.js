/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import { FormControlLabel, Switch } from "@mui/material";
import FetchFromDocModal from "components/CvBuilder/Modals/FetchFromDocModal";

import ExecutiveSummaryModal from "./Modals/ExecutiveSummaryModal";
import FoundingTeamModal from "./Modals/FoundingTeamModal";
import ProductServiceModal from "./Modals/ProductServiceModal";
import MarketAnalysisModal from "./Modals/MarketAnalysisModal";
import TractionMetricsModal from "./Modals/TractionMetricsModal";
import BusinessModelModal from "./Modals/BusinessModelModal";
import FundingRequestModal from "./Modals/FundingRequestModal";
import FinancialProjectionsModal from "./Modals/FinancialProjectionsModal";
import ExitStrategyModal from "./Modals/ExitStrategyModal";
import RisksMitigationModal from "./Modals/RisksMitigationModal";
import SubmitterInfoModal from "./Modals/SubmitterInfoModal";

import ApplySuccessModal from "components/CvBuilder/Modals/ApplySuccessModal"; 

//**------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData, putData, uploadSingleFile } from "utils/Gateway";

import { assetImages } from "constants";
import { useTranslation } from "react-i18next";

const CapitalQuestLeftPanel = ({
  getChallengeDetails = () => {},
}) => {

  const { t } = useTranslation();
  const params = useParams();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [uploadedDocUrl, setUploadedDocUrl] = useState("");
  const [docContent, setDocContent] = useState("");
  const [fetchingUploadedData, setFetchingUploadedData] = useState(false);
  const [aiJsonContent, setAiJsonContent] = useState("");

  const [userImagePath, setUserImagePath] = useState("");
  const [userName, setUserName] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [userPhone, setUserPhone] = useState("");
  const [userPosition, setUserPosition] = useState("");
  const [userimageid, setUserImageId] = useState("");

  // Add state variables for public/private visibility settings
  const [publicPersonalInformation, setPublicPersonalInformation] = useState(false);
  const [publicExecutiveSummary, setPublicExecutiveSummary] = useState(false);
  const [publicFoundingTeam, setPublicFoundingTeam] = useState(false);
  const [publicAboutTheProductOrService, setPublicAboutTheProductOrService] = useState(false);
  const [publicMarketAnalysis, setPublicMarketAnalysis] = useState(false);
  const [publicTractionAndMetrics, setPublicTractionAndMetrics] = useState(false);
  const [publicBusinessModel, setPublicBusinessModel] = useState(false);
  const [publicRequestForFunding, setPublicRequestForFunding] = useState(false);
  const [publicFinancialProjections, setPublicFinancialProjections] = useState(false);
  const [publicExitStrategy, setPublicExitStrategy] = useState(false);
  const [publicRisksAndMitigation, setPublicRisksAndMitigation] = useState(false);

  const [submittingData, setSubmittingData] = useState(false);

  const defaultQuestData = {
    // challengeId
    challengeId: params.id,

    // User Information
    userName: "",
    userEmail: "",
    userPhone: "",
    userPosition: "",
    userImagePath: "",
    userImageId: "",
    publicProfile: false,

    // Executive Summary
    startupname: "",
    startupwebsite: "",
    startupemail: "",
    startuptagline: "",
    startupdescription: "",
    problemstatement: "",
    solution: "",
    uniquevalueproposition: "",
    businessmodel: "",
    currentstatus: "",

    //Founders
    founders: [], // founderData

    // Product/Service
    productdescription: "",
    currentdevelopmentstage: "",
    productdevelopmentroadmap: "",
    technology: "",
    intellectualproperty: "",
    productdemo: [],
    productcompetitiveadvantage: "",

    // Market Analysis
    marketsize: "",
    markettrends: "",
    targetmarket: "",
    competitors: "",
    competitiveadvantage: "",
    entrybarriers: "",
    differentiationstrategy: "",
    customeracquisitionstrategy: "",

    // Traction and Metrics
    mainkpis: "",
    customerevolution: "",
    growthrate: "",
    revenue: "",
    cacandcltv: "",
    stategicpartnerships: "",
    customertestimonials: [],

    // Business Model
    revenuesources: "",
    distrubutionchannels: "",
    grossmargin: "",
    scalabilityplan: "",
    realtimeordertracking: false,
    pricingstrategy: [],

    // Request for Funding
    fundingamount: "",
    proposedvalue: "",
    fundinguse: "",
    expectedrunway: "",
    previousfunding: [],
    milestones: "",

    // Financial Projections
    revenueprojections: "",
    breakevenpoint: "",
    mainassumptions: "",
    scenarios: "",

    // Exit Strategy
    potentialacquirers: "",
    timeframe: "",
    exitinsector: [],
    estimatereturn: "",

    // Risks and Mitigation
    businessrisks: "",
    mitigationstrategies: "",
    swotanalysis: "",

    // Add public/private fields to the state
    personalInformation: false,
    executiveSummary: false,
    foundingTeam: false,
    aboutTheProductOrService: false,
    marketAnalysis: false,
    tractionAndMetrics: false,
    businessModel: false,
    requestForFunding: false,
    financialProjections: false,
    exitStrategy: false,
    risksAndMitigation: false,
  };

  const [capitalQuestResponseData, setCapitalQuestResponseData] = useState(defaultQuestData);

  const [founderData, setFounderData] = useState({
    name: "",
    role: "",
    about: "",
    experience: "",
    skills: "",
    linkedin: "",
    equityholding: "",
    notableadvisors: "",
  });
  const [founderList, setFounderList] = useState([]);
  const [founderIndex, setFounderIndex] = useState(null);

  const [validationErrors, setValidationErrors] = useState([]);

  const updateFounderBlock = (founder, index) => {
    setFounderIndex(index);
    setFounderData(founder);
  };

  const deleteFounderBlock = (index) => {
    const updatedList = [...founderList];
    updatedList.splice(index, 1);
    setFounderList(updatedList);
  };

  // Function to handle API data mapping
  const mapApiDataToState = (apiData) => {
    setCapitalQuestResponseData({
      ...capitalQuestResponseData,
      ...apiData,
    });

    // Set the public/private state values from API data
    if (apiData.personalInformation !== undefined) setPublicPersonalInformation(apiData.personalInformation);
    if (apiData.executiveSummary !== undefined) setPublicExecutiveSummary(apiData.executiveSummary);
    if (apiData.foundingTeam !== undefined) setPublicFoundingTeam(apiData.foundingTeam);
    if (apiData.aboutTheProductOrService !== undefined) setPublicAboutTheProductOrService(apiData.aboutTheProductOrService);
    if (apiData.marketAnalysis !== undefined) setPublicMarketAnalysis(apiData.marketAnalysis);
    if (apiData.tractionAndMetrics !== undefined) setPublicTractionAndMetrics(apiData.tractionAndMetrics);
    if (apiData.businessModel !== undefined) setPublicBusinessModel(apiData.businessModel);
    if (apiData.requestForFunding !== undefined) setPublicRequestForFunding(apiData.requestForFunding);
    if (apiData.financialProjections !== undefined) setPublicFinancialProjections(apiData.financialProjections);
    if (apiData.exitStrategy !== undefined) setPublicExitStrategy(apiData.exitStrategy);
    if (apiData.risksAndMitigation !== undefined) setPublicRisksAndMitigation(apiData.risksAndMitigation);
  };

  // Function to prepare data for API submission
  const prepareDataForApi = () => {
    return {
      ...capitalQuestResponseData,
      personalInformation: publicPersonalInformation,
      executiveSummary: publicExecutiveSummary,
      foundingTeam: publicFoundingTeam,
      aboutTheProductOrService: publicAboutTheProductOrService,
      marketAnalysis: publicMarketAnalysis,
      tractionAndMetrics: publicTractionAndMetrics,
      businessModel: publicBusinessModel,
      requestForFunding: publicRequestForFunding,
      financialProjections: publicFinancialProjections,
      exitStrategy: publicExitStrategy,
      risksAndMitigation: publicRisksAndMitigation,
    };
  };

  const validateApplicationData = () => {
    const errors = [];
    
    // Check required personal information
    if (!userName || userName.trim() === '') {
      errors.push("Your full name is required");
    }
    
    if (!userEmail || userEmail.trim() === '') {
      errors.push("Your email address is required");
    }
    
    // Check required executive summary fields
    if (!capitalQuestResponseData.startupname || capitalQuestResponseData.startupname.trim() === '') {
      errors.push("Startup name is required");
    }
    
    if (!capitalQuestResponseData.startupdescription || capitalQuestResponseData.startupdescription.trim() === '') {
      errors.push("Startup description is required");
    }
    
    if (!capitalQuestResponseData.problemstatement || capitalQuestResponseData.problemstatement.trim() === '') {
      errors.push("Problem statement is required");
    }
    
    if (!capitalQuestResponseData.solution || capitalQuestResponseData.solution.trim() === '') {
      errors.push("Solution is required");
    }
    
    // Check founding team
    if (founderList.length === 0) {
      errors.push("At least one founder with name and role is required");
    } else {
      // Check if any founder has missing name or role
      const invalidFounders = founderList.filter(
        founder => !founder.name || founder.name.trim() === '' || !founder.role || founder.role.trim() === ''
      );
      
      if (invalidFounders.length > 0) {
        errors.push("All founders must have both name and role");
      }
    }
    
    // Check required product/service fields
    if (!capitalQuestResponseData.productdescription || capitalQuestResponseData.productdescription.trim() === '') {
      errors.push("Product description is required");
    }
    
    // Check required funding request fields
    if (!capitalQuestResponseData.fundingamount || capitalQuestResponseData.fundingamount.trim() === '') {
      errors.push("Funding amount is required");
    }
    
    setValidationErrors(errors);
    return errors;
  };

  const showConfirmationModal = () => {
    // Run validation before showing modal
    const errors = validateApplicationData();
    
    // Always show the modal
    const confirmModal = new bootstrap.Modal(document.getElementById('submit_confirmation_modal'));
    confirmModal.show();
  };

  const handleSubmitApplication = async () => {
    try {
      // map user data to capitalQuestResponseData
      capitalQuestResponseData.userName = userName;
      capitalQuestResponseData.userEmail = userEmail;
      capitalQuestResponseData.userPhone = userPhone;
      capitalQuestResponseData.userPosition = userPosition;
      capitalQuestResponseData.userImageId = userimageid;
      capitalQuestResponseData.founders = founderList;

      // Add public/private fields to the submission data
      capitalQuestResponseData.personalInformation = publicPersonalInformation;
      capitalQuestResponseData.executiveSummary = publicExecutiveSummary;
      capitalQuestResponseData.foundingTeam = publicFoundingTeam;
      capitalQuestResponseData.aboutTheProductOrService = publicAboutTheProductOrService;
      capitalQuestResponseData.marketAnalysis = publicMarketAnalysis;
      capitalQuestResponseData.tractionAndMetrics = publicTractionAndMetrics;
      capitalQuestResponseData.businessModel = publicBusinessModel;
      capitalQuestResponseData.requestForFunding = publicRequestForFunding;
      capitalQuestResponseData.financialProjections = publicFinancialProjections;
      capitalQuestResponseData.exitStrategy = publicExitStrategy;
      capitalQuestResponseData.risksAndMitigation = publicRisksAndMitigation;

      setSubmittingData(true);

      // Prepare data for submission
      const requestUrl =
        url.API_BASE_URL + url.API_SAVE_CAPITALQUEST_RESPONSE + `?token=${token}`;

      const response = await postData(requestUrl, capitalQuestResponseData);

      console.log("Response from capital quest API:", response);

      setSubmittingData(false);

      if (response.status) {
        // Hide modal and show success message
        let modal = document.querySelector("#submit_confirmation_modal");
        let bootstrapModal = bootstrap.Modal.getInstance(modal);
        bootstrapModal.hide();

        // show success modal 
        let successModal = new bootstrap.Modal(
          document.getElementById("apply_success_mdl")
        );
        successModal.show();

        // reload the challenge details to show the last response submitted
        getChallengeDetails();

      } else {
        throw new Error('Failed to submit');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      alert("Failed to submit application. Please try again.");
    }
  };

  //function for image upload
  const imageUploadHandler = async (e) => {
    const file = e.target.files[0];

    console.log(file);

    try {
      const requestUrl =
        url.API_BASE_URL + url.API_SINGLE_FILE_UPLOAD + `?token=${token}`;

      const response = await uploadSingleFile(requestUrl, file);

      console.log("upload response-------->", response);

      if (response.status) {
        setUserImagePath(response.data.path);
        setUserImageId(response.data._id);
      }
    } catch (error) {
      console.error(error.message);
    }
  };

  //** function for send uploaded doc file url to server or gpt api */
  const getDocumentDataUsingAI = async () => {
    try {
      // show wait while we fetch and map the data from AI
      setFetchingUploadedData(true);

      if (docContent.length > 10) {

        let aiParams = {
          message: docContent,
        };

        console.log("aiParams", aiParams);

        let requestUrl =
          url.API_BASE_URL + url.API_CHAT_GPT_CQ_ASSISTANT + `?token=${token}`;

        console.log("AI requestUrl", requestUrl);

        const response = await postData(requestUrl, aiParams);

        console.log("gpt response data", response);

        if (response.status) {
          setFetchingUploadedData(false); // stop showing loading
          setAiJsonContent(response.jsoncontent); // add the json content as received for mapping
        }

      }
    } catch (error) {
      console.log(error.message);
    }
  };

  // function to map the AI content to the state
  const mapAiContent = (aiJsonContent) => {
    if (aiJsonContent) {
      setUserName(aiJsonContent.userName || "");
      setUserEmail(aiJsonContent.userEmail || "");
      setUserPhone(aiJsonContent.userPhone || "");
      setUserPosition(aiJsonContent.userPosition || "");

      const mappedData = {
        // User Information
        userName: aiJsonContent.userName || "",
        userEmail: aiJsonContent.userEmail || "",
        userPhone: aiJsonContent.userPhone || "",
        userPosition: aiJsonContent.userPosition || "",
        
        publicProfile: aiJsonContent.publicProfile || false,

        // Executive Summary
        startupname: aiJsonContent.startupname || "",
        startupwebsite: aiJsonContent.startupwebsite || "",
        startupemail: aiJsonContent.startupemail || "",
        startuptagline: aiJsonContent.startuptagline || "",
        startupdescription: aiJsonContent.startupdescription || "",
        problemstatement: aiJsonContent.problemstatement || "",
        solution: aiJsonContent.solution || "",
        uniquevalueproposition: aiJsonContent.uniquevalueproposition || "",
        businessmodel: aiJsonContent.businessmodel || "",
        currentstatus: aiJsonContent.currentstatus || "",

        // Founders
        founders: Array.isArray(aiJsonContent.founders)
          ? aiJsonContent.founders.map((founder) => ({
              name: founder.name || "",
              role: founder.role || "",
              about: founder.about || "",
              experience: founder.experience || "",
              skills: founder.skills || "",
              linkedin: founder.linkedin || "",
              equityholding: founder.equityholding || "",
              notableadvisors: founder.notableadvisors || "",
            }))
          : [],

        // Product/Service
        productdescription: aiJsonContent.productdescription || "",
        currentdevelopmentstage: aiJsonContent.currentdevelopmentstage || "",
        productdevelopmentroadmap: aiJsonContent.productdevelopmentroadmap || "",
        technology: aiJsonContent.technology || "",
        intellectualproperty: aiJsonContent.intellectualproperty || "",
        productdemo: aiJsonContent.productdemo || [],
        productcompetitiveadvantage: aiJsonContent.productcompetitiveadvantage || "",

        // Market Analysis
        marketsize: aiJsonContent.marketsize || "",
        markettrends: aiJsonContent.markettrends || "",
        targetmarket: aiJsonContent.targetmarket || "",
        competitors: aiJsonContent.competitors || "",
        competitiveadvantage: aiJsonContent.competitiveadvantage || "",
        entrybarriers: aiJsonContent.entrybarriers || "",
        differentiationstrategy: aiJsonContent.differentiationstrategy || "",
        customeracquisitionstrategy: aiJsonContent.customeracquisitionstrategy || "",

        // Traction and Metrics
        mainkpis: aiJsonContent.mainkpis || "",
        customerevolution: aiJsonContent.customerevolution || "",
        growthrate: aiJsonContent.growthrate || "",
        revenue: aiJsonContent.revenue || "",
        cacandcltv: aiJsonContent.cacandcltv || "",
        stategicpartnerships: aiJsonContent.stategicpartnerships || "",
        customertestimonials: aiJsonContent.customertestimonials || [],

        // Business Model
        revenuesources: aiJsonContent.revenuesources || "",
        distrubutionchannels: aiJsonContent.distrubutionchannels || "",
        grossmargin: aiJsonContent.grossmargin || "",
        scalabilityplan: aiJsonContent.scalabilityplan || "",
        realtimeordertracking: aiJsonContent.realtimeordertracking || false,
        //pricingstrategy: aiJsonContent.pricingstrategy || [],

        // Request for Funding
        fundingamount: aiJsonContent.fundingamount || "",
        proposedvalue: aiJsonContent.proposedvalue || "",
        fundinguse: aiJsonContent.fundinguse || "",
        expectedrunway: aiJsonContent.expectedrunway || "",
        previousfunding: aiJsonContent.previousfunding || [],
        milestones: aiJsonContent.milestones || "",

        // Financial Projections
        revenueprojections: aiJsonContent.revenueprojections || "",
        breakevenpoint: aiJsonContent.breakevenpoint || "",
        mainassumptions: aiJsonContent.mainassumptions || "",
        scenarios: aiJsonContent.scenarios || "",

        // Exit Strategy
        potentialacquirers: aiJsonContent.potentialacquirers || "",
        timeframe: aiJsonContent.timeframe || "",
        exitinsector: aiJsonContent.exitinsector || [],
        estimatereturn: aiJsonContent.estimatereturn || "",

        // Risks and Mitigation
        businessrisks: aiJsonContent.businessrisks || "",
        mitigationstrategies: aiJsonContent.mitigationstrategies || "",
        swotanalysis: aiJsonContent.swotanalysis || "",
      };

      // Debugging: Log the mapped founders data
      console.log("Mapped Founders Data:", mappedData.founders);

      // Update the state with the mapped data
      setCapitalQuestResponseData((prevState) => ({
        ...prevState,
        ...mappedData,
      }));

      // Update the founderList state to match the manual process
      setFounderList(mappedData.founders);
    }
  };

  // reset capital quest form
  const resetCapitalQuest = () => {
    
    setUserName('');
    setUserEmail('');
    setUserImagePath('');
    setUserImageId('');
    setUserPhone('');
    setUserPosition('');  

    // reset this form reload getChallengeDetails
    setCapitalQuestResponseData(defaultQuestData);
    setFounderList([]);
    setFounderIndex(null);
    setDocContent('');
    setUploadedDocUrl('');
    setAiJsonContent('');

  }

  //** use effect for send uploaded doc file url to server
  useEffect(() => {
    if (docContent.length > 10) {
      console.log("docContent", docContent);
      getDocumentDataUsingAI();
    }
  }, [docContent]);

  // map the content only when we have the json data from AI api
  useEffect(() => {
    console.log("aiJsonContent", aiJsonContent)
    if(aiJsonContent && aiJsonContent != "") {
      console.log('mapping aiJsonContent');
      mapAiContent(aiJsonContent);
    }
    
  }, [aiJsonContent]);

  

  return (
    <div className="col-lg-6">
      <div className="builder_leftpnl">

        <div className="d-flex d-row">
          {/* Add a button to trigger saveCapitalQuestData for demonstration */}
          <Link className="btn outline me-4"
            to={`/jobs/details/${params.id}`}
          >
            <i className="material-icons-outlined">arrow_back</i>
            Back
          </Link>
          {/* <!-- ========== action buttons ends ========== --> */}

          <button className="btn btn-outline btn-sm me-2" onClick={resetCapitalQuest}>
            <i className="material-icons-outlined">refresh</i>
          </button>

          {/* <!-- ========== Start fetch from uploaded doc button ========== --> */}
          <button
            className="btn btn-sm btn-primary"
            data-bs-toggle="modal"
            data-bs-target="#fetch_from_doc"
          >
            <div
              className={`spinner-border me-1 ${
                fetchingUploadedData ? "" : "d-none"
              }`}
              role="status"
            >
              <span className="sr-only"></span>
            </div>
            <i className="material-icons me-2">upload_file</i>
            Upload from Doc
          </button>
          {/* <!-- ========== End fetch from uploaded doc button ========== --> */}
        </div>
        

        <div className="accordion mt-2 w-100" id="helpAccordion">
          <div className="accordion-item">
            <h2 className="accordion-header" id="headingOne">
              <button
                className="accordion-button collapsed"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#collapseOne"
                aria-expanded="false"
                aria-controls="collapseOne"
              >
                How to Apply & Submit Your Response?
              </button>
            </h2>
            <div
              id="collapseOne"
              className="accordion-collapse collapse"
              aria-labelledby="headingOne"
              data-bs-parent="#helpAccordion"
            >
              <div className="accordion-body">
                <ol>
                  <li>
                    Add your details about Capital Quest in respective blocks. To file response, we need certain information for evaluation.
                  </li>

                </ol>
              </div>
            </div>
          </div>
        </div>

        {/* <!-- ========== Capital Quest Sections ========== --> */}
        <div className="builder_item white_shadow_bx px-4 py-0 mb-3">

          {/* Add User Information Section with privacy toggle */}
          <div className="build_innr_row py-4 overflow-hidden">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">

              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Personal Information <span className="text-danger">*</span></h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicPersonalInformation}
                        onChange={(e) => setPublicPersonalInformation(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicPersonalInformation ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#submitter_info_modal"
                  >
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>

            <div className="builder_usr_row d-flex justify-content-between">

              <div className="build_usrleft_pnl d-flex gap-3">
                <div className="build_usrpic position-relative">
                  <figure>
                    <img
                      src={
                        userImagePath == ""
                          ? assetImages.defaultUser
                          : url.SERVER_URL + userImagePath
                      }
                      alt=""
                    />
                  </figure>
                  <a href="#" className="upload_camera">
                    <input
                      type="file"
                      className="file_btn"
                      id="uploadedImageFile"
                      onChange={imageUploadHandler}
                    />
                    <span className="material-icons">photo_camera</span>
                  </a>
                </div>

                <div className="build_usrinfo">
                  <h3>
                    {userName || <span className="text-muted">Full Name <span className="text-danger">*</span></span>}
                    <span className="d-block">{userPosition}</span>
                  </h3>
                  <ul className="list_stye_none d-flex align-items-center mt-3 gap-4 mb-3">
                    <li className="d-flex align-items-center gap-2">
                      <i className="material-icons">email</i>
                      <span>{userEmail || <span className="text-muted">Email <span className="text-danger">*</span></span>}</span>
                    </li>
                  </ul>
                  <ul className="list_stye_none d-flex align-items-center mt-3 gap-4 mb-3">
                    <li className="d-flex align-items-center gap-2">
                      <i className="material-icons">phone</i>
                      <span>{userPhone || '_ _ - _ _ _ _ _'}</span>
                    </li>
                  </ul>
                </div>
              </div>

            </div>
          </div>

          {/* Executive Summary Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Executive Summary <span className="text-danger">*</span></h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicExecutiveSummary}
                        onChange={(e) => setPublicExecutiveSummary(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicExecutiveSummary ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#executive_summary_modal"
                  >
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>

            <div
              className="build_experience_details"
              style={{ marginLeft: "30px" }}
            >
              <p className="mb-2">
                <span><strong>Startup Name:</strong> <span className="text-danger">*</span></span>{" "}
                {capitalQuestResponseData.startupname || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Website:</strong> </span>{" "}
                {capitalQuestResponseData.startupwebsite || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Description:</strong> <span className="text-danger">*</span></span>
                {capitalQuestResponseData.startupdescription || "N/A"}
              </p>

              <p className="mb-2">
                <span><strong>Email:</strong> </span>{" "}
                {capitalQuestResponseData.startupemail || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Tagline:</strong> </span>{" "}
                {capitalQuestResponseData.startuptagline || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Problem Statement:</strong> <span className="text-danger">*</span></span>{" "}
                {capitalQuestResponseData.problemstatement || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Solution:</strong> <span className="text-danger">*</span></span>{" "}
                {capitalQuestResponseData.solution || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Unique Value Proposition:</strong> </span>{" "}
                {capitalQuestResponseData.uniquevalueproposition || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Business Model:</strong> </span>{" "}
                {capitalQuestResponseData.businessmodel || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Current Status:</strong> </span>{" "}
                {capitalQuestResponseData.currentstatus || "N/A"}
              </p>
            </div>
          </div>

          {/* Founding Team Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Founding Team <span className="text-danger">*</span></h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicFoundingTeam}
                        onChange={(e) => setPublicFoundingTeam(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicFoundingTeam ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#founding_team_modal"
                    onClick={() => {
                      setFounderIndex(null);
                      setFounderData({
                        name: "",
                        role: "",
                        about: "",
                        experience: "",
                        skills: "",
                        linkedin: "",
                        equityholding: "",
                        notableadvisors: "",
                      });
                    }}
                  >
                    <i className="material-icons">add</i>
                  </Link>
                </div>
              </div>
            </div>
            {founderList.length === 0 && (
              <div className="alert alert-warning mb-3" role="alert">
                Please add at least one founder with name and role.
              </div>
            )}
            <div className="build_experience_details">
              {founderList.map((founder, index) => (
                <div
                  className="build_exprience_row d-flex justify-content-between align-items-start pb-4"
                  key={index}
                >
                  <div className="left_exp_info d-flex gap-3 align-items-start">
                    <div className="exp_logo"></div>
                    <div className="exp_logo_details">
                      <h4>
                        {founder.name} <span>({founder.role})</span>
                      </h4>
                      <p><strong>About:</strong> {founder.about}</p>
                      <p><strong>Experience:</strong> {founder.experience}</p>
                      <p><strong>Skills:</strong> {founder.skills}</p>
                      <p><strong>LinkedIn:</strong> {founder.linkedin}</p>
                      <p><strong>Equity Holding:</strong> {founder.equityholding}</p>
                      <p><strong>Notable Advisors:</strong> {founder.notableadvisors}</p>
                    </div>
                  </div>
                  <div className="build_edit_icon">
                    <Link
                      to="#"
                      data-bs-toggle="modal"
                      data-bs-target="#founding_team_modal"
                      onClick={() => updateFounderBlock(founder, index)}
                    >
                      <i className="material-icons">edit</i>
                    </Link>
                    <Link to="#" onClick={() => deleteFounderBlock(index)}>
                      <i className="material-icons">delete</i>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Product/Service Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Product/Service <span className="text-danger">*</span></h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicAboutTheProductOrService}
                        onChange={(e) => setPublicAboutTheProductOrService(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicAboutTheProductOrService ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#product_service_modal"
                  >
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Product Description:</strong> <span className="text-danger">*</span></span>
                {capitalQuestResponseData.productdescription || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Current Development Stage:</strong> </span>
                {capitalQuestResponseData.currentdevelopmentstage || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Development Roadmap:</strong> </span>
                {capitalQuestResponseData.productdevelopmentroadmap || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Technology:</strong> </span>
                {capitalQuestResponseData.technology || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Intellectual Property:</strong> </span>
                {capitalQuestResponseData.intellectualproperty || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Competitive Advantage:</strong> </span>
                {capitalQuestResponseData.productcompetitiveadvantage || "N/A"}
              </p>
            </div>
          </div>

          {/* Market Analysis Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Market Analysis</h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicMarketAnalysis}
                        onChange={(e) => setPublicMarketAnalysis(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicMarketAnalysis ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#market_analysis_modal"
                  >
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Market Size:</strong> </span>
                {capitalQuestResponseData.marketsize || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Market Trends:</strong> </span>
                {capitalQuestResponseData.markettrends || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Target Market:</strong> </span>
                {capitalQuestResponseData.targetmarket || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Competitors:</strong> </span>
                {capitalQuestResponseData.competitors || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Competitive Advantage:</strong> </span>
                {capitalQuestResponseData.competitiveadvantage || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Entry Barriers:</strong> </span>
                {capitalQuestResponseData.entrybarriers || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Differentiation Strategy:</strong> </span>
                {capitalQuestResponseData.differentiationstrategy || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Customer Acquisition Strategy:</strong> </span>
                {capitalQuestResponseData.customeracquisitionstrategy || "N/A"}
              </p>
            </div>
          </div>

          {/* Traction Metrics Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Traction and Metrics</h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicTractionAndMetrics}
                        onChange={(e) => setPublicTractionAndMetrics(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicTractionAndMetrics ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link to="#" data-bs-toggle="modal" data-bs-target="#traction_metrics_modal">
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Main KPIs:</strong> </span>
                {capitalQuestResponseData.mainkpis || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Customer Evolution:</strong> </span>
                {capitalQuestResponseData.customerevolution || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Growth Rate:</strong> </span>
                {capitalQuestResponseData.growthrate || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Revenue:</strong> </span>
                {capitalQuestResponseData.revenue || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>CAC and CLTV:</strong> </span>
                {capitalQuestResponseData.cacandcltv || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Strategic Partnerships:</strong> </span>
                {capitalQuestResponseData.stategicpartnerships || "N/A"}
              </p>
              {capitalQuestResponseData.customertestimonials?.length > 0 && (
                <div className="mb-2">
                  <span><strong>Customer Testimonials:</strong></span>
                  <ul className="list-unstyled mt-2 ms-3">
                    {capitalQuestResponseData.customertestimonials.map((testimonial, index) => (
                      <li key={index}>• {testimonial}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Business Model Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Business Model</h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicBusinessModel}
                        onChange={(e) => setPublicBusinessModel(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicBusinessModel ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link to="#" data-bs-toggle="modal" data-bs-target="#business_model_modal">
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Revenue Sources:</strong> </span>
                {capitalQuestResponseData.revenuesources || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Distribution Channels:</strong> </span>
                {capitalQuestResponseData.distrubutionchannels || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Gross Margin:</strong> </span>
                {capitalQuestResponseData.grossmargin || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Scalability Plan:</strong> </span>
                {capitalQuestResponseData.scalabilityplan || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Real-time Order Tracking:</strong> </span>
                {capitalQuestResponseData.realtimeordertracking ? "Yes" : "No"}
              </p>
              {capitalQuestResponseData.pricingstrategy?.length > 0 && (
                <div className="mb-2">
                  <span><strong>Pricing Strategy:</strong></span>
                  <ul className="list-unstyled mt-2 ms-3">
                    {capitalQuestResponseData.pricingstrategy.map((strategy, index) => (
                      <li key={index}>• {strategy.duration} - {strategy.name} (${strategy.price})</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Funding Request Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Funding Request <span className="text-danger">*</span></h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicRequestForFunding}
                        onChange={(e) => setPublicRequestForFunding(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicRequestForFunding ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link to="#" data-bs-toggle="modal" data-bs-target="#funding_request_modal">
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Funding Amount:</strong> <span className="text-danger">*</span></span>
                {capitalQuestResponseData.fundingamount || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Proposed Value:</strong> </span>
                {capitalQuestResponseData.proposedvalue || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Funding Use:</strong> </span>
                {capitalQuestResponseData.fundinguse || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Expected Runway:</strong> </span>
                {capitalQuestResponseData.expectedrunway || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Milestones:</strong> </span>
                {capitalQuestResponseData.milestones || "N/A"}
              </p>
              {capitalQuestResponseData.previousfunding?.length > 0 && (
                <div className="mb-2">
                  <span><strong>Previous Funding:</strong></span>
                  <ul className="list-unstyled mt-2 ms-3">
                    {capitalQuestResponseData.previousfunding.map((funding, index) => (
                      <li key={index}>• {new Date(funding.date).toLocaleDateString()} - {funding.company} (${funding.amount})</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Financial Projections Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Financial Projections</h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicFinancialProjections}
                        onChange={(e) => setPublicFinancialProjections(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicFinancialProjections ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#financial_projections_modal"
                  >
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Revenue Projections:</strong> </span>
                {capitalQuestResponseData.revenueprojections || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Break-even Point:</strong> </span>
                {capitalQuestResponseData.breakevenpoint || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Main Assumptions:</strong> </span>
                {capitalQuestResponseData.mainassumptions || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Scenarios:</strong> </span>
                {capitalQuestResponseData.scenarios || "N/A"}
              </p>
            </div>
          </div>

          {/* Exit Strategy Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Exit Strategy</h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicExitStrategy}
                        onChange={(e) => setPublicExitStrategy(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicExitStrategy ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link to="#" data-bs-toggle="modal" data-bs-target="#exit_strategy_modal">
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Potential Acquirers:</strong> </span>
                {capitalQuestResponseData.potentialacquirers || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Timeframe:</strong> </span>
                {capitalQuestResponseData.timeframe || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Estimated Return:</strong> </span>
                {capitalQuestResponseData.estimatereturn || "N/A"}
              </p>
              {capitalQuestResponseData.exitinsector?.length > 0 && (
                <div className="mb-2">
                  <span><strong>Exit Sectors:</strong> </span>
                  <ul className="list-unstyled mt-2 ms-3">
                    {capitalQuestResponseData.exitinsector.map((sector, index) => (
                      <li key={index}>• {sector}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Risks and Mitigation Section with privacy toggle */}
          <div className="build_innr_row py-4">
            <div className="build_hdng_row d-flex align-items-center justify-content-between gap-2 mb-4">
              <div className="build_hdng d-flex align-items-center gap-1">
                <i className="material-icons">drag_indicator</i>
                <h3>Risks and Mitigation</h3>
              </div>

              <div className="d-flex align-items-center gap-2">
                <div>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={publicRisksAndMitigation}
                        onChange={(e) => setPublicRisksAndMitigation(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={publicRisksAndMitigation ? 'Public' : 'Private'}
                  />
                </div>
                <div className="build_edit_icon">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#risks_mitigation_modal"
                  >
                    <i className="material-icons">edit</i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="build_experience_details" style={{ marginLeft: "30px" }}>
              <p className="mb-2">
                <span><strong>Business Risks:</strong> </span>
                {capitalQuestResponseData.businessrisks || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>Mitigation Strategies:</strong> </span>
                {capitalQuestResponseData.mitigationstrategies || "N/A"}
              </p>
              <p className="mb-2">
                <span><strong>SWOT Analysis:</strong> </span>
                {capitalQuestResponseData.swotanalysis || "N/A"}
              </p>
            </div>
          </div>

          {/* After Risks and Mitigation Section */}
          <div className="d-flex flex-column align-items-end mt-4 mb-4">
            <p className="text-muted mb-2">Fields marked with <span className="text-danger">*</span> are required</p>
            <button
              className="btn btn-primary"
              onClick={showConfirmationModal}
            >
              Submit Application
            </button>
          </div>

        </div>

        {/* Modals */}
        <ExecutiveSummaryModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <FoundingTeamModal
          founderIndex={founderIndex}
          founderData={founderData}
          setFounderData={setFounderData}
          founderList={founderList}
          setFounderList={setFounderList}
          setFounderIndex={setFounderIndex}
        />

        <ProductServiceModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <MarketAnalysisModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <TractionMetricsModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <BusinessModelModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <FundingRequestModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <FinancialProjectionsModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <ExitStrategyModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <RisksMitigationModal
          capitalQuestResponseData={capitalQuestResponseData}
          setCapitalQuestResponseData={setCapitalQuestResponseData}
        />
        <SubmitterInfoModal
          userName={userName}
          setUserName={setUserName}
          userEmail={userEmail}
          setUserEmail={setUserEmail}
          userPhone={userPhone}
          setUserPhone={setUserPhone}
          userPosition={userPosition}
          setUserPosition={setUserPosition}
        />

        {/* Submit Confirmation Modal with enhanced UI */}
        <div className="modal fade" id="submit_confirmation_modal">
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  {validationErrors.length > 0 ? 'Required Fields Missing' : 'Confirm Submission'}
                </h5>
                <button
                  type="button"
                  className="close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                >
                  <i className="material-icons-outlined">close</i>
                </button>
              </div>
              <div className="modal-body">
                {/* Show validation errors if any */}
                {validationErrors.length > 0 ? (
                  <div className="alert alert-danger">
                    <p><strong>Please correct the following issues before submitting:</strong></p>
                    <ul className="mb-0">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <div className="alert alert-warning">
                    <strong>Warning:</strong> Make sure you added all necessary information.
                    Once submitted, it can't be edited.
                  </div>
                )}

                {/* Styled submission review */}
                <div className="submission-review">
                  <h6 className="text-center mb-4">Review Your Information</h6>
                  
                  {/* Personal Information */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Personal Information <span className="text-danger">*</span></h6>
                      <span className="ms-2 badge bg-secondary">{publicPersonalInformation ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Name:</strong> <span className="text-danger">*</span> {userName || "Not provided"}</p>
                          <p><strong>Email:</strong> <span className="text-danger">*</span> {userEmail || "Not provided"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Phone:</strong> {userPhone || "Not provided"}</p>
                          <p><strong>Position:</strong> {userPosition || "Not provided"}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                
                  {/* Executive Summary */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Executive Summary <span className="text-danger">*</span></h6>
                      <span className="ms-2 badge bg-secondary">{publicExecutiveSummary ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Startup Name:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.startupname || "Not provided"}</p>
                          <p><strong>Website:</strong> {capitalQuestResponseData.startupwebsite || "N/A"}</p>
                          <p><strong>Email:</strong> {capitalQuestResponseData.startupemail || "N/A"}</p>
                          <p><strong>Tagline:</strong> {capitalQuestResponseData.startuptagline || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                        <p><strong>Description:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.startupdescription ? capitalQuestResponseData.startupdescription.substring(0, 100) + '...' : "Not provided"}</p>
                          <p><strong>Problem Statement:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.problemstatement ? capitalQuestResponseData.problemstatement.substring(0, 100) + '...' : "Not provided"}</p>
                          <p><strong>Solution:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.solution ? capitalQuestResponseData.solution.substring(0, 100) + '...' : "Not provided"}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Founding Team */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Founding Team <span className="text-danger">*</span></h6>
                      <span className="ms-2 badge bg-secondary">{publicFoundingTeam ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      {founderList.length > 0 ? (
                        founderList.map((founder, index) => (
                          <div key={index} className="p-3 mb-3 border rounded">
                            <h6>Founder {index + 1}</h6>
                            <div className="row">
                              <div className="col-md-6">
                                <p><strong>Name:</strong> <span className="text-danger">*</span> {founder.name || "Not provided"}</p>
                                <p><strong>Role:</strong> <span className="text-danger">*</span> {founder.role || "Not provided"}</p>
                                <p><strong>LinkedIn:</strong> {founder.linkedin || "N/A"}</p>
                                <p><strong>Equity Holding:</strong> {founder.equityholding || "N/A"}</p>
                              </div>
                              <div className="col-md-6">
                                <p><strong>Experience:</strong> {founder.experience || "N/A"}</p>
                                <p><strong>Skills:</strong> {founder.skills || "N/A"}</p>
                                <p><strong>About:</strong> {founder.about ? founder.about.substring(0, 70) + '...' : "N/A"}</p>
                                <p><strong>Notable Advisors:</strong> {founder.notableadvisors || "N/A"}</p>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="alert alert-warning">
                          <p className="mb-0">No founding team members added. At least one founder is required.</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Product/Service */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Product/Service <span className="text-danger">*</span></h6>
                      <span className="ms-2 badge bg-secondary">{publicAboutTheProductOrService ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Description:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.productdescription ? capitalQuestResponseData.productdescription.substring(0, 100) + '...' : "Not provided"}</p>
                          <p><strong>Development Stage:</strong> {capitalQuestResponseData.currentdevelopmentstage || "N/A"}</p>
                          <p><strong>Roadmap:</strong> {capitalQuestResponseData.productdevelopmentroadmap ? capitalQuestResponseData.productdevelopmentroadmap.substring(0, 70) + '...' : "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Technology:</strong> {capitalQuestResponseData.technology || "N/A"}</p>
                          <p><strong>Intellectual Property:</strong> {capitalQuestResponseData.intellectualproperty || "N/A"}</p>
                          <p><strong>Competitive Advantage:</strong> {capitalQuestResponseData.productcompetitiveadvantage || "N/A"}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Market Analysis */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Market Analysis</h6>
                      <span className="ms-2 badge bg-secondary">{publicMarketAnalysis ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Market Size:</strong> {capitalQuestResponseData.marketsize || "N/A"}</p>
                          <p><strong>Market Trends:</strong> {capitalQuestResponseData.markettrends || "N/A"}</p>
                          <p><strong>Target Market:</strong> {capitalQuestResponseData.targetmarket || "N/A"}</p>
                          <p><strong>Competitors:</strong> {capitalQuestResponseData.competitors || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Competitive Advantage:</strong> {capitalQuestResponseData.competitiveadvantage || "N/A"}</p>
                          <p><strong>Entry Barriers:</strong> {capitalQuestResponseData.entrybarriers || "N/A"}</p>
                          <p><strong>Differentiation:</strong> {capitalQuestResponseData.differentiationstrategy || "N/A"}</p>
                          <p><strong>Customer Acquisition:</strong> {capitalQuestResponseData.customeracquisitionstrategy || "N/A"}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Funding Request */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Funding Request <span className="text-danger">*</span></h6>
                      <span className="ms-2 badge bg-secondary">{publicRequestForFunding ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Funding Amount:</strong> <span className="text-danger">*</span> {capitalQuestResponseData.fundingamount || "Not provided"}</p>
                          <p><strong>Proposed Value:</strong> {capitalQuestResponseData.proposedvalue || "N/A"}</p>
                          <p><strong>Funding Use:</strong> {capitalQuestResponseData.fundinguse || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Expected Runway:</strong> {capitalQuestResponseData.expectedrunway || "N/A"}</p>
                          <p><strong>Milestones:</strong> {capitalQuestResponseData.milestones || "N/A"}</p>
                          {capitalQuestResponseData.previousfunding?.length > 0 && (
                            <div>
                              <p><strong>Previous Funding:</strong></p>
                              <ul className="list-unstyled ms-3">
                                {capitalQuestResponseData.previousfunding.slice(0, 2).map((funding, index) => (
                                  <li key={index}>• {new Date(funding.date).toLocaleDateString()} - {funding.company} (${funding.amount})</li>
                                ))}
                                {capitalQuestResponseData.previousfunding.length > 2 && <li>• ... and {capitalQuestResponseData.previousfunding.length - 2} more</li>}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Traction and Metrics Section */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Traction and Metrics</h6>
                      <span className="ms-2 badge bg-secondary">{publicTractionAndMetrics ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Main KPIs:</strong> {capitalQuestResponseData.mainkpis || "N/A"}</p>
                          <p><strong>Customer Evolution:</strong> {capitalQuestResponseData.customerevolution || "N/A"}</p>
                          <p><strong>Growth Rate:</strong> {capitalQuestResponseData.growthrate || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Revenue:</strong> {capitalQuestResponseData.revenue || "N/A"}</p>
                          <p><strong>CAC and CLTV:</strong> {capitalQuestResponseData.cacandcltv || "N/A"}</p>
                          <p><strong>Strategic Partnerships:</strong> {capitalQuestResponseData.stategicpartnerships || "N/A"}</p>
                        </div>
                      </div>
                      {capitalQuestResponseData.customertestimonials?.length > 0 && (
                        <div>
                          <p><strong>Customer Testimonials:</strong></p>
                          <ul className="list-unstyled ms-3">
                            {capitalQuestResponseData.customertestimonials.slice(0, 2).map((testimonial, index) => (
                              <li key={index}>• {testimonial.substring(0, 70)}...</li>
                            ))}
                            {capitalQuestResponseData.customertestimonials.length > 2 && 
                              <li>• ... and {capitalQuestResponseData.customertestimonials.length - 2} more</li>}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Business Model Section */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Business Model</h6>
                      <span className="ms-2 badge bg-secondary">{publicBusinessModel ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Revenue Sources:</strong> {capitalQuestResponseData.revenuesources || "N/A"}</p>
                          <p><strong>Distribution Channels:</strong> {capitalQuestResponseData.distrubutionchannels || "N/A"}</p>
                          <p><strong>Gross Margin:</strong> {capitalQuestResponseData.grossmargin || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Scalability Plan:</strong> {capitalQuestResponseData.scalabilityplan || "N/A"}</p>
                          <p><strong>Real-time Order Tracking:</strong> {capitalQuestResponseData.realtimeordertracking ? "Yes" : "No"}</p>
                          
                          {capitalQuestResponseData.pricingstrategy?.length > 0 && (
                            <div>
                              <p><strong>Pricing Strategy:</strong></p>
                              <ul className="list-unstyled ms-3">
                                {capitalQuestResponseData.pricingstrategy.slice(0, 2).map((strategy, index) => (
                                  <li key={index}>• {strategy.duration} - {strategy.name} (${strategy.price})</li>
                                ))}
                                {capitalQuestResponseData.pricingstrategy.length > 2 && 
                                  <li>• ... and {capitalQuestResponseData.pricingstrategy.length - 2} more</li>}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Financial Projections Section */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Financial Projections</h6>
                      <span className="ms-2 badge bg-secondary">{publicFinancialProjections ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Revenue Projections:</strong> {capitalQuestResponseData.revenueprojections || "N/A"}</p>
                          <p><strong>Break-even Point:</strong> {capitalQuestResponseData.breakevenpoint || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>Main Assumptions:</strong> {capitalQuestResponseData.mainassumptions || "N/A"}</p>
                          <p><strong>Scenarios:</strong> {capitalQuestResponseData.scenarios || "N/A"}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Exit Strategy Section */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Exit Strategy</h6>
                      <span className="ms-2 badge bg-secondary">{publicExitStrategy ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Potential Acquirers:</strong> {capitalQuestResponseData.potentialacquirers || "N/A"}</p>
                          <p><strong>Timeframe:</strong> {capitalQuestResponseData.timeframe || "N/A"}</p>
                          <p><strong>Estimated Return:</strong> {capitalQuestResponseData.estimatereturn || "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          {capitalQuestResponseData.exitinsector?.length > 0 && (
                            <div>
                              <p><strong>Exit Sectors:</strong></p>
                              <ul className="list-unstyled ms-3">
                                {capitalQuestResponseData.exitinsector.slice(0, 3).map((sector, index) => (
                                  <li key={index}>• {sector}</li>
                                ))}
                                {capitalQuestResponseData.exitinsector.length > 3 && 
                                  <li>• ... and {capitalQuestResponseData.exitinsector.length - 3} more</li>}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Risks and Mitigation Section */}
                  <div className="card mb-4">
                    <div className="card-header d-flex align-items-center bg-light">
                      <h6 className="mb-0">Risks and Mitigation</h6>
                      <span className="ms-2 badge bg-secondary">{publicRisksAndMitigation ? 'Public' : 'Private'}</span>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <p><strong>Business Risks:</strong> {capitalQuestResponseData.businessrisks ? capitalQuestResponseData.businessrisks.substring(0, 100) + '...' : "N/A"}</p>
                          <p><strong>Mitigation Strategies:</strong> {capitalQuestResponseData.mitigationstrategies ? capitalQuestResponseData.mitigationstrategies.substring(0, 100) + '...' : "N/A"}</p>
                        </div>
                        <div className="col-md-6">
                          <p><strong>SWOT Analysis:</strong> {capitalQuestResponseData.swotanalysis ? capitalQuestResponseData.swotanalysis.substring(0, 100) + '...' : "N/A"}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Privacy Settings Summary */}
                  <div className="card mb-4">
                    <div className="card-header bg-light">
                      <h6 className="mb-0">Privacy Settings Summary</h6>
                    </div>
                    <div className="card-body">
                      <div className="row">
                        <div className="col-md-6">
                          <ul className="list-group">
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Personal Information
                              <span className={`badge ${publicPersonalInformation ? 'bg-success' : 'bg-secondary'}`}>
                                {publicPersonalInformation ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Executive Summary
                              <span className={`badge ${publicExecutiveSummary ? 'bg-success' : 'bg-secondary'}`}>
                                {publicExecutiveSummary ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Founding Team
                              <span className={`badge ${publicFoundingTeam ? 'bg-success' : 'bg-secondary'}`}>
                                {publicFoundingTeam ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Product/Service
                              <span className={`badge ${publicAboutTheProductOrService ? 'bg-success' : 'bg-secondary'}`}>
                                {publicAboutTheProductOrService ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Market Analysis
                              <span className={`badge ${publicMarketAnalysis ? 'bg-success' : 'bg-secondary'}`}>
                                {publicMarketAnalysis ? 'Public' : 'Private'}
                              </span>
                            </li>
                          </ul>
                        </div>
                        <div className="col-md-6">
                          <ul className="list-group">
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Traction & Metrics
                              <span className={`badge ${publicTractionAndMetrics ? 'bg-success' : 'bg-secondary'}`}>
                                {publicTractionAndMetrics ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Business Model
                              <span className={`badge ${publicBusinessModel ? 'bg-success' : 'bg-secondary'}`}>
                                {publicBusinessModel ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Funding Request
                              <span className={`badge ${publicRequestForFunding ? 'bg-success' : 'bg-secondary'}`}>
                                {publicRequestForFunding ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Financial Projections
                              <span className={`badge ${publicFinancialProjections ? 'bg-success' : 'bg-secondary'}`}>
                                {publicFinancialProjections ? 'Public' : 'Private'}
                              </span>
                            </li>
                            <li className="list-group-item d-flex justify-content-between align-items-center">
                              Exit Strategy & Risks
                              <span className={`badge ${publicExitStrategy || publicRisksAndMitigation ? 'bg-success' : 'bg-secondary'}`}>
                                {publicExitStrategy || publicRisksAndMitigation ? 'Public' : 'Private'}
                              </span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="text-center mt-4">
                    <p className="text-muted"><span className="text-danger">*</span> Indicates required fields</p>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  data-bs-dismiss="modal"
                >
                  Cancel
                </button>
                
                {/* Show Submit button only if there are no errors */}
                {validationErrors.length === 0 && (
                  <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleSubmitApplication}
                  disabled={submittingData} // optional: disables button while submitting
                >
                  {submittingData ? (
                    <>
                      <span
                        className="spinner-border spinner-border-sm me-2"
                        role="status"
                        aria-hidden="true"
                      ></span>
                      Please wait...
                    </>
                  ) : (
                    "Confirm and Submit"
                  )}
                </button>
                
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Thanks modal after successfull submition and response.status == true */}
        <ApplySuccessModal
          afterModalClose={resetCapitalQuest}
        />

        {/* <!-- ========== Start fetch from doc modal component ========== --> */}
        <FetchFromDocModal
          token={token}
          setUploadedDocUrl={setUploadedDocUrl}
          setDocContent={setDocContent}
        />
        {/* <!-- ========== End fetch from doc modal component ========== --> */}


      </div>
    </div>
  );
};

export default CapitalQuestLeftPanel;
