/* eslint-disable */
import React, { useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { postData } from "utils/Gateway";

const GiveReviewModal = ({
  moduleName,
  componentid,
  receiverid,
  ideaid,
  opportunityid,
  setIsReviewDone = () => {},
}) => {
  const { t } = useTranslation();
  const token = localStorage.getItem("token");

  const params = useParams();

  const [comments, setComments] = useState("");
  const [starRating, setStarRating] = useState("1");
  const [isReviewPosting, setIsReviewPosting] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState(
    "We value your honest opinion. Please tell us how your experience was."
  );

  const getStarClass = (starPosition) => {
    return starPosition <= parseInt(starRating) ? "active_star" : "";
  };

  const selectedStar = (e, star) => {
    e.preventDefault();
    setStarRating(star);
  };

  const reviewSubmitHandler = async () => {
    try {
      const reviewData = {
        comment: comments,
        rating: starRating,
      };

      let requestURL =
        url.API_BASE_URL +
        url.API_SUBMIT_REVIEW +
        `?token=${token}&moduleslug=${moduleName}`;

      switch (moduleName) {
        case "company":
          reviewData.companyid = params.id;
          break;
        case "appuser":
          reviewData.appuserid = params.id;
          break;
        case "event":
          reviewData.eventid = params.id;
          break;
        case "group":
          reviewData.groupid = params.id;
          break;
        case "course":
          reviewData.courseid = params.id;
          break;
        case "challenge":
          reviewData.challengeid = params.id;
          reviewData.ideaid = ideaid;
          reviewData.invoiceid = opportunityid;
          reviewData.receiverid = receiverid;
          break;
      }

      setIsReviewPosting(true);

      const response = await postData(requestURL, reviewData);

      setIsReviewPosting(false);

      if (response.status) {
        resetHandler();
        setIsReviewDone(true);

        const reviewModal = document.querySelector("#feedback_mdl");
        const modal = bootstrap.Modal.getInstance(reviewModal);
        modal.hide();

        const reviewSuccessModal = new bootstrap.Modal(
          document.getElementById("review_success_mdl")
        );
        reviewSuccessModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const resetHandler = () => {
    setStarRating("1");
    setComments("");
    setFeedbackMessage(
      "We value your honest opinion. Please tell us how your experience was."
    );
  };

  return (
    <div className="post_modal feedback_modal">
      <div
        className="modal fade"
        id="feedback_mdl"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <h3 className="modal-title">
                {t("Share Your Voice & Shape Our Future")}
              </h3>
              <button
                type="button"
                className="btn-close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <span className="material-icons-outlined">close</span>
              </button>
            </div>
            <div className="modal-body">
              <div className="course_rvw_row">
                <div className="rating_hdng">
                  <h5>{t("Let Your Stars Speak")}</h5>
                </div>
                <ul className="list_stye_none">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <li key={star} className={getStarClass(star)}>
                      <Link
                        to="#"
                        onClick={(e) => selectedStar(e, star.toString())}
                      >
                        <span className="material-icons-outlined">star</span>
                      </Link>
                    </li>
                  ))}
                </ul>
                <div className="feedback-message mt-2 text-center">
                  <em>{feedbackMessage}</em>
                </div>
              </div>
              <div className="post_description">
                <label>{t("Tell Us Your Story")}</label>
                <textarea
                  className="form-control"
                  placeholder="Share your thoughts and experiences..."
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                />
              </div>
              <div className="post_btn">
                <button
                  className="btn"
                  style={{
                    cursor: isReviewPosting ? "not-allowed" : "pointer",
                  }}
                  disabled={isReviewPosting}
                  onClick={reviewSubmitHandler}
                >
                  {t("Submit Your Valuable Feedback")}
                  {isReviewPosting && (
                    <div
                      className="mx-2 spinner-border spinner-border-sm"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiveReviewModal;
