/* eslint-disable */

import { useState, useEffect } from "react";
import { Link } from "react-router-dom";

const LanguageModal = ({ userLanguages, setUserLanguages }) => {

  const languagesTemplate = {
    languagetext: "",
  };

  const [languagesBlock, setLanguagesBlock] = useState([]);

  const addNewLanguagesBlock = () => {
    setLanguagesBlock([...languagesBlock, languagesTemplate]);
  };

  const deleteLanguagesBlock = (index) => {
    const deleteLanguagesArr = [...languagesBlock];
    deleteLanguagesArr.splice(index, 1);
    setLanguagesBlock(deleteLanguagesArr);
  };

  // function for change survey select
  const languagesInputHandler = (index, field, value) => {
    const updatedFormValues = languagesBlock.map((block, i) =>
      index == i? Object.assign(block, { [field]: value }) : block
    );

    setLanguagesBlock(updatedFormValues);
  };

  const updatedLanguagesBlock = (updatedData) => {
    setLanguagesBlock(updatedData);
  };

  const saveInfoHandler = () => {

    console.log('languagesBlock',languagesBlock);

    if (languagesBlock.length > 0) {
      setUserLanguages(languagesBlock);
    } else {
      setUserLanguages([]);
    }

    resetHandler();

    let languageModal = document.querySelector("#language_modal");
    let modal = bootstrap.Modal.getInstance(languageModal);
    modal.hide();
  };

  const resetHandler = () => {
    setLanguagesBlock([]);
  };

  useEffect(() => {
    if (userLanguages.length > 0) {
      updatedLanguagesBlock(userLanguages);
    }
  }, [userLanguages]);

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade show" id="language_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Languages</h3>
                <h5>Preferrer communication languages like English, Spanish etc.</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                // onClick={resetHandler}
              >
                <i className="material-icons-outlined">close </i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    {languagesBlock.map((languagesData, index) => {
                      return (
                        <div className="d-flex gap-1 mb-2" key={index}>
                          <input
                            type="text"
                            className="form-control"
                            placeholder="Enter Languages"
                            value={languagesData.languagetext}
                            onChange={(e) => {
                              languagesInputHandler(
                                index,
                                "languagetext",
                                e.target.value
                              );
                            }}
                          />
                          <div className="adnew_btn">
                            <Link
                              onClick={() => {
                                deleteLanguagesBlock(index);
                              }}
                              to="#"
                              className="d-inline-flex align-items-center justify-content-center gap-1"
                            >
                              <i className="material-icons-outlined">delete</i>
                            </Link>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div className="form-group">
                    <div className="adnew_btn">
                      <Link
                        to="#"
                        className="d-inline-flex align-items-center justify-content-center gap-1"
                        onClick={addNewLanguagesBlock}
                      >
                        <i className="material-icons-outlined">add </i>
                        <span>Add New</span>
                      </Link>
                    </div>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageModal;