/* eslint-disable */
import React, { useContext, useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useParams } from "react-router-dom";
import AsyncSelect from "react-select/async";

import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

import { assetImages } from "constants";

const ChatAiSummary = ({ roomId, isGroupChat = false }) => {
  // console.log("roomId >>>", roomId);

  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };

  const [startDate, setStartDate] = useState(getCurrentDate());
  const [endDate, setEndDate] = useState(getCurrentDate());

  const [loading, setLoading] = useState(false);
  const [loadingSummary, setLoadingSummary] = useState(false);
  const [chatContent, setChatContent] = useState("");
  const [summary, setSummary] = useState([]);
  const [plannedTasks, setPlannedTasks] = useState([]);
  const [task, setTask] = useState([]);
  const [error, setError] = useState("");
  const [managedTasks, setManagedTasks] = useState([]);
  const [createdTasks, setCreatedTasks] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Add custom styles for react-select
  const customSelectStyles = {
    control: (base) => ({
      ...base,
      minHeight: "31px",
      height: "31px",
    }),
    dropdownIndicator: (base) => ({
      ...base,
      padding: "4px",
      svg: {
        height: "16px",
        width: "16px",
      },
    }),
    clearIndicator: (base) => ({
      ...base,
      padding: "4px",
      svg: {
        height: "14px",
        width: "14px",
      },
    }),
    valueContainer: (base) => ({
      ...base,
      height: "31px",
      padding: "0 6px",
    }),
    input: (base) => ({
      ...base,
      margin: "0px",
      padding: "0px",
    }),
  };

  const validateDates = () => {
    if (!startDate || !endDate) {
      setError("Both start and end dates are required.");
      return false;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end < start) {
      setError("End date must be greater than or equal to start date.");
      return false;
    }

    return true;
  };

  const handleGenerateSummary = async () => {
    if (!validateDates()) {
      return;
    }
    setError("");
    setLoading(true);
    setSummary([]);
    setPlannedTasks([]);
    setChatContent("");

    try {
      let requestUrl =
        url.API_BASE_URL + url.API_GET_ALL_CHAT_MESSAGES + `?token=${token}`;

      requestUrl += `&startdate=${startDate}&enddate=${endDate}`;

      if (isGroupChat) {
        requestUrl += `&chatgroupid=${roomId}`;
      } else {
        requestUrl += `&chatpersonid=${roomId}`;
      }

      console.log("requestUrl", requestUrl);

      const response = await getData(requestUrl);

      console.log("chat response", response);

      if (response.status) {
        const allChatRecords = response.data;

        // if allChatRecords is array, run loop and concat all record.description
        if (Array.isArray(allChatRecords)) {
          let chatContent = "";
          allChatRecords.forEach((record) => {
            chatContent += record.description + "; ";
          });

          setChatContent(chatContent);
        }
      }
    } catch (error) {
      setError("Failed to fetch summary. Please try again.");
    }
    setLoading(false);
  };

  // get AI Summary and Task based on this chat text
  // Request API Post data with chatContent and get response
  const getTaskAndSummary = async () => {
    setLoadingSummary(true);
    setLoading(true);

    let requestUrl =
      url.API_BASE_URL + url.API_AI_CONVERSATION_TASK + `?token=${token}`;

    const body = {
      chatContent: chatContent,
    };

    try {
      const response = await postData(requestUrl, body);

      console.log("response", response);

      if (response.status) {
        setSummary(response.data.summary);
        setPlannedTasks(response.data.tasks);
      }

      setLoadingSummary(false);
      setLoading(false);
    } catch (error) {
      console.error("Failed to get AI summary", error);
    }
  };

  const loadProjectOptions = async (inputValue) => {
    if (!inputValue || inputValue.length < 2) {
      return [];
    }

    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_PROJECTLIST_BY_SEARCH +
        `?token=${token}&searchtitle=${inputValue}`;
      const response = await getData(requestUrl);

      if (response.status && response.data) {
        return response.data.map((project) => ({
          value: project._id,
          label: project.label,
        }));
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch project list", error);
      return [];
    }
  };

  // on change of chatContent, call getTaskAndSummary
  useEffect(() => {
    if (chatContent.length > 5) {
      getTaskAndSummary();
    }
  }, [chatContent]);

  // reset all when roomId changes
  useEffect(() => {
    setChatContent("");
    setSummary([]);
    setPlannedTasks([]);
    setError("");
  }, [roomId]);

  // Initialize managed tasks from planned tasks
  useEffect(() => {
    if (plannedTasks.length > 0) {
      const initialTasks = plannedTasks.map((task, index) => ({
        id: index + 1,
        title: task,
        startDate: getCurrentDate(),
        endDate: getCurrentDate(),
        hours: 0,
        priority: "Normal",
        rank: index + 1,
        project: null,
      }));
      setManagedTasks(initialTasks);
    }
  }, [plannedTasks]);

  const handleAddTask = () => {
    const newTask = {
      id: managedTasks.length + 1,
      title: "",
      startDate: getCurrentDate(),
      endDate: getCurrentDate(),
      hours: 0,
      priority: "Normal",
      rank: managedTasks.length + 1,
      project: null,
    };
    setManagedTasks([...managedTasks, newTask]);
  };

  const handleDeleteTask = (taskId) => {
    setManagedTasks(managedTasks.filter((task) => task.id !== taskId));
  };

  const handleTaskChange = (id, field, value) => {
    setManagedTasks(
      managedTasks.map((task) =>
        task.id === id ? { ...task, [field]: value } : task
      )
    );
  };

  const handleSubmitTasks = async () => {
    if (managedTasks.length === 0) return;

    setIsSubmitting(true);

    console.log("managedTasks", managedTasks);

    try {
      const requestUrl =
        url.API_BASE_URL + url.API_CREATE_TASKS_FROM_CHAT + `?token=${token}`;
      const response = await postData(requestUrl, { tasks: managedTasks });

      if (response.status) {
        setCreatedTasks(response.data);
        setManagedTasks([]); // Clear the form after successful creation
      }
    } catch (error) {
      setError("Failed to create tasks. Please try again.");
    }

    setIsSubmitting(false);
  };

  return (
    <div
      className="modal fade"
      id="chataisummary"
      tabIndex="-1"
      aria-labelledby="chataisummaryLabel"
      aria-hidden="true"
      data-bs-backdrop="static"
    >
      <div className="modal-dialog" style={{ minWidth: "90%" }}>
        <div className="modal-content shadow-xl">
          <div className="modal-header bg-primary text-white">
            <h5 className="modal-title" id="chataisummaryLabel">
              Chat AI Summary
            </h5>
            <button
              type="button"
              className="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>

          <div className="modal-body p-4">
            {/* Date Range Selection */}
            <div className="mb-3">
              <label className="form-label fw-semibold">
                Select Date Range:
              </label>
              <div className="d-flex gap-2">
                <input
                  type="date"
                  className="form-control"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
                <input
                  type="date"
                  className="form-control"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
                <button
                  className="btn btn-primary btn-sm"
                  onClick={handleGenerateSummary}
                  disabled={loading}
                >
                  {loading ? "Generating..." : "Show"}
                </button>
              </div>
            </div>

            {error && <p className="text-danger">{error}</p>}

            {/* show loadingSummary */}
            {loadingSummary && (
              <div className="text-center mt-3">
                <p>
                  ⏳ Please wait... while we generate summary and associated
                  tasks.
                </p>

                {/* Bootstrap Glow Placeholder */}
                <div className="placeholder-glow">
                  <span className="placeholder col-6"></span>
                  <span className="placeholder col-4"></span>
                  <span className="placeholder col-8"></span>
                </div>
              </div>
            )}

            {/* Summary Section */}
            {summary && summary.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Summary</h5>
                <ul className="list-unstyled mt-2">
                  {summary.map((item, index) => (
                    <li key={index} className="mb-2">
                      ✅ {item}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Planned Tasks Section */}
            {plannedTasks && plannedTasks.length > 0 && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <h5 className="fw-bold">Planned Tasks</h5>
                <ul className="list-unstyled mt-2">
                  {plannedTasks.map((task, index) => (
                    <li key={index} className="mb-2">
                      📌 {task}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Task Management Section */}
            {(summary?.length > 0 || plannedTasks?.length > 0) && (
              <div className="card border-0 shadow-sm p-3 mt-3">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h5 className="fw-bold mb-0">Manage Tasks</h5>
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={handleAddTask}
                  >
                    + Add New Task
                  </button>
                </div>

                {managedTasks.length > 0 && (
                  <div style={{ overflow: "auto", maxWidth: "100%" }}>
                    <div style={{ minWidth: "1200px" }}>
                      <table className="table table-bordered">
                        <thead className="bg-light">
                          <tr>
                            <th style={{ width: "35%" }}>Task Title</th>
                            <th style={{ width: "25%" }}>Project</th>
                            <th style={{ width: "8%" }}>Start Date</th>
                            <th style={{ width: "8%" }}>End Date</th>
                            <th style={{ width: "6%" }}>Hours</th>
                            <th style={{ width: "8%" }}>Priority</th>
                            <th style={{ width: "6%" }}>Rank</th>
                            <th style={{ width: "4%" }}>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {managedTasks.map((task) => (
                            <tr key={task.id}>
                              <td>
                                <textarea
                                  className="form-control form-control-sm"
                                  value={task.title}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "title",
                                      e.target.value
                                    )
                                  }
                                  rows="3"
                                  style={{ width: "100%", resize: "vertical" }}
                                  placeholder="Enter task description..."
                                />
                              </td>
                              <td>
                                <AsyncSelect
                                  cacheOptions
                                  defaultOptions
                                  value={task.project}
                                  loadOptions={loadProjectOptions}
                                  onChange={(selected) =>
                                    handleTaskChange(
                                      task.id,
                                      "project",
                                      selected
                                    )
                                  }
                                  placeholder="Search project..."
                                  styles={customSelectStyles}
                                  isClearable
                                />
                              </td>
                              <td>
                                <input
                                  type="date"
                                  className="form-control form-control-sm"
                                  value={task.startDate}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "startDate",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td>
                                <input
                                  type="date"
                                  className="form-control form-control-sm"
                                  value={task.endDate}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "endDate",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td>
                                <input
                                  type="number"
                                  className="form-control form-control-sm"
                                  value={task.hours}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "hours",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td>
                                <select
                                  className="form-select form-select-sm"
                                  value={task.priority}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "priority",
                                      e.target.value
                                    )
                                  }
                                >
                                  <option value="High">High</option>
                                  <option value="Normal">Normal</option>
                                  <option value="Low">Low</option>
                                </select>
                              </td>
                              <td>
                                <input
                                  type="number"
                                  className="form-control form-control-sm"
                                  value={task.rank}
                                  onChange={(e) =>
                                    handleTaskChange(
                                      task.id,
                                      "rank",
                                      e.target.value
                                    )
                                  }
                                />
                              </td>
                              <td className="text-center">
                                <Link
                                  to="#"
                                  className="text-danger"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    handleDeleteTask(task.id);
                                  }}
                                >
                                  <i className="material-icons-outlined">
                                    delete
                                  </i>
                                </Link>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>

                      {/* Add Submit Button */}
                      <div className="d-flex justify-content-end mt-3">
                        <button
                          className="btn btn-success"
                          onClick={handleSubmitTasks}
                          disabled={isSubmitting || managedTasks.length === 0}
                        >
                          {isSubmitting ? "Creating Tasks..." : "Add to Task"}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Display Created Tasks */}
                {createdTasks.length > 0 && (
                  <div className="mt-4">
                    <h6 className="fw-bold mb-3">Created Tasks</h6>
                    <div className="table-responsive">
                      <table className="table table-sm">
                        <thead className="bg-light">
                          <tr>
                            <th>Title</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {createdTasks.map((task) => (
                            <tr key={task.id}>
                              <td>{task.title}</td>
                              <td>
                                <span className="badge bg-success">
                                  Created
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatAiSummary;
