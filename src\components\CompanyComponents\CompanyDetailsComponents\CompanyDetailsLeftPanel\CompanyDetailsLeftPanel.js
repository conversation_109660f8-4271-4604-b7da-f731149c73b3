/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import SuccessModal from "components/common/Modals/SuccessModal";
import CompanyConnectModal from "../Modals/CompanyConnectModal";
import TagsModal from "components/common/Modals/TagsModal";
import TagsAddedSuccessModal from "components/common/Modals/TagsAddedSuccessModal";

/**
 * Main component for displaying company details in left panel
 * Fetches company data based on URL parameter and displays profile card
 */
const CompanyDetailsLeftPanel = ({
  companyPanelReload = false,
  setcompanyPanelReload = () => {},
}) => {
  // Extract company ID from URL parameters
  const { id } = useParams();

  // Retrieve authentication token from local storage
  const token = localStorage.getItem("token");

  // State management for loading status and company data
  const [isLoading, setIsLoading] = useState(true);
  const [companyDetails, setCompanyDetails] = useState(null);

  /**
   * Fetches company details from API
   * Uses company ID from URL and auth token
   */
  const fetchCompanyDetails = async () => {
    if (!id) return;

    try {
      setIsLoading(true);

      // Construct API endpoint URL
      const endpoint = `${url.API_GET_COMPANY_DETAILS}/${id}?token=${token}&modulefrom=frontendleftpanel`;
      const requestUrl = url.API_BASE_URL + endpoint;

      // Execute API call
      const response = await getData(requestUrl);

      console.log("response of company details", response);

      // Update state with valid response data
      if (response?.status && response.data) {
        setCompanyDetails(response.data);
      }
    } catch (error) {
      console.error("Failed to fetch company details:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch company details when component mounts or ID changes
  useEffect(() => {
    fetchCompanyDetails();
  }, [id]);

  useEffect(() => {
    if (companyPanelReload) {
      fetchCompanyDetails();
      setcompanyPanelReload(false);
    }
  }, [companyPanelReload]);

  // Show loading skeleton while data is being fetched
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  // Handle case where no company data is found
  if (!companyDetails) {
    return (
      <div className="pnl_leftpnl">
        <div className="alert alert-warning">
          Company information not available
        </div>
      </div>
    );
  }

  // Render company profile card with fetched data
  return (
    <div className="pnl_leftpnl">
      <CompanyProfileCard company={companyDetails} />

      {/* --------- tags modal ----------- */}
      <TagsModal moduleName="companydetails" selectedId={id} />

      {/* --------- tags added success / greetings modal ------------- */}
      <TagsAddedSuccessModal afterTagModalClose={fetchCompanyDetails} />

      {/* ---- company connect modal ----- */}
      <CompanyConnectModal
        moderatorId={companyDetails?.moderator?._id}
        companyName={companyDetails?.name}
        companyid={id}
      />

      {/* ---- success modal ----- */}
      <SuccessModal />
    </div>
  );
};

// ===================================================================
// Sub-components for different UI states and sections
// ===================================================================

// ===================================================================
// Actual Data Display Components
// ===================================================================

/**
 * Company Profile Card Component
 * Displays actual company information after data is loaded
 */
const CompanyProfileCard = ({ company }) => (
  <div className="white_shadow_bx company_leftusr">
    <div className="expertholder_outer">
      {/* Company logo display */}
      <div className="company_pic">
        <img
          width={64}
          height={64}
          className="rounded-circle"
          src={company.logo ? url.SERVER_URL + company.logo.path : ""}
          alt={`${company.name} logo`}
        />
      </div>

      <div className="experts_holdertext">
        {/* Company name */}
        <h3>{company.name}</h3>

        {/* Company rating display */}
        <div className="jb_rating star_color">
          <i className="material-icons-outlined ornge_icon">star</i>
          <span className="rating">
            {company.totalratings} <em>({company.totalreviews} reviews)</em>
          </span>
        </div>

        {/* Company location information */}
        <div className="details_tags expert_details">
          <div className="address">
            <i className="material-icons-outlined">place</i>
            {/* Display city and country, omitting empty values */}
            <span>
              {[company.city, company.country].filter(Boolean).join(", ")}
            </span>
          </div>
        </div>

        {/* Action buttons (Connect and Options) */}
        <ActionButtons />

        {/* Company tags display */}
        <CompanyTags tags={company.companytags} />
      </div>
    </div>
  </div>
);

/**
 * Action Buttons Component
 * Contains interactive buttons for user actions
 */
const ActionButtons = () => (
  <div className="feedback_jointbtnOuter company_btns">
    {/* Connect button - triggers modal */}
    <div className="details_apply">
      <Link
        to="#"
        className="btn outline"
        data-bs-target="#company_connect"
        data-bs-toggle="modal"
      >
        Connect
      </Link>
    </div>

    {/* Options button - triggers tags modal */}
    <div className="job_sharebtn">
      <Link to="#" data-bs-target="#tags_modal" data-bs-toggle="modal">
        <span className="material-icons">more</span>
      </Link>
    </div>
  </div>
);

/**
 * Company Tags Component
 * Displays list of tags associated with the company
 */
const CompanyTags = ({ tags }) => (
  <div className="feedback_jointbtnOuter">
    <div className="tags_links mt-4 mb-2">
      <ul className="list_stye_none d-flex flex-wrap gap-2">
        {tags?.map((tag, i) => (
          <li key={i}>
            <Link to="#" className="d-flex align-items-center gap-2">
              {/* Individual company tag */}
              <span className="badge text-dark">{tag.title}</span>
            </Link>
          </li>
        ))}
      </ul>
    </div>
  </div>
);

/**
 * Loading Skeleton Component
 * Shows placeholder UI while data is loading
 */
const LoadingSkeleton = () => (
  <div className="white_shadow_bx company_leftusr">
    <div className="expertholder_outer">
      {/* Company logo placeholder */}
      <div className="company_pic">
        <div
          className="rounded-circle placeholder bg-secondary"
          style={{ width: 64, height: 64 }}
        />
      </div>

      <div className="experts_holdertext">
        {/* Company name placeholder */}
        <h3 className="placeholder-glow d-flex justify-content-center mt-2">
          <span className="placeholder col-6" />
        </h3>

        {/* Rating stars placeholder */}
        <RatingSkeleton />

        {/* Location info placeholder */}
        <LocationSkeleton />

        {/* Action buttons placeholder */}
        <ActionButtonsSkeleton />

        {/* Tags section placeholder */}
        <TagsSkeleton />
      </div>
    </div>
  </div>
);

/**
 * Rating Skeleton Component
 * Placeholder for rating information
 */
const RatingSkeleton = () => (
  <div className="jb_rating star_color">
    <i className="material-icons-outlined ornge_icon placeholder bg-secondary rounded" />
    <span className="rating placeholder-glow">
      <span className="placeholder col-3" />
      <em>
        <span className="placeholder col-4" />
      </em>
    </span>
  </div>
);

/**
 * Location Skeleton Component
 * Placeholder for location information
 */
const LocationSkeleton = () => (
  <div className="details_tags expert_details">
    <div className="address">
      <i className="material-icons-outlined placeholder bg-secondary rounded" />
      <span className="placeholder-glow">
        <span className="placeholder col-8" />
      </span>
    </div>
  </div>
);

/**
 * Action Buttons Skeleton Component
 * Placeholder for action buttons
 */
const ActionButtonsSkeleton = () => (
  <div className="feedback_jointbtnOuter company_btns">
    <div className="details_apply">
      <div className="btn outline placeholder-glow">
        <span className="placeholder col-12" />
      </div>
    </div>
    <div className="job_sharebtn">
      <div
        className="placeholder bg-secondary rounded-circle"
        style={{ width: 40, height: 40 }}
      >
        <span className="material-icons placeholder bg-secondary rounded" />
      </div>
    </div>
  </div>
);

/**
 * Tags Skeleton Component
 * Placeholder for company tags section
 */
const TagsSkeleton = () => (
  <div className="feedback_jointbtnOuter">
    <div className="tags_links mt-4 mb-2">
      <ul className="list_stye_none d-flex flex-wrap gap-2">
        {/* Generate 4 tag placeholders */}
        {[...Array(4)].map((_, i) => (
          <li key={i}>
            <div className="d-flex align-items-center gap-2 placeholder-glow">
              <span
                className="placeholder col-6 rounded-pill"
                style={{ padding: "6px 12px", minWidth: 60 }}
              />
            </div>
          </li>
        ))}
      </ul>
    </div>
  </div>
);

export default CompanyDetailsLeftPanel;
