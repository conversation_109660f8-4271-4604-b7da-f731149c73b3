/* eslint-disable */
import AuthHeader from "components/common/AuthHeader";
import React, { useContext, useEffect } from "react";
//import context
import { GlobalProvider } from "context/GlobalContext";
import FriendRequestListBody from "components/FriendRequestListComponents/FriendRequestListBody/FriendRequestListBody";

const FriendRequestList = () => {
  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ---- friends requests section ---------- */}
      <FriendRequestListBody />
    </>
  );
};

export default FriendRequestList;
