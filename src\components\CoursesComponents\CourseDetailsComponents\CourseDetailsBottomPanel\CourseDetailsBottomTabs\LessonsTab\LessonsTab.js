/* eslint-disable */
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData, postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import LessonLeftPanel from "./Panels/LessonLeftPanel";
import LessonRighPanel from "./Panels/LessonRighPanel";
import { assetImages } from "constants";

const LessonsTab = ({
  activeTab = "",
  courseLessonTabReload = false,
  setCourseLessonTabReload = () => {},
}) => {
  const glowCount = [1, 2, 3, 4];
  const token = localStorage.getItem("token");

  const params = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [lessonList, setlessonList] = useState([]);
  const [courseAutoApproval, setCourseAutoApproval] = useState(false);
  const [userCourseRole, setUserCourseRole] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isApproved, setisApproved] = useState(false);

  const [selectedLessonId, setSelectedLessonId] = useState(null);

  //function for get all lessons
  const getCourseWithLessons = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_GET_COURSE_DETAILS_WITH_LESSONS +
        `/${params.id}?token=${token}`;

      const response = await getData(requestURL);

      setIsLoading(false);

      console.log(response);

      if (response.status) {
        setCourseAutoApproval(response.data.autoapproval);
        setUserCourseRole(response.data.usercourserole);
        setisApproved(response.data.isapproved);
        setIsSubscribed(response.data.issubscribed);
        setlessonList(response.data.courselessons);
        if (response.data.courselessons.length > 0) {
          setSelectedLessonId(response.data.courselessons[0]?._id);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const nextLessonHandler = (lessonCompleted = false) => {
    if (lessonList.length > 0) {
      const getCurrentIndexOfLesson = lessonList.findIndex(
        (item) => item._id === selectedLessonId
      );

      if (getCurrentIndexOfLesson + 1 === lessonList.length) {
        setSelectedLessonId(lessonList[0]?._id);
      } else {
        setSelectedLessonId(lessonList[getCurrentIndexOfLesson + 1]?._id);
      }

      if (lessonCompleted === true) {
        const updatedLessons = [...lessonList];
        updatedLessons[getCurrentIndexOfLesson].statusstring = "1";
        updatedLessons[getCurrentIndexOfLesson].completestatus = "Completed";
        setlessonList(updatedLessons);
      }
    }
  };

  useEffect(() => {
    if (courseLessonTabReload && courseAutoApproval) {
      getCourseWithLessons();
      setCourseLessonTabReload(false);
    }
  }, [courseLessonTabReload, courseAutoApproval]);

  useEffect(() => {
    if (params.id && activeTab === "lessons") {
      getCourseWithLessons();
    }
  }, [params.id, activeTab]);

  if (isLoading) {
    return (
      <div className="lessons_part_outer">
        <div className="lessons_leftpnl">
          <div className="lesson_rating_outer">
            {/* Placeholder for 3 lessons */}
            {glowCount.map((_, index) => (
              <div
                className="lesson_rating_row placeholder-glow"
                key={index}
                style={{
                  border: "1px solid #e0e0e0",
                  padding: "1rem",
                  borderRadius: "0.5rem",
                  marginBottom: "1rem",
                }}
              >
                {/* Top Row: Duration & Member */}
                <div className="lesson_statusOuter d-flex justify-content-between">
                  <div className="lesson_time">
                    <ul className="list_stye_none mb-2">
                      <li className="placeholder col-4"></li>
                      <li className="placeholder col-6"></li>
                    </ul>
                  </div>

                  {/* Status (Optional) */}
                  <div className="lesson_status d-flex align-items-center gap-1">
                    <span
                      className="placeholder"
                      style={{ width: "20px", height: "20px" }}
                    ></span>
                    <span
                      className="placeholder col-4"
                      style={{ width: "20px", height: "20px" }}
                    ></span>
                  </div>
                </div>

                {/* Lesson Title */}
                <div className="lesson_hdng mb-2">
                  <h3 className="mb-0">
                    <span className="placeholder col-7"></span>
                  </h3>
                </div>

                {/* Chapter Name */}
                <div className="lesson_time rating_status">
                  <ul className="list_stye_none mb-0">
                    <li className="placeholder col-5"></li>
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="lessons_rightpnl">
          <div className="course_detailspnl_innr">
            <div className="progress_courseOuter">
              <div className="back_progressOuter">
                <div className="page_back">
                  <span className="back_course placeholder-glow d-inline-flex align-items-center">
                    <span
                      className="placeholder rounded-circle"
                      style={{ width: "1.5rem", height: "1.5rem" }}
                    ></span>
                    <span className="ms-2 placeholder col-3"></span>
                  </span>
                </div>
                <div className="progress_text">
                  <h5 className="placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </h5>
                </div>
              </div>
              <div className="invite_progressouter">
                <span
                  className="invite_progress_bar blue_bar"
                  style={{ width: "32%" }}
                ></span>
              </div>
            </div>

            <div className="white_shadow_bx">
              <div className="lessons_tablink tab_links people_tablinks mb-3">
                <ul className="nav nav-tabs list_stye_none">
                  <li className="nav-item">
                    <span
                      className="nav-link active placeholder col-3"
                      style={{ width: "80px" }}
                    ></span>
                  </li>
                  <li className="nav-item">
                    <span
                      className="nav-link placeholder-glow col-3 "
                      style={{ width: "80px" }}
                    ></span>
                  </li>
                  <li className="nav-item">
                    <span
                      className="nav-link placeholder-glow col-4"
                      style={{ width: "80px" }}
                    ></span>
                  </li>
                </ul>
              </div>

              <div className="tab-content">
                <div className="tab-pane fade active show" id="content">
                  {/* --- Content Tab Placeholder --- */}
                  <div className="content_pnl lesson_pnl">
                    <div className="lesson_detailsHdng placeholder-glow">
                      <h2 className="placeholder col-6"></h2>
                    </div>

                    <div className="jb_rating mb-3 placeholder-glow d-flex align-items-center gap-2 col-2">
                      <span
                        className="placeholder rounded-circle"
                        style={{ width: "1.2rem", height: "1.2rem" }}
                      ></span>
                      <span className="placeholder col-9"></span>
                    </div>

                    <div className="course_contentOuter">
                      <div className="course_contentBx">
                        <div className="contnt_hdng placeholder-glow">
                          <h4 className="placeholder col-3"></h4>
                        </div>
                        <div className="results_para placeholder-glow">
                          <p className="placeholder col-12 mb-2"></p>
                          <p className="placeholder col-10 mb-2"></p>
                          <p className="placeholder col-8 mb-2"></p>
                        </div>
                      </div>
                    </div>

                    <div className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3 mb-3 placeholder-glow">
                      <p className="fw-medium placeholder col-6 mb-0"></p>
                      <span
                        className="placeholder rounded-circle"
                        style={{ width: "1.5rem", height: "1.5rem" }}
                      ></span>
                    </div>

                    <div className="course_vdopnl placeholder-glow mb-3">
                      <div
                        className="placeholder w-100"
                        style={{ height: "100%" }}
                      ></div>
                    </div>

                    <div className="lesson_btn mt-4">
                      <button className="btn disabled placeholder col-4"></button>
                    </div>
                  </div>
                </div>

                {/* Dummy tabs (Task, Question) can have similar placeholder blocks if needed */}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } else {
    if (userCourseRole !== "" || (isSubscribed && isApproved)) {
      if (lessonList.length > 0) {
        return (
          <div className="lessons_part_outer">
            <LessonLeftPanel
              lessonList={lessonList}
              userCourseRole={userCourseRole}
              selectedLessonId={selectedLessonId}
              setSelectedLessonId={setSelectedLessonId}
            />
            <LessonRighPanel
              selectedLessonId={selectedLessonId}
              nextLessonHandler={nextLessonHandler}
              userCourseRole={userCourseRole}
            />
          </div>
        );
      } else {
        return (
          <div className="d-flex align-items-center justify-content-center">
            <div className="pnl_mdlpnl">
              <div className="empty_access text-center">
                <div className="empty_pic mb-4">
                  {" "}
                  <img src={assetImages.emptyVector} alt="" />
                </div>
                <div className="empty_text">
                  <p className="fs-lg text-gray fw-semibold mb-4">
                    Sorry....! Lessons are not available for this course
                  </p>
                </div>
              </div>
            </div>
          </div>
        );
      }
    } else {
      return (
        <div className="d-flex align-items-center justify-content-center">
          <div className="pnl_mdlpnl">
            <div className="empty_access text-center">
              <div className="empty_pic mb-4">
                {" "}
                <img src={assetImages.emptyVector} alt="" />
              </div>
              <div className="empty_text">
                <p className="fs-lg text-gray fw-semibold mb-4">
                  Sorry....! You don't have privilege to see this content
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }
  }
};

export default LessonsTab;
