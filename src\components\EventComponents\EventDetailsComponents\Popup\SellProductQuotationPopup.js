/* eslint-disable */
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import { makeStripePayment } from "helper/StripePaymentHelper/StripePaymentHelper";

const SellProductQuotationPopup = ({
  eventid,
  sellprice,
  galleryImages,
  endDate,
  onClose,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [eventProducts, setEventProducts] = useState([]);
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quotationId, setQuotationId] = useState("");

  const [quantity, setQuantity] = useState(1);
  const [price, setPrice] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentId, setPaymentId] = useState("");
  const [isEditing, setIsEditing] = useState(false);

  const [sellQuotations, setSellQuotations] = useState([]);
  const [heighestBid, setHeighestBid] = useState(0);
  const [endDays, setEndDays] = useState(0);
  const [endHours, setEndHours] = useState(0);
  const [endMinutes, setEndMinutes] = useState(0);

  // Function to get event products
  const getEventProducts = async () => {
    resetStates();
    try {
      setIsLoading(true);
      const requestURL =
        url.API_BASE_URL +
        url.API_GET_EVENT_PRODUCTS +
        `/${eventid}` +
        `?token=${token}`;

      const response = await getData(requestURL);
      setIsLoading(false);

      if (response.status) {
        setEventProducts(response.data);
        if (response.data.length > 0) {
          setProducts(response.data.map((item) => item.product));
          setSelectedProduct(response.data[0].product); // Set the first product as selected by default
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  const fetchSellQuotations = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL + url.API_GET_ALL_QUOTATIONS + `?token=${token}`;

      requestURL += `&eventid=${eventid}`;
      requestURL += `&type=sell`; // Set type to 'sell'

      const response = await getData(requestURL);

      //   console.log("fetchSellQuotations response: SELL PRODUCT", response);

      setIsLoading(false);
      if (response.status) {
        setSellQuotations(response.data);

        // sort all quotations by totalprice in descending order
        const sortedQuotations = response.data.sort(
          (a, b) => b.totalprice - a.totalprice
        );

        // get the heighest totalprice from the sorted quotations
        const highestQuotation =
          sortedQuotations.length > 0 ? sortedQuotations[0] : null;
        if (highestQuotation) {
          setHeighestBid(highestQuotation.totalprice);
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // get saved quotation details for this eventid, productid and customerid
  const getSavedQuotation = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL + url.API_GET_ALL_QUOTATIONS + `?token=${token}`;

      requestURL += `&eventid=${eventid}`;
      requestURL += `&productid=${selectedProduct?._id}`;
      requestURL += `&providerid=${userInfo._id}`;
      requestURL += `&type=sell`; // Type is set to 'sell'

      console.log("getSavedQuotation SELL requestURL:", requestURL);

      const response = await getData(requestURL);

      console.log("getSavedQuotation SELL response:", response);

      setIsLoading(false);
      if (response.status) {
        if (response.data && response.data.length > 0) {
          const savedQuotation = response.data[0];
          setQuotationId(savedQuotation._id);
          setQuantity(savedQuotation.units || 1);
          setPrice(savedQuotation.totalprice || 0);

          if (savedQuotation.payment && savedQuotation.payment !== null) {
            setPaymentId(savedQuotation.payment);
          }
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // Function to save quotation
  const saveQuotation = async () => {
    try {
      setIsLoading(true);

      const quotationData = {
        eventid: eventid,
        productid: selectedProduct?._id,
        units: quantity,
        totalprice: price,
        type: "sell", // Type is set to 'sell'
        processingfee: sellprice,
      };

      if (quotationId) {
        const requestURL =
          url.API_BASE_URL +
          url.API_UPDATE_QUOTATION +
          `/${quotationId}?token=${token}`;

        const response = await putData(requestURL, quotationData);

        console.log("saveQuotation response:", response);
        setIsLoading(false);

        if (response.status) {
          setQuotationId(response.data._id);
          setIsEditing(false);
        }
      } else {
        const requestURL =
          url.API_BASE_URL + url.API_SAVE_QUOTATION + `?token=${token}`;

        const response = await postData(requestURL, quotationData);

        console.log("saveQuotation response:", response);
        setIsLoading(false);

        if (response.status) {
          setQuotationId(response.data._id);
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // Function to generate invoice
  const generateInvoice = async () => {
    try {
      setIsLoading(true);
      const requestURL =
        url.API_BASE_URL +
        url.API_REQUEST_QUOTATION_INVOICE +
        `?token=${token}`;

      const invoiceData = {
        eventid: eventid,
        quotationid: quotationId,
        amount: sellprice,
        duedate: endDate,
      };

      console.log("generateInvoice invoiceData:", requestURL, invoiceData);

      const response = await postData(requestURL, invoiceData);

      console.log("generateInvoice response:", response);

      if (response.status) {
        const invoiceData = response.data;

        const invoiceId = invoiceData._id;

        const redirectUrl =
          url.FRONTEND_BASE_URL + "/events/details/" + eventid;

        if (invoiceId) {
          await makeStripePayment(invoiceId, token, userInfo._id, redirectUrl);
        }

        setIsLoading(false);
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // reset all states when the popup is closed
  const resetStates = () => {
    setEventProducts([]);
    setProducts([]);
    setSelectedProduct(null);
    setQuotationId("");
    setQuantity(1);
    setPrice(0);
    setIsLoading(false);
    setPaymentId("");
    setIsEditing(false);
  };

  // Fetch event products on component mount
  useEffect(() => {
    getEventProducts();
    fetchSellQuotations();
  }, [eventid]);

  // when selected product is changed, fetch saved quotation details
  useEffect(() => {
    if (selectedProduct) {
      getSavedQuotation();
    }
  }, [selectedProduct]);

  // set remaining days, hours and minutes based on endDate
  useEffect(() => {
    if (endDate) {
      const now = new Date();
      const end = new Date(endDate);
      const diff = end - now;

      if (diff > 0) {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        setEndDays(days);
        setEndHours(hours);
        setEndMinutes(minutes);
      }
    }
  }, [endDate]);

  return (
    <div
      className="offcanvas offcanvas-end custom-offcanvas-width-90"
      tabIndex="-1"
      id="offcanvasSellQuotation"
      aria-labelledby="offcanvasSellQuotationLabel"
      aria-modal="true"
      role="dialog"
    >
      <div className="offcanvas-header p-4 shadow-sm">
        <div className="d-flex gap-2 medicine_wrap">
          <div className="sell_pic rounded-10 overflow-hidden">
            <img src="images/medicine-pic.jpg" alt="" />
          </div>
          <div className="sell_info">
            <h3 className="fw-semibold mb-2 text-black fs-xl">
              Sell Quotation
            </h3>
          </div>
        </div>
        <div className="d-flex align-items-center gap-2 add_watch_wrap">
          <button
            type="button"
            className="btn-close text-reset bg-light p-3 rounded-circle"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
            onClick={onClose}
          ></button>
        </div>
      </div>

      {selectedProduct && (
        <div className="offcanvas-body p-4">
          <div className="sell_canvas_wrapper">
            <div className="row gy-3">
              {/* product info section */}
              <div className="col-lg-6">
                {/* Product Details */}
                <div className="selling_pnl_item border mb-3 rounded-10 overflow-hidden">
                  <div className="p-3 sell_product_details">
                    <h4 className="fs-lg fw-semibold mb-3 text-black">
                      Details Of the Product
                    </h4>
                    <div className="row gy-2">
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Product Name
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.name}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Category
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.category?.name}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Subcategory
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.subcategory?.name}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Brand
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.brand?.name}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Model
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.model}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Condition
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.condition}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Quantity Available
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.quantity}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="form-group">
                          <label className="text-black mb-2 fs-sm fw-semibold d-block">
                            Location
                          </label>
                          <p className="text-black fw-semibold fs-md">
                            {selectedProduct?.location}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Documentos Section */}
                <div className="border-top p-3 sell_product_docs">
                  <h4 className="fs-lg fw-semibold mb-3 text-black">
                    Documentos
                  </h4>
                  <div className="upload_area row">
                    {galleryImages && galleryImages.length > 0 ? (
                      galleryImages.map((image, index) => (
                        <div className="col-md-4 col-6 mb-3" key={index}>
                          <div className="upload_item">
                            <img
                              src={url.SERVER_URL + image.path}
                              alt={`Document ${index + 1}`}
                              className="img-fluid rounded-10"
                            />
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted">No documents uploaded</p>
                    )}
                  </div>
                </div>
              </div>
              {/* Bidding Section */}
              <div className="col-lg-3">
                <div className="bids_rightpnl">
                  <div className="p-3 rounded-10 overflow-hidden bids_counter_item text-center border-dark-gray mb-3">
                    <h4 className="fs-lg fw-semibold mb-2 text-black">
                      Current Highest Bid
                    </h4>
                    <h3 className="text-success fw-semibold fs-xl mb-3">
                      {" "}
                      €{heighestBid}
                    </h3>
                    <h6 className="text-gray mb-3 fs-sm">Auction ending in</h6>
                    <div className="aution_bx_wrapper mb-3">
                      <ul className="d-flex justify-content-center gap-2 w-100 list_stye_none">
                        <li
                          className="p-3 text-center rounded-10 border w-100"
                          style={{ height: "65px" }}
                        >
                          <strong className="text-danger fw-semibold fs-xl mb-2">
                            {endDays}
                          </strong>
                          <p className="text-gray fs-md fw-regular">
                            {endDays > 1 ? "Days" : "Day"}
                          </p>
                        </li>
                        <li
                          className="p-3 text-center rounded-10 border w-100"
                          style={{ height: "65px" }}
                        >
                          <strong className="text-danger fw-semibold fs-xl mb-2">
                            {endHours}
                          </strong>
                          <p className="text-gray fs-md fw-regular">
                            {endHours > 1 ? "Hours" : "Hour"}
                          </p>
                        </li>
                        <li
                          className="p-3 text-center rounded-10 border w-100"
                          style={{ height: "65px" }}
                        >
                          <strong className="text-danger fw-semibold fs-xl mb-2">
                            {endMinutes}
                          </strong>
                          <p className="text-gray fs-md fw-regular">
                            {endMinutes > 1 ? "Min" : "Min"}
                          </p>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="shadow-sm rounded-8 overflow-hidden p-3">
                    <h4 className="fs-lg fw-semibold mb-3 text-black">
                      Live Bidding
                    </h4>

                    <div className="table-responsive table-wrapper">
                      <table className="table align-middle font-sm text-nowrap border-0 mb-0">
                        <thead>
                          <tr>
                            <th
                              className="font-sm fw-semibold border-0 text-gray p-3"
                              style={{ backgroundColor: "#E6F3FF" }}
                            >
                              <div className="d-flex align-items-center gap-3">
                                <span>Id</span>
                              </div>
                            </th>
                            <th
                              className="font-sm fw-semibold border-0 text-gray p-3"
                              style={{ backgroundColor: "#E6F3FF" }}
                            >
                              <div className="d-flex align-items-center gap-3">
                                <span>Amount</span>
                              </div>
                            </th>
                            <th
                              className="font-sm fw-semibold border-0 text-gray p-3"
                              style={{ backgroundColor: "#E6F3FF" }}
                            >
                              <div className="d-flex align-items-center gap-3">
                                <span>Date</span>
                              </div>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {sellQuotations.map((sellQuotation, index) => {
                            const formattedDate = new Date(
                              sellQuotation.createdAt
                            ).toLocaleDateString("en-GB");
                            return (
                              <tr key={index}>
                                <td className="border-bottom-0 text-secondary font-sm bg-gray p-3 fw-semibold text-black">
                                  {sellQuotation.customid
                                    ? sellQuotation.customid
                                    : index + 1}
                                </td>
                                <td className="border-bottom-0 text-secondary font-sm bg-gray p-3 fw-semibold text-black">
                                  €{sellQuotation.totalprice}
                                </td>
                                <td className="border-bottom-0 text-secondary font-sm bg-gray p-3 fw-semibold text-black">
                                  {formattedDate}
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              {/* Quotation Section */}
              <div className="col-lg-3">
                <div className="bids_rightpnl">
                  {/* Quotation Deposit Section */}
                  <div className="live_bidding border rounded-10 p-3 mb-3">
                    <h4 className="fs-lg fw-semibold mb-3 text-black">
                      Quotation Deposit
                    </h4>
                    <div className="bidding_item d-flex align-items-center gap-3 mb-3 justify-content-between">
                      <div className="bid_info">
                        <h5 className="text-black mb-1 fs-md">Amount to pay</h5>
                        <p className="text-success fw-semibold mb-0 fs-lg">
                          €{sellprice ? sellprice.toFixed(2) : "0.00"}
                        </p>
                      </div>
                      <div className="bid_timer">
                        <h5 className="text-black mb-1 fs-md">End Date</h5>
                        <p className="text-danger fw-normal mb-0 fs-md">
                          {endDate
                            ? new Date(endDate).toLocaleDateString("en-GB")
                            : "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Quotation Section */}
                  <div className="border rounded-10 p-3">
                    <div className="d-flex justify-content-between align-items-center">
                      <h4 className="fs-lg fw-semibold mb-3 text-black">
                        {quotationId ? "Quotation Details" : "Your Quotation"}
                      </h4>
                      {quotationId && !paymentId && (
                        <button
                          className="btn btn-sm btn-outline-secondary"
                          onClick={() => setIsEditing(true)} // Enable editing mode
                        >
                          <i className="material-icons">edit</i>
                        </button>
                      )}
                    </div>

                    {!quotationId ? (
                      <>
                        <div className="row gy-3">
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Quantity
                              </label>
                              <input
                                type="number"
                                className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                value={quantity}
                                onChange={(e) => setQuantity(e.target.value)}
                                min="1"
                                step="1"
                              />
                            </div>
                          </div>
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Max Price
                              </label>
                              <div className="input-group">
                                <span className="input-group-text">€</span>
                                <input
                                  type="number"
                                  className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                  value={price}
                                  onChange={(e) => setPrice(e.target.value)}
                                  min="0"
                                  step="1"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="text-center mt-3">
                          <button
                            className="btn btn-primary rounded-10 px-4 py-2 fs-md fw-semibold"
                            onClick={saveQuotation}
                            disabled={isLoading || price <= 1} // Disable button if price <= 1 or loading
                          >
                            {isLoading ? "Submitting..." : "Confirm & Submit"}
                          </button>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="row gy-3">
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Quantity
                              </label>
                              {isEditing ? (
                                <input
                                  type="number"
                                  className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                  value={quantity}
                                  onChange={(e) => setQuantity(e.target.value)}
                                  min="1"
                                  step="1"
                                />
                              ) : (
                                <p className="text-black fw-semibold fs-md">
                                  {quantity}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Max Price
                              </label>
                              {isEditing ? (
                                <div className="input-group">
                                  <span className="input-group-text">€</span>
                                  <input
                                    type="number"
                                    className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                    value={price}
                                    onChange={(e) => setPrice(e.target.value)}
                                    min="0"
                                    step="1"
                                  />
                                </div>
                              ) : (
                                <p className="text-black fw-semibold fs-md">
                                  €{Number(price).toFixed(2)}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="row gy-3">
                          <div className="col-md-12">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Payment Status
                              </label>
                              <p
                                className={`fw-semibold fs-md ${
                                  paymentId ? "text-success" : "text-danger"
                                }`}
                              >
                                {paymentId ? "Paid" : "Pending"}
                              </p>
                            </div>
                          </div>
                        </div>
                        {isEditing && (
                          <div className="text-center mt-3">
                            <button
                              className="btn btn-primary rounded-10 px-4 py-2 fs-md fw-semibold"
                              onClick={saveQuotation}
                              disabled={isLoading || price <= 1} // Disable button if price <= 1 or loading
                            >
                              {isLoading ? "Saving..." : "Save Changes"}
                            </button>
                          </div>
                        )}
                        {!paymentId && !isEditing && (
                          <div className="text-center mt-3">
                            <button
                              className="btn btn-primary rounded-10 px-4 py-2 fs-md fw-semibold"
                              onClick={generateInvoice}
                              disabled={isLoading}
                            >
                              {isLoading ? "Processing..." : "Make Payment"}
                            </button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SellProductQuotationPopup;
