/* eslint-disable */
import { useState, useEffect } from "react";

import { Link, useParams } from "react-router-dom";

//**------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData, postData, putData, uploadMultipleFile } from "utils/Gateway";
import { Height } from "@mui/icons-material";

const SaveOpportunity = ({
  opportunity,
  receiverId,
  challengeId,
  challengeResponseId,
  setIsOpportunitySaved,
  showOpportunity,
  setShowOpportunity,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [opportunityId, setOpportunityId] = useState(null);

  const [title, setTitle] = useState("");
  const [validateTitle, setValidateTitle] = useState(true);

  const [description, setDescription] = useState("");

  const [price, setPrice] = useState(0);
  const [validatePrice, setValidatePrice] = useState(true);

  const [promisePercent, setPromisePercent] = useState(0);

  const [items, setItems] = useState([
    { description: "", quantity: 1, price: 0, tax: 0, amount: 0 },
  ]);
  const [validateItem, setValidateItem] = useState(true);

  const [isSaving, setIsSaving] = useState(false);

  const handleItemChange = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;

    // Update total amount when quantity, price, or tax changes
    if (field === "quantity" || field === "price" || field === "tax") {
      updateTotalAmount(newItems, index);
    }

    setItems(newItems);

    updateTotalPrice(newItems); // Update price when any item's total amount changes
  };

  const addItem = () => {
    setItems([
      ...items,
      { description: "", quantity: 1, price: 0, tax: 0, amount: 0 },
    ]);
  };

  const removeItem = (index) => {
    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);

    updateTotalPrice(newItems); // Update price when any item is removed
  };

  // Handle keypress events on item fields
  const handleItemKeyDown = (e, index) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addItem();
      // Focus on the description field of the new item after rendering
      setTimeout(() => {
        const newItemInputs = document.querySelectorAll(`[data-item="${items.length}"] input`);
        if (newItemInputs.length > 0) {
          newItemInputs[0].focus();
        }
      }, 100);
    }
  };

  //* save the invoice record */
  const saveInvoice = async () => {
    if (validationHandler()) {
      console.log("is validated");

      setIsSaving(true);

      let invoiceData = {
        challenge: challengeId || null,
        clientid: receiverId || null,
        title,
        description,
        amount: price,
        promisepercent: promisePercent,
        payableamount: price,
        currency: "euro",
        items: items, // invoice items
        status: "1",
        notes: [],
        typeslug: "challenge",
        modulename: "",
      };

      console.log("invoice ===>", invoiceData);

      let requestUrl = url.API_BASE_URL;

      let response = {};

      try {
        if (opportunityId) {
          requestUrl +=
            url.API_UPDATE_INVOICE + `/${opportunityId}?token=${token}`;

          console.log("requesturl UPDATE", requestUrl);

          response = await putData(requestUrl, invoiceData);

          setIsSaving(false);
        } else {
          requestUrl += url.API_CREATE_INVOICE + `?token=${token}`;

          console.log("requesturl ADD", requestUrl);

          response = await postData(requestUrl, invoiceData);

          setIsSaving(false);
        }

        console.log("invoice response", response);

        if (response.status) {
          let offCanvasPopup = document.querySelector(
            "#saveOpportunityOffcanvas"
          );
          let offCanvas = bootstrap.Offcanvas.getInstance(offCanvasPopup);
          offCanvas.hide();
          resetHandler();
        }
      } catch (error) {
        console.log(error);
      }
    } else {
      console.log("is not validated");
    }

    //setShowAlert(true);
  };

  useEffect(() => {
    resetHandler();
  }, [challengeResponseId, receiverId]);

  // reset all states
  const resetHandler = () => {
    setTitle("");
    setDescription("");
    setPrice(0);
    setPromisePercent("");
    setItems([{ description: "", quantity: 1, price: 0, tax: 0, amount: 0 }]); // Always have one default item
    setValidateTitle(true);
    setValidatePrice(true);
    setValidateItem(true);
  };

  const updateTotalAmount = (newItems, index) => {
    const { quantity, price, tax } = newItems[index];

    const subtotal = (quantity || 0) * (price || 0); // Calculate subtotal (quantity * price)
    const taxAmount = subtotal * ((tax || 0) / 100); // Calculate tax
    const total = subtotal + taxAmount; // Total amount after tax

    newItems[index].amount = total; // Update the total amount
  };

  const updateTotalPrice = (newItems) => {
    const totalPrice = newItems.reduce(
      (sum, item) => sum + (item.amount || 0),
      0
    );
    setPrice(totalPrice); // Update the price field in the form
  };

  // check if title and price is empty or null setIsValidate false
  // also atlest there should be 1 item with description
  const validationHandler = () => {
    let isValidate = true;

    if (title === "" || title === null) {
      setValidateTitle(false);

      isValidate = false;

      console.log("title is empty");
    }

    if (price === "" || price === null) {
      setValidatePrice(false);

      isValidate = false;
    }

    // check if there is atleast 1 item and item description is not empty
    if (items.length === 0) {
      setValidateItem(false);
      isValidate = false;
    } else if (items[0].description == "") {
      setValidateItem(false);
      isValidate = false;
    }

    return isValidate;
  };

  return (
    <div
      className="offcanvas offcanvas-end offcanvas-xl"
      tabIndex="-1"
      id="saveOpportunityOffcanvas"
    >
      <div className="offcanvas-header border-bottom">
        <h3 className="fw-bold">Add Opportunity</h3>
        <button
          type="button"
          className="btn-close"
          data-bs-dismiss="offcanvas"
          onClick={() => {
            setShowOpportunity(false);
          }}
        ></button>
      </div>
      <div className="offcanvas-body py-4">
        <form className="opportunity-form">
          <div className="invoicehead">
            <div className="mb-4">
              <label htmlFor="title" className="form-label fw-bold">
                Title <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                className={`form-control ${validateTitle ? '' : 'is-invalid'}`}
                id="title"
                value={title}
                onChange={(e) => {
                  setTitle(e.target.value);
                  setValidateTitle(true);
                }}
                placeholder="Enter opportunity title"
              />

              {validateTitle == false && (
                <div className="invalid-feedback">Title is required</div>
              )}
            </div>
            <div className="mb-4">
              <label htmlFor="description" className="form-label fw-bold">
                Description
              </label>
              <textarea
                className="form-control"
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter opportunity description"
                rows="3"
              ></textarea>
            </div>
            <div className="row mb-4">
              <div className="col">
                <label htmlFor="price" className="form-label fw-bold">
                  Price <span className="text-danger">*</span>
                </label>
                <div className="input-group">
                  <span className="input-group-text">€</span>
                  <input
                    type="number"
                    className={`form-control ${validatePrice ? '' : 'is-invalid'}`}
                    id="price"
                    value={price}
                    onChange={(e) => {
                      setPrice(parseFloat(e.target.value) || 0);
                      setValidatePrice(true);
                    }}
                    placeholder="0.00"
                  />
                </div>
                {validatePrice == false && (
                  <div className="text-danger small mt-1">Please add a price</div>
                )}
              </div>
              <div className="col">
                <label htmlFor="promisePercent" className="form-label fw-bold">
                  Promise Percent
                </label>
                <div className="input-group">
                  <input
                    type="number"
                    className="form-control"
                    id="promisePercent"
                    value={promisePercent}
                    onChange={(e) => setPromisePercent(parseFloat(e.target.value) || 0)}
                    placeholder="0"
                  />
                  <span className="input-group-text">%</span>
                </div>
              </div>
            </div>
            <h5 className="mb-3 fw-bold">Items <span className="text-danger">*</span></h5>
            <div
              className="card p-4 shadow-sm mb-3"
              style={{ background: "#F7F9FC" }}
            >
              <div className="table-responsive">
                <table className="table table-borderless item-table">
                  <colgroup>
                    <col style={{width: "40%"}} /> {/* Description - wider column */}
                    <col style={{width: "12%"}} /> {/* Quantity - narrower column */}
                    <col style={{width: "15%"}} /> {/* Price - narrower column */}
                    <col style={{width: "12%"}} /> {/* Tax - narrower column */}
                    <col style={{width: "15%"}} /> {/* Amount - narrower column */}
                    <col style={{width: "6%"}} />  {/* Action - minimal width */}
                  </colgroup>
                  <thead>
                    <tr className="text-muted">
                      <th>Description</th>
                      <th>Quantity</th>
                      <th>Price</th>
                      <th>Tax %</th>
                      <th>Amount</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    {items.map((item, index) => (
                      <tr key={index} data-item={index} className="item-row">
                        <td>
                          <input
                            type="text"
                            className={`form-control ${validateItem ? '' : 'is-invalid'}`}
                            value={item.description}
                            placeholder="Enter product name or service description"
                            onChange={(e) =>
                              handleItemChange(index, "description", e.target.value)
                            }
                            onKeyDown={(e) => handleItemKeyDown(e, index)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={item.quantity}
                            min="1"
                            onChange={(e) =>
                              handleItemChange(
                                index,
                                "quantity",
                                parseFloat(e.target.value) || 0
                              )
                            }
                            onKeyDown={(e) => handleItemKeyDown(e, index)}
                          />
                        </td>
                        <td>
                          <div className="input-group input-group-sm">
                            <span className="input-group-text">€</span>
                            <input
                              type="number"
                              className="form-control"
                              value={item.price}
                              placeholder="0.00"
                              onChange={(e) =>
                                handleItemChange(
                                  index,
                                  "price",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              onKeyDown={(e) => handleItemKeyDown(e, index)}
                            />
                          </div>
                        </td>
                        <td>
                          <div className="input-group input-group-sm">
                            <input
                              type="number"
                              className="form-control"
                              value={item.tax}
                              placeholder="0"
                              onChange={(e) =>
                                handleItemChange(
                                  index,
                                  "tax",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              onKeyDown={(e) => handleItemKeyDown(e, index)}
                            />
                            <span className="input-group-text">%</span>
                          </div>
                        </td>
                        <td>
                          <div className="input-group input-group-sm">
                            <span className="input-group-text">€</span>
                            <input
                              type="number"
                              className="form-control"
                              value={item.amount}
                              readOnly
                            />
                          </div>
                        </td>
                        <td className="text-center">
                          {items.length > 1 && (
                            <button
                              type="button"
                              className="btn outline btn-sm btn-link text-danger"
                              onClick={() => removeItem(index)}
                            >
                              <i className="material-icons-outlined">delete</i>
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {validateItem === false && (
                <div className="text-danger mb-3">Please add at least one item with a description</div>
              )}

              <div>
                <button type="button" className="btn outline btn-sm" onClick={addItem}>
                  <i className="material-icons-outlined">add</i> Add Another Item
                </button>
                <small className="text-muted ms-3">Press Enter to add a new row</small>
              </div>
            </div>
          </div>
          <div className="invoicefooter d-flex justify-content-between mt-4 pt-3 border-top">
            <button type="button" className="btn btn-secondary" data-bs-dismiss="offcanvas">
              Cancel
            </button>
            <button type="button" className="btn btn-primary" onClick={saveInvoice} disabled={isSaving}>
              {isSaving ? <><i className="fas fa-spinner fa-spin me-2"></i>Saving...</> : "Save Opportunity"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SaveOpportunity;
