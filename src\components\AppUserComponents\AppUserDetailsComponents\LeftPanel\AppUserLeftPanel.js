/* eslint-disable */
import "react-multi-carousel/lib/styles.css";
import React, { useContext, useEffect, useState } from "react";
import { Link, useHistory, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData, putData, deleteData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

//import images
import { assetImages } from "constants";
import RequestCancelModal from "../Modals/RequestCancelModal";
import UnfriendSuccessModal from "../Modals/UnfriendSuccessModal";
import ResumeContactSuccessModal from "../Modals/ResumeContactSuccessModal";
import RecomandationModal from "../Modals/RecomandationModal";
import RecomnadSuccessModal from "../Modals/RecomnadSuccessModal";
import { getDateFormatInSpanish } from "helper/Common/CommonHelper";

const AppUserLeftPanel = ({
  leftPanelReload = false,
  setLeftPanelReload = () => {},
}) => {
  const { t } = useTranslation(); // translation marker

  const params = useParams();
  const history = useHistory();

  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [userImage, setUserImage] = useState("");
  const [userDetails, setUserDetails] = useState({});

  //function for get user details
  const getUserDetails = async () => {
    try {
      setIsLoading(true);
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USER_DETAILS +
        `/${params.id}` +
        `?token=${token}`;

      const response = await getData(requestUrl);

      setIsLoading(false);

      // console.log("response in app user left panel", response);

      if (response.status && response.data) {
        setUserDetails(response.data);
        setUserImage(response.data?.userimage);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for cancel request
  const cancelRequestHandler = async () => {
    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_REJECT_CONTACT_REQUEST +
        `?token=${token}&personid=${params.id}`;

      const response = await deleteData(requestURL);

      console.log(response);

      if (response.status) {
        //show register modal
        let showModal = new bootstrap.Modal(
          document.getElementById("gretings_cancel_mdl")
        );
        showModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for unfriend contact
  const unfriendHandler = async () => {
    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_UNFRIEND_CONTACT +
        `?token=${token}&personid=${params.id}`;

      const response = await putData(requestURL);

      console.log(response);

      if (response.status) {
        //show register modal
        let showModal = new bootstrap.Modal(
          document.getElementById("unfriend_success_mdl")
        );
        showModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for resume contact
  const resumeContactHandler = async () => {
    try {
      let requestURL =
        url.API_BASE_URL +
        url.API_RESUME_CONTACT +
        `?token=${token}&personid=${params.id}`;

      const response = await putData(requestURL);

      console.log(response);

      if (response.status) {
        //show register modal
        let showModal = new bootstrap.Modal(
          document.getElementById("resume_contact_success_mdl")
        );
        showModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //create chat
  const createContactChatHandler = async () => {
    if (params.id && params.id.toString() !== userInfo._id.toString()) {
      try {
        let requestURL =
          url.API_BASE_URL +
          url.API_SEARCH_CONTACT +
          `?token=${token}&member=${params.id}`;

        const response = await getData(requestURL);

        console.log(response);

        if (response.status) {
          const externalUrl = `/conversation/contact/${response.data._id}`;
          history.push(externalUrl);
        }
      } catch (error) {
        console.log(error.message);
      }
    } else {
    }
  };

  useEffect(() => {
    if (params.id) {
      getUserDetails();
    }
  }, [params.id]);

  useEffect(() => {
    if (leftPanelReload) {
      getUserDetails();
      setLeftPanelReload(false);
    }
  }, [leftPanelReload]);

  if (isLoading) {
    return (
      <div className="pnl_leftpnl">
        <span className="cross_btn material-icons-outlined placeholder-glow">
          <span className="placeholder col-1"></span>
        </span>
        <div className="white_shadow_bx people_leftusr placeholder-glow">
          <div className="expertholder_outer">
            <div className="experts_holderPic">
              <div
                className="rounded-circle placeholder"
                style={{ width: "120px", height: "120px" }}
              ></div>
            </div>

            <div className="experts_holdertext">
              <h3 className="d-flex flex-column gap-2 align-items-center">
                <span className="placeholder col-6"></span>
                <span className="placeholder col-4"></span>
              </h3>

              <div className="d-flex gap-2 justify-content-center mb-3">
                <div
                  className="rounded-90 py-2 px-3 placeholder"
                  style={{ height: "auto", width: "100px" }}
                ></div>
                <div
                  className="rounded-90 py-2 px-3 placeholder"
                  style={{ width: "100px" }}
                ></div>
              </div>

              <div className="details_tags expert_details mb-3">
                <div className="address">
                  <span className="placeholder col-8"></span>
                </div>
              </div>

              <div className="feedback_jointbtnOuter mb-4 d-flex gap-2 justify-content-center">
                <div className="placeholder col-3 py-2"></div>
                <div className="placeholder col-3 py-2"></div>
              </div>

              <div className="feedback_jointbtnOuter mb-4 d-flex gap-2 justify-content-center">
                <div className="details_apply">
                  <div
                    className="btn outline placeholder"
                    style={{ width: "40px", height: "40px" }}
                  ></div>
                </div>
                <div className="details_apply">
                  <div
                    className="btn outline placeholder"
                    style={{ width: "40px", height: "40px" }}
                  ></div>
                </div>
                <div className="details_apply">
                  <div
                    className="btn outline placeholder"
                    style={{ width: "40px", height: "40px" }}
                  ></div>
                </div>
              </div>

              <div className="feedback_jointbtnOuter flex-wrap mb-4 d-flex justify-content-center">
                <div className="placeholder col-3 py-2"></div>
              </div>

              <div className="connected_text mb-3">
                <div className="placeholder col-8 py-2"></div>
              </div>

              <div className="tags_links mt-4 mb-2">
                <ul className="list_stye_none d-flex flex-wrap gap-2 justify-content-center">
                  {[...Array(4)].map((_, index) => (
                    <li key={index}>
                      <div
                        className="placeholder col-3 py-1"
                        style={{ width: "60px" }}
                      ></div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pnl_leftpnl">
      <span className="cross_btn material-icons-outlined">close</span>
      <div className="white_shadow_bx people_leftusr">
        <div className="expertholder_outer">
          <div className="experts_holderPic">
            <img
              height="120"
              className="rounded-circle"
              src={
                userImage === ""
                  ? assetImages.defaultUser
                  : url.SERVER_URL + userImage
              }
              alt=""
            />
          </div>

          <div className="experts_holdertext">
            <h3>
              {userDetails.name} {userDetails.surname}
              <span className="mt-1">{userDetails.position}</span>
            </h3>

            <div className="d-flex gap-2 justify-content-center">
              <div
                className="jb_rating star_color py-2 px-3"
                style={{ height: "auto" }}
              >
                <i className="material-icons-outlined ornge_icon"> star </i>
                <span className="rating">
                  {userDetails.totalratings}{" "}
                  <em>({userDetails.totalreviews})</em>
                </span>
              </div>
              <div className="rounded-90 py-2 px-3 border-green bg-light-green text-green fs-sm fw-medium">
                € {userDetails.costperhour} / Hr
              </div>
            </div>

            <div className="details_tags expert_details">
              <div className="address">
                <i className="material-icons-outlined">place</i>
                {userDetails.city ||
                userDetails.state ||
                userDetails.country ? (
                  <span>
                    {userDetails.city && userDetails.city}
                    {userDetails.state &&
                      (userDetails.city ? ", " : "") + userDetails.state}
                    {userDetails.country &&
                      (userDetails.city || userDetails.state ? ", " : "") +
                        userDetails.country}
                  </span>
                ) : (
                  <span>-</span>
                )}
              </div>
            </div>

            <div className="feedback_jointbtnOuter mb-4">
              {userDetails.connectstatus ||
              userDetails?._id?.toString() === userInfo._id.toString() ? (
                <div className="feed_btn">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#recomanded_mdl"
                    className="btn gray_btn"
                  >
                    {t("Recomanded")}
                  </Link>
                </div>
              ) : null}

              {userDetails?._id?.toString() ===
              userInfo._id?.toString() ? null : userDetails.connectstatus ? (
                <div className="feed_btn">
                  <Link
                    to="#"
                    data-bs-toggle="modal"
                    data-bs-target="#feedback_mdl"
                    className="btn gray_btn"
                  >
                    {t("Give Review")}
                  </Link>
                </div>
              ) : null}
            </div>

            <div className="feedback_jointbtnOuter mb-4">
              {userDetails?._id?.toString() ===
              userInfo._id?.toString() ? null : userDetails.isconnect ? (
                <div className="details_apply dropdown">
                  <Link
                    to="#"
                    className="btn outline"
                    data-bs-toggle={
                      userDetails.connectstatus
                        ? "dropdown"
                        : userDetails.unfriendstatus === "0"
                        ? "dropdown"
                        : userDetails.resumecontactowner === "1" ||
                          userDetails.resumecontactmember === "1"
                        ? "dropdown"
                        : "#"
                    }
                  >
                    <i className="material-icons-outlined">group</i>
                  </Link>
                  <ul className="list_stye_none dropdown-menu">
                    {userDetails.connectstatus ? (
                      <li>
                        <Link to="#" onClick={unfriendHandler}>
                          <i className="material-icons-outlined">
                            person_remove
                          </i>
                          <span>{t("Unfriend")}</span>
                        </Link>
                      </li>
                    ) : null}

                    {userDetails.connectstatus ? null : userDetails.unfriendstatus ==
                      "0" ? (
                      <li>
                        <Link to="#" onClick={cancelRequestHandler}>
                          <i className="material-icons-outlined red_icon">
                            close
                          </i>
                          <span>{t("Cancel Request")}</span>
                        </Link>
                      </li>
                    ) : userDetails.resumecontactowner == "1" ||
                      userDetails.resumecontactmember == "1" ? (
                      <li>
                        <Link to="#" onClick={resumeContactHandler}>
                          <i className="material-icons-outlined red_icon">
                            question_mark
                          </i>
                          <span>{t("Resume Contact")}</span>
                        </Link>
                      </li>
                    ) : null}
                  </ul>
                </div>
              ) : (
                <div className="details_apply">
                  <Link
                    to="#"
                    className="btn outline"
                    data-bs-toggle="modal"
                    data-bs-target="#send_rqst_mdl"
                  >
                    {t("Connect")}
                  </Link>
                </div>
              )}

              {/* chat icon  */}
              {userDetails?._id?.toString() ===
              userInfo._id.toString() ? null : (
                <div className="details_apply">
                  <Link
                    to="#"
                    className="btn outline"
                    onClick={createContactChatHandler}
                  >
                    <i className="material-icons-outlined">chat</i>
                  </Link>
                </div>
              )}

              {/* tag trigger section start */}
              <div className="details_apply">
                <Link
                  data-bs-target="#tags_modal"
                  data-bs-toggle="modal"
                  to="#"
                  className="btn outline"
                >
                  <i className="material-icons-outlined">more</i>
                </Link>
              </div>
              {/* tag trigger section end */}
            </div>

            {/* <div
              className={
                userDetails?._id?.toString() === userInfo._id.toString()
                  ? "d-none"
                  : "feedback_jointbtnOuter flex-wrap mb-4"
              }
            >
              <div className="feed_btn">
                <Link
                  to="#"
                  data-bs-toggle="modal"
                  data-bs-target="#intial_meet_modal"
                  className="btn gray_btn"
                >
                  Meet
                </Link>
              </div>
            </div> */}

            {/* connectdate and request date section start */}
            {userDetails.connectdate !== "" ? (
              <div className="connected_text">
                <p className="d-flex align-items-center justify-content-center">
                  <i className="material-icons">check_circle</i>
                  <span>
                    {t("Connected on")}{" "}
                    {getDateFormatInSpanish(userDetails.connectdate)}
                  </span>
                </p>
              </div>
            ) : userDetails.requestdate !== "" ? (
              <div className="connected_text">
                <p className="d-flex align-items-center justify-content-center">
                  <i className="material-icons text-danger">info</i>
                  <span>
                    {t("Request on")}{" "}
                    {getDateFormatInSpanish(userDetails.requestdate)}
                  </span>
                </p>
              </div>
            ) : null}
            {/* connectdate and request date section end */}

            {/* tag section start */}
            <div className="tags_links mt-4 mb-2">
              <ul className="list_stye_none d-flex flex-wrap gap-2 justify-content-center">
                {userDetails?.tags?.map((tag, index) => {
                  return (
                    <li key={index}>
                      <Link to="#" className="d-flex align-items-center gap-2">
                        <span>{tag.title}</span>
                        {/* <i className="material-icons">close</i> */}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </div>
            {/* tag section end */}
          </div>
        </div>
      </div>

      {/* modal section start */}
      <RequestCancelModal afterModalClose={getUserDetails} />

      <UnfriendSuccessModal afterModalClose={getUserDetails} />

      <ResumeContactSuccessModal afterModalClose={getUserDetails} />

      <RecomandationModal
        recieverId={params.id}
        token={token}
        userName={`${userDetails.name} ${userDetails.surname}`}
        userImage={userImage}
      />

      <RecomnadSuccessModal />
    </div>
  );
};

export default AppUserLeftPanel;
