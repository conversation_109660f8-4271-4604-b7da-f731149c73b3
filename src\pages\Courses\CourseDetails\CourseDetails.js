/* eslint-disable */
import React, { useEffect } from "react";
import CourseDetailsBody from "components/CoursesComponents/CourseDetailsComponents/CourseDetailsBody/CourseDetailsBody";
import AuthHeader from "components/common/AuthHeader";

const CourseDetails = () => {
  useEffect(() => {
    document.title = "Course Details";
  }, []);

  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ------- body section ------------ */}
      <CourseDetailsBody />
    </>
  );
};

export default CourseDetails;
