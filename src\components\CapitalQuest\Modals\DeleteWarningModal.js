import React from "react";

const DeleteWarningModal = ({ onDelete, onCancelDelete, moduleName = "" }) => {
  return (
    <div className="post_modal success_greetings_mdl">
      <div
        className="modal fade"
        id="delete_warning_mdl"
        data-bs-backdrop="static"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-body">
              <div className="gretings_icon text-danger">
                <span className="material-icons-outlined">warning</span>
              </div>
              <div className="gretings_hdng">
                <h3>Are you sure you want to delete this {moduleName} ?</h3>
              </div>
              <div className="post_btn post_bnts_outer">
                <button
                  className="btn"
                  data-bs-dismiss="modal"
                  onClick={onDelete}
                >
                  Yes Delete
                </button>
                <button
                  className="btn"
                  data-bs-dismiss="modal"
                  onClick={onCancelDelete}
                >
                  No, Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteWarningModal;
