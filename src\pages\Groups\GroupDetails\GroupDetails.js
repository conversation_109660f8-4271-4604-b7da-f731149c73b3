/* eslint-disable */
import React, { useEffect } from "react";
import GroupDetailsBody from "components/GroupsComponents/GroupDetailsComponents/GroupDetailsBody/GroupDetailsBody";
import AuthHeader from "components/common/AuthHeader";

const GroupDetails = () => {
  useEffect(() => {
    document.title = "Group Details";
  }, []);

  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ------- body section ------------ */}
      <GroupDetailsBody />
    </>
  );
};

export default GroupDetails;
