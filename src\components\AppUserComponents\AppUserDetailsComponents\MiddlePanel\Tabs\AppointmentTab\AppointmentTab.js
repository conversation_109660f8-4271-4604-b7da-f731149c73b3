/* eslint-disable */
import React, { useState, useEffect } from "react";
import { useParams, <PERSON> } from "react-router-dom";

//calendar --------------------------------------------
import { Calendar, momentLocalizer } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
// -----------------------------------------------

/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import AppointmentModal from "components/AppUserComponents/AppUserDetailsComponents/Modals/AppointmentModal";
import ScheduleConfirmModal from "components/AppUserComponents/AppUserDetailsComponents/Modals/ScheduleConfirmModal";

const AppointmentTab = ({ tabName = "" }) => {
  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const localizer = momentLocalizer(moment);

  const [isLoading, setIsLoading] = useState(false);
  const [userDetails, setUserDetails] = useState({});
  const [calenderAgendaDates, setCalenderAgendaDates] = useState([]);

  // set selected agenda id
  const [selectedAgendaId, setSelectedAgendaId] = useState(null);
  const [meetingStartTime, setMeetingStartTime] = useState("");
  const [meetingEndTime, setMeetingEndTime] = useState("");
  const [meetingDuration, setMeetingDuration] = useState("");
  const [selectedMeetingDate, setSelectedMeetingDate] = useState("");

  // set color to event
  const eventStyleGetter = (event) => {
    const backgroundColor = event.availability ? "#05843e" : "#DF3429"; // set green or red
    return { style: { backgroundColor } };
  };

  //function for get user details
  const getUserDetails = async () => {
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_USER_DETAILS +
        `/${params.id}` +
        `?token=${token}`;

      const response = await getData(requestUrl);

      // console.log("response in app user left panel", response);

      if (response.status && response.data) {
        setUserDetails(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //get all agenda date specific
  const getAllAgenda = async () => {
    try {
      setIsLoading(true);

      let useraccountArr = [params.id];

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_AGENDA +
        `?token=${token}&useraccount=${useraccountArr}`;

      console.log("agenda url", requestUrl);

      const response = await getData(requestUrl);

      setIsLoading(false);

      console.log("agenda records", response);

      if (response.status) {
        // filter data as per format
        setCalendarEventData(response.data);

        //setCalenderAgendaDates(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  // arrange the event data for big calendar
  const setCalendarEventData = (agendaRecords = []) => {
    const events = agendaRecords.map(
      ({ _id, description, date, starttime, endtime, availability }) => ({
        id: _id,
        title: description,
        start: new Date(`${date}T${starttime}:00`),
        end: new Date(`${date}T${endtime}:00`),
        availability,
      })
    );

    setCalenderAgendaDates(events);
  };

  //function for on select event
  const onSelectEvent = (event) => {
    if (params.id.toString() !== userInfo._id) {
      // console.log("event", event);
      setSelectedAgendaId(event.id);

      if (event.availability) {
        const myModal = new bootstrap.Modal(
          document.getElementById("appointment_meet_modal")
        );
        myModal.show();
      }
    }
  };

  //function for on select slot
  // const onSelectSlot = (event) => {
  //   if (params.id.toString() !== userInfo._id) {
  //     const dateObj = new Date(event.start);

  //     // Extract year, month, and day from the date object
  //     const year = dateObj.getFullYear();
  //     const month = String(dateObj.getMonth() + 1).padStart(2, "0"); // Month is zero-based
  //     const day = String(dateObj.getDate()).padStart(2, "0");

  //     // Construct the formatted date string
  //     const formattedDate = `${year}-${month}-${day}`;

  //     setSelectedMeetingDate(formattedDate);

  //     const myModal = new bootstrap.Modal(
  //       document.getElementById("appointment_meet_modal")
  //     );

  //     myModal.show();
  //   }
  // };

  useEffect(() => {
    if (params.id && tabName === "appointments") {
      getAllAgenda();
      getUserDetails();
    }
  }, [params.id, tabName]);

  // glow placeholder
  if (isLoading) {
    return (
      <div className="people_details_bx white_shadow_bx">
        <div className="booking_table mb-4">
          <div style={{ padding: "10px" }}>
            <div className="placeholder-glow">
              <div
                className="placeholder col-12"
                style={{ height: "300px" }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="people_details_bx white_shadow_bx">
      <div className="booking_table mb-4">
        <div style={{ height: 600, padding: "10px" }}>
          <Calendar
            localizer={localizer}
            events={calenderAgendaDates} // Make sure you're passing events data to the calendar component. This is where you'll pass the filteredEventData array.
            startAccessor="start"
            endAccessor="end"
            onSelectEvent={onSelectEvent}
            // onSelectSlot={onSelectSlot}
            selectable
            style={{ flex: 1 }}
            eventPropGetter={eventStyleGetter}
          />
        </div>
      </div>

      {/* modal  */}

      <AppointmentModal
        personName={`${userDetails.name} ${userDetails.surname}`}
        selectedAgendaId={selectedAgendaId}
        setSelectedAgendaId={setSelectedAgendaId}
        meetingStartTime={meetingStartTime}
        setMeetingStartTime={setMeetingStartTime}
        meetingEndTime={meetingEndTime}
        setMeetingEndTime={setMeetingEndTime}
        meetingDuration={meetingDuration}
        setMeetingDuration={setMeetingDuration}
        setSelectedMeetingDate={setSelectedMeetingDate}
      />

      <ScheduleConfirmModal
        personName={`${userDetails.name} ${userDetails.surname}`}
        meetingStartTime={meetingStartTime}
        meetingEndTime={meetingEndTime}
        selectedMeetingDate={selectedMeetingDate}
        meetingDuration={meetingDuration}
        onModalClose={() => {
          setSelectedAgendaId(null);
          setMeetingStartTime("");
          setMeetingEndTime("");
          setMeetingDuration("");
          getAllAgenda();
        }}
      />
    </div>
  );
};

export default AppointmentTab;
