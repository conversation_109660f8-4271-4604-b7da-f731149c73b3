/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams, useHistory, useLocation } from "react-router-dom";
// import use translation
import { useTranslation } from "react-i18next";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

//import images
import { assetImages } from "constants";
import { getDateFormatInSpanish, todayValue } from "helper/Common/CommonHelper";
import LeftPanlePlaceholder from "../../../common/Placeholders/LeftPanlePlaceholder";
import BuyProductQuotationPopup from "../Popup/BuyProductQuotationPopup";
import SellProductQuotationPopup from "../Popup/SellProductQuotationPopup";
import { generateRandomString } from "utils/GetRandomString";

const EventDetailsLeftPanel = ({
  isJoined = false,
  setisJoined = () => {},
  setIsLoading = () => {},
  setCopyMessage = () => {},
  setEventType = () => {},
  setisApproved = () => {},
  setIsModerator = () => {},
  refreshEvent = false,
  setRefreshEvent = () => {},
}) => {
  const fullURL = window.location.href;

  const parsedURL = new URL(fullURL);
  const searchParams = parsedURL.searchParams;
  const secretParam = searchParams.get("secret");
  const emailParams = searchParams.get("memberemail");
  const memberShipIdParams = searchParams.get("membershipid");

  const history = useHistory();
  const location = useLocation();

  const params = useParams();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const { t } = useTranslation(); // translation marker

  const memberLimit = 5; //for displaying the limit number of members

  const [isDetailsLoading, setIsDetailsLoading] = useState(false);
  const [eventDetails, seteventDetails] = useState({});
  const [isJoining, setIsJoining] = useState(false);

  //share function
  const shareHandler = async () => {
    const baseURL = `${parsedURL.protocol}//${parsedURL.host}`;

    const secretString = generateRandomString(4); // Change 10 to the desired length

    try {
      let eventData = {
        secret: secretString,
      };
      let requestUrl =
        url.API_BASE_URL +
        url.API_UPDATE_EVENT +
        `/${params.id}` +
        `?token=${token}`;

      const response = await putData(requestUrl, eventData);

      if (response.status) {
        const redirectUrl = baseURL + `?ref=${fullURL}&secret=${secretString}`;

        const bootstrapModal = new bootstrap.Modal(
          document.getElementById("share_mdl")
        );
        bootstrapModal.show();

        setCopyMessage(redirectUrl);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for get event details
  const getEventDetails = async () => {
    try {
      setIsDetailsLoading(true);
      setIsLoading(true); // Set loading state to true before API call
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_EVENT_DETAILS +
        `/${params.id}` +
        `?token=${token}&modulefrom=frontendleftpanel`;

      // console.log("requestURL in left panel", requestURL);

      const response = await getData(requestURL);

      console.log("event details response in left panel", response);

      setIsDetailsLoading(false);
      setIsLoading(false); // Set loading state to false after API call completes

      if (response.status) {
        seteventDetails(response.data);
        setEventType(response.data.eventtype);
        setisJoined(response.data.isjoined);
        setisApproved(response.data.isapproved);

        if (
          userInfo?.role?.slug === "ADMIN" ||
          userInfo?.role?.slug === "SUPER_ADMIN" ||
          userInfo._id.toString() == response.data?.moderator._id?.toString()
        ) {
          setIsModerator(true);
        } else {
          setIsModerator(false);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for join event
  const joinEventHandler = async () => {
    try {
      setIsJoining(true);
      let eventData = {
        event: params.id,
        useremail: userInfo.email,
        secret: secretParam ? secretParam : eventDetails.secret,
      };

      // console.log("Join event Data------>", eventData);

      let requestURL =
        url.API_BASE_URL + url.API_JOIN_EVENT + `?token=${token}`;

      const response = await postData(requestURL, eventData);

      // console.log("response in join event------->", response);

      setIsJoining(false);

      console.log(response);

      if (response.status) {
        const currentPath = location.pathname;

        if (location.search) {
          // replace current entry with clean URL
          history.replace(currentPath);
        }

        const bootstrapModal = new bootstrap.Modal(
          document.getElementById("join_success_mdl")
        );
        bootstrapModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for join event by mail
  const joinEventByMail = async (memberShipId) => {
    if (!isJoined) {
      try {
        let eventData = {
          user: userInfo._id,
          memberstatus: "1",
          joiningdate: todayValue,
          event: params.id,
        };

        // console.log("Join event Data------>", eventData);

        let requestURL =
          url.API_BASE_URL +
          url.API_JOIN_EVENT_BY_MAIL +
          `/${memberShipId}?token=${token}`;

        const response = await putData(requestURL, eventData);

        // console.log("response in join event by mail------->", response);

        if (response.status) {
          setRefreshEvent(true);

          const currentPath = location.pathname;

          if (location.search) {
            // replace current entry with clean URL
            history.replace(currentPath);
          }
        }
      } catch (error) {
        console.log(error.message);
      }
    } else {
      console.log("already joined");
    }
  };

  useEffect(() => {
    if (params.id) {
      getEventDetails();
    }
  }, [params.id]);

  useEffect(() => {
    if (refreshEvent) {
      getEventDetails();
      setRefreshEvent(false);
    }
  }, [refreshEvent]);

  useEffect(() => {
    if (secretParam) {
      joinEventHandler();
    }
  }, [secretParam]);

  useEffect(() => {
    if (emailParams && memberShipIdParams) {
      if (userInfo.email === emailParams) {
        joinEventByMail(memberShipIdParams);
      }
    }
  }, [emailParams, memberShipIdParams]);

  if (isDetailsLoading) {
    return <LeftPanlePlaceholder />;
  }

  return (
    <div className="pnl_leftpnl">
      <div className="white_shadow_bx people_leftusr">
        <div className="groups_info">
          <div className="top_grp_info">
            <div className="experts_holdertext">
              <h3>{eventDetails?.title}</h3>
              <div className="event_hdng">
                <h5>
                  <i className="material-icons-outlined"> event_note </i>
                  <em>{getDateFormatInSpanish(eventDetails?.fromdate)}</em>
                </h5>
              </div>
            </div>
            <div className="share_btn">
              <Link to="#" onClick={shareHandler}>
                <span className="material-icons">share</span>
              </Link>
            </div>
          </div>

          <div className="event_tags">
            <ul className="list_stye_none">
              <li>
                <i className="material-icons-outlined ornge_icon">star</i>
                <span className="rating">
                  {eventDetails?.totalratings}{" "}
                  <em>({eventDetails?.totalreviews})</em>
                </span>
              </li>
              <li>
                <i className="material-icons-outlined">place</i>
                <span>
                  {eventDetails?.city}, {eventDetails?.country}
                </span>
              </li>
            </ul>
          </div>

          {/* event members  */}
          <div className="event_user grps_user">
            <ul className="list_stye_none">
              {eventDetails?.eventmemberlist
                ?.slice(0, memberLimit)
                .map((member, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">
                        <img
                          className="rounded-circle"
                          width="40"
                          height="40"
                          src={
                            member.userimage == ""
                              ? assetImages.defaultUser
                              : url.SERVER_URL + member.userimage
                          }
                          alt=""
                        />
                      </Link>
                    </li>
                  );
                })}
              <li>
                {eventDetails?.eventmemberlist?.length > memberLimit ? (
                  <Link to="#">
                    +{eventDetails?.eventmemberlist?.length - memberLimit} more
                  </Link>
                ) : null}
              </li>
            </ul>
          </div>

          <div className="feedback_jointbtnOuter">
            {eventDetails?.moderator?._id?.toString() ===
              userInfo._id.toString() ||
            userInfo.role.slug === "ADMIN" ||
            userInfo.role.slug === "SUPER_ADMIN" ? null : (
              <div className="feed_btn">
                {eventDetails?.isapproved ? (
                  <Link to="#" className="btn gray_btn accepted_btn">
                    <i className="material-icons-outlined"> check_circle </i>
                    <span>{t("Joined")}</span>
                  </Link>
                ) : eventDetails?.isjoined ? null : (
                  <button
                    className="btn outline"
                    onClick={joinEventHandler}
                    disabled={isJoining ? true : false}
                    style={{
                      cursor: isJoining ? "not-allowed" : "pointer",
                    }}
                  >
                    {t("Join Now")}
                    {isJoining && (
                      <div
                        className="mx-2 spinner-border spinner-border-sm"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    )}
                  </button>
                )}
              </div>
            )}

            {userInfo.role.slug === "ADMIN" ||
            userInfo.role.slug === "SUPER_ADMIN" ||
            eventDetails?.moderator?._id?.toString() ===
              userInfo._id.toString() ||
            eventDetails?.isapproved ? (
              <div className="feed_btn">
                <Link
                  to="#"
                  className="btn gray_btn"
                  data-bs-toggle="modal"
                  data-bs-target="#feedback_mdl"
                >
                  {t("Give Review")}
                </Link>
              </div>
            ) : null}

            <div className="tag_btn">
              <Link
                data-bs-target="#tags_modal"
                data-bs-toggle="modal"
                to="#"
                className="btn gray_btn"
              >
                <i className="material-icons-outlined">more</i>
              </Link>
            </div>
            {/* add tag button end */}
          </div>

          {eventDetails?.eventtype &&
          eventDetails?.eventtype?.slug === "buysell" &&
          (eventDetails?.isapproved ||
            eventDetails?.moderator?._id?.toString() ===
              userInfo._id.toString() ||
            userInfo.role.slug === "ADMIN" ||
            userInfo.role.slug === "SUPER_ADMIN") ? (
            <div className="mt-2 feed_btn_row d-flex">
              <Link
                to="#"
                className="btn outline buy_btn me-2"
                data-bs-toggle="offcanvas"
                data-bs-target="#offcanvasBuyQuotation"
              >
                {t("Buy")}
              </Link>
              <Link
                to="#"
                className="btn outline sell_btn"
                data-bs-toggle="offcanvas"
                data-bs-target="#offcanvasSellQuotation"
              >
                {t("Sell")}
              </Link>
            </div>
          ) : (
            ""
          )}

          {eventDetails?.moderator?._id?.toString() ===
          userInfo._id.toString() ? (
            <div className="connected_text">
              <p className="d-flex align-items-center justify-content-start">
                <i className="material-icons">check_circle</i>
                <span>{t("Moderator")}</span>
              </p>
            </div>
          ) : (
            <div className="connected_text">
              {eventDetails?.isapproved ? (
                <p className="d-flex align-items-center justify-content-start">
                  <i className="material-icons">check_circle</i>
                  <span>
                    {t("Joined on")}{" "}
                    {getDateFormatInSpanish(eventDetails?.approvaldate)}
                  </span>
                </p>
              ) : eventDetails?.isjoined ? (
                <p className="d-flex align-items-center justify-content-start">
                  <i className="material-icons text-danger">error</i>
                  <span>
                    {t("Request on")}{" "}
                    {getDateFormatInSpanish(eventDetails?.requestdate)}
                  </span>
                </p>
              ) : null}
            </div>
          )}

          <div className="tags_links mt-4 mb-2">
            <ul className="list_stye_none d-flex flex-wrap gap-2 ">
              {eventDetails?.eventtags?.map((tag, index) => {
                return (
                  <li key={index}>
                    <Link to="#" className="d-flex align-items-center gap-2">
                      <span>{tag.title}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>

      <BuyProductQuotationPopup
        eventid={params.id} // Pass appropriate data for products
        buyprice={eventDetails?.buyparticipationfee}
        galleryImages={eventDetails.gallery}
        endDate={eventDetails.todate}
        onClose={() => console.log("Offcanvas closed")} // Replace with actual close handler
      />

      <SellProductQuotationPopup
        eventid={params.id} // Pass appropriate data for products
        sellprice={eventDetails?.sellparticipationfee}
        galleryImages={eventDetails.gallery}
        endDate={eventDetails.todate}
        onClose={() => console.log("Offcanvas closed")} // Replace with actual close handler
      />
    </div>
  );
};

export default EventDetailsLeftPanel;
