/* eslint-disable */
import React, { useEffect, useState } from "react";

import AuthHeader from "components/common/AuthHeader";
//import CognifitBrainGamesListBody from "components/CognifitComponents/ActivityList/BrainGamesList";  
import CognifitAssesmentListBody from "components/CognifitComponents/ActivityList/AssesmentList"; 
//import CognifitTrainingListBody from "components/CognifitComponents/ActivityList/TrainingList";

const TABS = [
  { key: "assessments", label: "Assessments" },
  { key: "brainGames", label: "Brain Games" },
  { key: "training", label: "Training" }
];

const CognifitActivityList = () => {
  useEffect(() => {
    document.title = "Cognifit | Activity List";
  }, []);

  const [activeTab, setActiveTab] = useState("assessments");

  const renderTabContent = () => {
    switch (activeTab) {
      // case "brainGames":
      //   return <CognifitBrainGamesListBody />;
      case "assessments":
        return <CognifitAssesmentListBody />;
      // case "training":
      //   return <CognifitTrainingListBody />;
      default:
        return null;
    }
  };

  return (
    <>
      <AuthHeader moduleName={"cognifit"} />
      <div className="container-fluid" style={{ marginTop: "6rem" }}>
        <div className="bg-white rounded-10 shadow-sm p-3 mb-4">
          <ul className="nav nav-tabs professional-tabs mb-3" role="tablist">
            {TABS.map(tab => (
              <li className="nav-item" key={tab.key} role="presentation">
                <button
                  className={`nav-link${activeTab === tab.key ? " active" : ""}`}
                  style={{
                    fontWeight: 600,
                    fontSize: "1.1rem",
                    border: "none",
                    borderBottom: activeTab === tab.key ? "3px solid #007bff" : "3px solid transparent",
                    color: activeTab === tab.key ? "#007bff" : "#222",
                    background: "none",
                    padding: "12px 24px",
                    transition: "border-bottom 0.2s"
                  }}
                  onClick={() => setActiveTab(tab.key)}
                  type="button"
                  role="tab"
                  aria-selected={activeTab === tab.key}
                >
                  {tab.label}
                </button>
              </li>
            ))}
          </ul>
          <div>
            {renderTabContent()}
          </div>
        </div>
      </div>
    </>
  );
};

export default CognifitActivityList;