/* eslint-disable */
import React, { useState, useEffect } from "react";

import { Link, useParams } from "react-router-dom";

import * as url from "helper/UrlHelper";

import GiveReviewModal from "components/common/Modals/GiveReviewModal";
import ShowReviewModal from "components/common/Modals/ShowReviewModal";

import { getData } from "utils/Gateway";
import ViewIdea from "components/JobsComponents/JobDetailsComponents/Popups/ViewIdea";
import ViewOpportunity from "components/JobsComponents/JobDetailsComponents/Popups/ViewOpportunity";
import { assetImages } from "constants";

const FeedbackTab = ({ tabName = "" }) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const params = useParams();

  const challengeId = params.id;

  const [selectedIdea, setSelectedIdea] = useState(null); // idea id if any

  const [selectedOpportunity, setSelectedOpportunity] = useState(null); // opportunity id if any

  const [selectedChallengeResponse, setSelectedChallengeResponse] =
    useState(null); // challenge response

  const [selectedReview, setSelectedReview] = useState(null); // review id if any

  const [selectedUserId, setSelectedUserId] = useState(null); // who receive the feedback

  const [allIdeas, setAllIdeas] = useState([]); // all ideas of the challenge associated with the user
  const [allOpportunities, setAllOpportunities] = useState([]); // all opportunities of the challenge associated with the user

  const [showIdea, setShowIdea] = useState(false);
  const [showOpportunity, setShowOpportunity] = useState(false);
  const [showReview, setShowReview] = useState(false);
  const [isReviewDone, setIsReviewDone] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  // Show feedback data ---------------------
  const showFeedbackData = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_CHALLENGE_FEEDBACK_COMPONENTS +
        `/${params.id}?token=${token}&challenge=${challengeId}`;

      console.log("feedback requestURL", requestURL);

      const response = await getData(requestURL);

      console.log("feedback response", response);

      setIsLoading(false);

      if (response.status) {
        setAllIdeas(response.ideas);
        setAllOpportunities(response.invoices);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  // show idea popup -------------------
  const showIdeaHandler = () => {
    console.log("showIdeaHandler selectedIdea", selectedIdea);

    const offcanvasElement = document.getElementById("updateIdeaOffcanvas");
    const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
    bsOffcanvas.show();

    setShowOpportunity(false);
  };

  // show opportunity popup ------------
  const showOpportunityHandler = () => {
    console.log(
      "showOpportunityHandler selectedOpportunity",
      selectedOpportunity
    );

    const offcanvasElement = document.getElementById(
      "updateOpportunityOffcanvas"
    );
    const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
    bsOffcanvas.show();

    setShowIdea(false);
  };

  // open Givereview modal with id feedback_mdl
  const reviewIdea = (idea) => {
    setSelectedIdea(idea);
    setSelectedOpportunity(null);

    let reviewModal = document.getElementById("feedback_mdl");
    let modal = new bootstrap.Modal(reviewModal);
    modal.show();
  };

  // open Givereview modal with id feedback_mdl
  const reviewOpportunity = (invoice) => {
    setSelectedIdea(null);
    setSelectedOpportunity(invoice);

    let reviewModal = document.getElementById("feedback_mdl");
    let modal = new bootstrap.Modal(reviewModal);
    modal.show();
  };

  // show review
  const showReviewHandler = (review) => {
    setSelectedReview(review);
    setShowReview(true);

    let reviewModal = document.getElementById("view_feedback_modal");
    let modal = new bootstrap.Modal(reviewModal);
    modal.show();
  };

  // show idea
  useEffect(() => {
    console.log("selectedIdea", selectedIdea);
    console.log("showIdea", showIdea);

    if ((selectedIdea != null) & showIdea) {
      showIdeaHandler();
    }
  }, [selectedIdea, showIdea]);

  useEffect(() => {
    if (isReviewDone) {
      showFeedbackData();
    }
  }, [isReviewDone]);

  // show opportunity
  useEffect(() => {
    console.log("selectedOpportunity", selectedOpportunity);
    console.log("showOpportunity", showOpportunity);

    if ((selectedOpportunity != null) & showOpportunity) {
      showOpportunityHandler();
    }
  }, [selectedOpportunity, showOpportunity]);

  // show feedbacks
  useEffect(() => {
    if (challengeId && tabName === "feedbacktab") {
      showFeedbackData();
    }
  }, [challengeId, tabName]);

  // Loading placeholder component
  const LoadingPlaceholder = () => (
    <div className="white_shadow_bx p-3 rounded">
      <div className="table-responsive">
        <table className="table table-hover">
          <thead>
            <tr>
              <th scope="col" style={{ minWidth: "150px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "90%" }}
                ></div>
              </th>
              <th scope="col" style={{ minWidth: "120px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "80%" }}
                ></div>
              </th>
              <th scope="col" style={{ minWidth: "150px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "90%" }}
                ></div>
              </th>
              <th scope="col" style={{ minWidth: "100px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "70%" }}
                ></div>
              </th>
              <th scope="col" style={{ minWidth: "100px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "60%" }}
                ></div>
              </th>
              <th scope="col" style={{ minWidth: "100px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "60%" }}
                ></div>
              </th>
              <th scope="col" style={{ minWidth: "120px" }}>
                <div
                  className="shimmer-placeholder"
                  style={{ height: "24px", width: "70%" }}
                ></div>
              </th>
            </tr>
          </thead>
          <tbody>
            {[1, 2, 3, 4].map((row) => (
              <tr key={row}>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "85%" }}
                  ></div>
                </td>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "70%" }}
                  ></div>
                </td>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "85%" }}
                  ></div>
                </td>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "60%" }}
                  ></div>
                </td>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "50%" }}
                  ></div>
                </td>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "40%" }}
                  ></div>
                </td>
                <td>
                  <div
                    className="shimmer-placeholder"
                    style={{ height: "20px", width: "70%" }}
                  ></div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <style jsx>{`
        .shimmer-placeholder {
          background: #f6f7f8;
          background: linear-gradient(
            to right,
            #f6f7f8 8%,
            #edeef1 18%,
            #f6f7f8 33%
          );
          background-size: 1000px 104px;
          border-radius: 4px;
          display: inline-block;
          animation: shimmer 1.5s infinite linear;
        }
        @keyframes shimmer {
          0% {
            background-position: -468px 0;
          }
          100% {
            background-position: 468px 0;
          }
        }
      `}</style>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="pnl_mdlpnl w-100">
      <div className="empty_access text-center">
        <div className="empty_pic mb-4">
          <img src={assetImages.emptyVector} alt="" />
        </div>
        <div className="empty_text">
          <p className="fs-md text-gray fw-semibold">
            No ideas or opportunities have been sent or received yet.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <div className="d-flex justify-content-end mb-3">
        <button
          className="btn outline d-flex align-items-center"
          onClick={() => {
            showFeedbackData();
          }}
          disabled={isLoading}
        >
          {isLoading ? (
            <span
              className="spinner-border spinner-border-sm me-2"
              role="status"
              aria-hidden="true"
            ></span>
          ) : (
            <span className="material-icons-outlined me-1">refresh</span>
          )}
          {isLoading ? "Loading..." : "Refresh"}
        </button>
      </div>

      {isLoading ? (
        <LoadingPlaceholder />
      ) : allIdeas.length === 0 && allOpportunities.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="people_details_bx white_shadow_bx p-3 rounded">
          <div className="table-responsive" style={{ overflowX: "auto" }}>
            <table className="table table-hover table-striped align-middle">
              <thead className="table-light">
                <tr key="0">
                  <th scope="col" style={{ minWidth: "150px" }}>
                    Who Send
                  </th>
                  <th scope="col" style={{ minWidth: "120px" }}>
                    What Send
                  </th>
                  <th scope="col" style={{ minWidth: "150px" }}>
                    Who Receive
                  </th>
                  <th scope="col" style={{ minWidth: "100px" }}>
                    Date
                  </th>
                  <th scope="col" style={{ minWidth: "100px" }}>
                    Status
                  </th>
                  <th scope="col" style={{ minWidth: "100px" }}>
                    Feedback
                  </th>
                  <th scope="col" style={{ minWidth: "120px" }}>
                    Action
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* Map all ideas */}
                {allIdeas &&
                  allIdeas.map((idea) => (
                    <tr key={idea.id}>
                      <td className="fw-medium">
                        {idea.owner?.name ?? ""} {idea.owner?.surname ?? ""}
                      </td>
                      <td>
                        <Link
                          to="#"
                          className="text-primary text-decoration-none"
                          onClick={() => {
                            setSelectedIdea(idea);
                            setShowIdea(true);
                          }}
                        >
                          <span className="material-icons-outlined fs-5 align-middle me-1">
                            lightbulb
                          </span>
                          Idea
                        </Link>
                      </td>
                      <td>
                        {idea.receiver?.name ?? ""}{" "}
                        {idea.receiver?.surname ?? ""}
                      </td>
                      <td>{new Date(idea.createdAt).toLocaleDateString()}</td>
                      <td>
                        {idea.reviews && idea.reviews.length > 0 ? (
                          <span className="badge bg-success">Completed</span>
                        ) : (
                          <span className="badge bg-warning text-dark">
                            Pending
                          </span>
                        )}
                      </td>
                      <td>
                        {idea.reviews && idea.reviews.length > 0 ? (
                          <span className="fw-bold">
                            {idea.reviews[0].rating}
                          </span>
                        ) : (
                          "--"
                        )}
                      </td>
                      <td>
                        {idea.reviews && idea.reviews.length > 0 ? (
                          <button
                            className="btn outline btn-outline-primary"
                            onClick={() => {
                              showReviewHandler(idea.reviews[0]);
                            }}
                          >
                            View
                          </button>
                        ) : idea.receiver?._id === userInfo._id ? (
                          <button
                            className="btn outline btn-primary"
                            onClick={() => {
                              reviewIdea(idea);
                            }}
                          >
                            Feedback
                          </button>
                        ) : (
                          <span className="text-muted">Pending</span>
                        )}
                      </td>
                    </tr>
                  ))}

                {/* Map all opportunities */}
                {allOpportunities &&
                  allOpportunities.map((invoice) => (
                    <tr key={invoice.id}>
                      <td className="fw-medium">
                        {invoice.user?.name ?? ""} {invoice.user?.surname ?? ""}
                      </td>
                      <td>
                        <Link
                          to="#"
                          className="text-primary text-decoration-none"
                          onClick={() => {
                            setSelectedOpportunity(invoice);
                            setShowOpportunity(true);
                          }}
                        >
                          <span className="material-icons-outlined fs-5 align-middle me-1">
                            business_center
                          </span>
                          Opportunity
                        </Link>
                      </td>
                      <td>
                        {invoice.client?.name ?? ""}{" "}
                        {invoice.client?.surname ?? ""}
                      </td>
                      <td>
                        {new Date(invoice.createdAt).toLocaleDateString()}
                      </td>
                      <td>
                        {invoice.reviews && invoice.reviews.length > 0 ? (
                          <span className="badge bg-success">Completed</span>
                        ) : (
                          <span className="badge bg-warning text-dark">
                            Pending
                          </span>
                        )}
                      </td>
                      <td>
                        {invoice.reviews && invoice.reviews.length > 0 ? (
                          <span className="fw-bold">
                            {invoice.reviews[0].rating}
                          </span>
                        ) : (
                          "--"
                        )}
                      </td>
                      <td>
                        {invoice.reviews && invoice.reviews.length > 0 ? (
                          <button
                            className="btn btn-sm btn-outline-primary"
                            onClick={() => {
                              showReviewHandler(invoice.reviews[0]);
                            }}
                          >
                            View
                          </button>
                        ) : invoice.client?._id === userInfo._id ? (
                          <button
                            className="btn outline btn-primary"
                            onClick={() => {
                              reviewOpportunity(invoice);
                            }}
                          >
                            Feedback
                          </button>
                        ) : (
                          <span className="text-muted">Pending</span>
                        )}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <GiveReviewModal
        moduleName="challenge"
        componentid={params.id}
        receiverid={selectedUserId}
        ideaid={selectedIdea ? selectedIdea._id : null}
        opportunityid={selectedOpportunity ? selectedOpportunity._id : null}
        setIsReviewDone={setIsReviewDone}
      />

      <ViewIdea
        idea={selectedIdea}
        challengeId={params.id}
        showIdea={showIdea}
        setShowIdea={setShowIdea}
      />

      <ViewOpportunity
        opportunity={selectedOpportunity}
        challengeId={params.id}
        showOpportunity={showOpportunity}
        setShowOpportunity={setShowOpportunity}
      />

      <ShowReviewModal
        reviewData={selectedReview}
        showReview={showReview}
        setShowReview={setShowReview}
      />
    </>
  );
};

export default FeedbackTab;
