/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

const DetailsTab = ({ tabName = "" }) => {
  const params = useParams();
  const token = localStorage.getItem("token");

  const [isLoading, setisLoading] = useState(true); // Set initial value to true

  const [summary, setsummary] = useState("");
  const [details, setdetails] = useState("");
  const [aboutCompany, setaboutCompany] = useState("");
  const [aboutPosition, setaboutPosition] = useState("");
  const [minSalary, setminSalary] = useState("");
  const [maxSalary, setmaxSalary] = useState("");
  const [isApplied, setIsApplied] = useState(false);

  //function for get project details
  const getJobDetails = async () => {
    setisLoading(true);
    try {
      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_CHALLENGE_DETAILS +
        `/${params.id}?token=${token}`;

      const response = await getData(requestUrl);
      setisLoading(false);
      console.log(response);
      if (response.status && response.data) {
        const data = response.data;
        setsummary(data.summary);
        setdetails(data.description);
        setaboutCompany(data.aboutcompany);
        setaboutPosition(data.aboutposition);
        setminSalary(data.minsalary);
        setmaxSalary(data.maxsalary);
        setIsApplied(response.data.isappiled);
      }
    } catch (error) {
      setisLoading(false);
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "overview") {
      getJobDetails();
    }
  }, [params.id, tabName]);

  // Placeholder styles for loading state
  const placeholderStyle = {
    background: "linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",
    backgroundSize: "200% 100%",
    animation: "glowing 1.5s infinite",
    borderRadius: "4px",
    height: "18px",
    margin: "8px 0",
    display: "block",
  };

  // Placeholder text function to generate multiple lines of loading placeholders
  const PlaceholderText = ({ lines = 3, width = 100 }) => (
    <>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          style={{
            ...placeholderStyle,
            width: `${width - (index % 3) * 10}%`,
          }}
        ></div>
      ))}
    </>
  );

  return (
    <div className="people_detailspnl">
      <div className="people_details_bx white_shadow_bx">
        <div className="results_innr">
          <div className="results_item">
            <div className="results_para">
              <h3>Summary</h3>
              {isLoading ? (
                <PlaceholderText lines={3} width={90} />
              ) : (
                <p>{summary}</p>
              )}
            </div>
          </div>
          <div className="results_item">
            <h3>Details</h3>
            <div className="results_para">
              {isLoading ? (
                <PlaceholderText lines={5} width={95} />
              ) : (
                <p dangerouslySetInnerHTML={{ __html: details }}></p>
              )}
            </div>
          </div>

          <div className="results_item">
            <h3>About Position</h3>
            <div className="results_para">
              {isLoading ? (
                <PlaceholderText lines={4} width={90} />
              ) : (
                <p dangerouslySetInnerHTML={{ __html: aboutPosition }}></p>
              )}
            </div>
          </div>

          <div className="results_item">
            <h3>About Company</h3>
            <div className="results_para">
              {isLoading ? (
                <PlaceholderText lines={4} width={85} />
              ) : (
                <p dangerouslySetInnerHTML={{ __html: aboutCompany }}></p>
              )}
            </div>
          </div>

          <div className="results_item">
            <h3>Salary and compensation</h3>
            <div className="results_para">
              {isLoading ? (
                <div style={{ ...placeholderStyle, width: "120px" }}></div>
              ) : (
                <p>
                  €{minSalary} — €{maxSalary}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="result_btnArea">
          {isLoading ? (
            <div
              style={{ ...placeholderStyle, width: "120px", height: "36px" }}
            ></div>
          ) : (
            <Link to={`/cvbuilder/${params.id}`} className="btn outline">
              {isApplied ? "Applied" : "Apply Now"}
            </Link>
          )}
        </div>
      </div>
      <style jsx>{`
        @keyframes glowing {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </div>
  );
};

export default DetailsTab;
