/* eslint-disable */
import React from "react";

const GroupListPlaceHolder = () => {
  return (
    <div className="groups_item placeholder-glow">
      <div className="top_company_row company_top d-flex justify-content-between">
        <div className="left_company w-75">
          <div className="event_hdng mb-2">
            <h3 className="placeholder col-8"></h3>
          </div>

          <div className="event_tags">
            <ul className="list_stye_none">
              <li>
                <span
                  className="rating placeholder"
                  style={{ width: "40px" }}
                ></span>
              </li>
              <li>
                <span className="placeholder" style={{ width: "70px" }}></span>
              </li>
            </ul>
          </div>
        </div>

        <div
          className="placeholder rounded-3"
          style={{ width: "80px", height: "80px" }}
        ></div>
      </div>

      <div className="event_para my-3">
        <p className="placeholder col-12 mb-1"></p>
        <p className="placeholder col-11 mb-1"></p>
      </div>

      <div className="company_info_outer">
        <div className="company_info_row mb-3">
          <label className="placeholder col-2 mb-2 d-block"></label>
          <div className="event_tags">
            <ul className="list_stye_none d-flex flex-wrap gap-2">
              <li className="placeholder col-3 rounded-pill py-2"></li>
              <li className="placeholder col-3 rounded-pill py-2"></li>
              <li className="placeholder col-2 rounded-pill py-2"></li>
            </ul>
          </div>
        </div>

        <div className="company_info_row mb-3">
          <label className="placeholder col-2 mb-2 d-block"></label>
          <div className="event_tags">
            <ul className="list_stye_none d-flex flex-wrap gap-2">
              <li className="placeholder col-3 rounded-pill py-2"></li>
              <li className="placeholder col-4 rounded-pill py-2"></li>
            </ul>
          </div>
        </div>

        <div className="company_info_row">
          <label className="placeholder col-2 mb-2 d-block"></label>
          <div className="event_tags">
            <ul className="list_stye_none d-flex flex-wrap gap-2">
              <li className="placeholder col-3 rounded-pill py-2"></li>
              <li className="placeholder col-3 rounded-pill py-2"></li>
              <li className="placeholder col-2 rounded-pill py-2"></li>
            </ul>
          </div>
        </div>
      </div>

      <div className="event_user grps_user my-3">
        <ul className="list_stye_none d-flex align-items-center gap-2">
          {[...Array(5)].map((_, i) => (
            <li key={i}>
              <div
                className="placeholder rounded-circle"
                style={{ width: "40px", height: "40px" }}
              ></div>
            </li>
          ))}
          <li>
            <div className="placeholder col-4 py-2"></div>
          </li>
        </ul>
      </div>

      <div className="details_btn">
        <div className="btn outline placeholder col-3 py-3"></div>
      </div>
    </div>
  );
};

export default GroupListPlaceHolder;
