/* eslint-disable */
import React, { useState } from "react";
import Select from "react-select";
import { useParams } from "react-router-dom";

/*------ Import API URLs and utility functions ------*/
import * as url from "helper/UrlHelper";
import { postData } from "utils/Gateway";

/*------ Import data for select options ------*/
import contactOptions from "Data/ContactOptions.json";
import relationOptions from "Data/RelationOptions.json";

/**
 * @component ConnectModal
 * @description A modal component for sending connection requests to other users.
 * It allows users to select a contact type, a reason for connection, and compose a message.
 * @param {object} props - The component props.
 * @param {function} props.afterModalClose - Callback function to be executed after the modal is closed.
 */
const ConnectModal = ({ afterModalClose }) => {
  /** @type {string} token - User authentication token from local storage. */
  const token = localStorage.getItem("token");
  /** @type {object} userInfo - User information object from local storage. */
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  /** @type {object} params - URL parameters, used to get the target member's ID. */
  const params = useParams();

  /* ---- State management for form inputs ---- */
  /** @type {[object, function]} selectContactOption - State for the selected contact type option. */
  const [selectContactOption, setSelectContactOption] = useState(null);
  /** @type {[object, function]} selectKnowingOption - State for the selected relation/reason option. */
  const [selectKnowingOption, setSelectKnowingOption] = useState(null);
  /** @type {[string, function]} connectMessage - State for the connection message text. */
  const [connectMessage, setConnectMessage] = useState("");
  /** @type {[boolean, function]} isSending - State to indicate if the request is currently being sent. */
  const [isSending, setIsSending] = useState(false);

  /* ---- State management for validation flags ---- */
  /** @type {[boolean, function]} contactOptionError - State for contact type selection validation error. */
  const [contactOptionError, setContactOptionError] = useState(false);
  /** @type {[boolean, function]} knowingOptionError - State for relation/reason selection validation error. */
  const [knowingOptionError, setKnowingOptionError] = useState(false);
  /** @type {[boolean, function]} connectMessageError - State for connection message validation error. */
  const [connectMessageError, setConnectMessageError] = useState(false);

  /**
   * @function validateFields
   * @description Validates all required fields in the form.
   * Sets respective error states and returns true if all fields are valid, false otherwise.
   * @returns {boolean} - True if all fields are valid, false otherwise.
   */
  const validateFields = () => {
    const contactError = !selectContactOption;
    const knowingError = !selectKnowingOption;
    const messageError = !connectMessage.trim();

    setContactOptionError(contactError);
    setKnowingOptionError(knowingError);
    setConnectMessageError(messageError);

    return !(contactError || knowingError || messageError);
  };

  /**
   * @function sendConnectHandler
   * @description Handles the submission of the connection request.
   * Validates fields, sends data to the API, and manages modal display based on response.
   */
  const sendConnectHandler = async () => {
    // Validate fields before sending the request
    const isValid = validateFields();

    if (isValid) {
      setIsSending(true); // Set sending state to true to show loading indicator
      try {
        /** @type {object} connectData - Data payload for the connection request. */
        let connectData = {
          member: params.id,
          reason: selectContactOption?.value ? selectContactOption?.value : "",
          source: selectKnowingOption?.value ? selectKnowingOption?.value : "",
          message: connectMessage,
        };

        /** @type {string} requestURL - The full URL for the API request. */
        let requestURL =
          url.API_BASE_URL + url.API_SEND_REQUEST_USER + `?token=${token}`;

        const response = await postData(requestURL, connectData);

        setIsSending(false); // Reset sending state
        if (response.status) {
          // Close the current modal
          resetHandler(); // Reset form fields
          let myModal = document.querySelector("#send_rqst_mdl");
          let modal = bootstrap.Modal.getInstance(myModal);
          modal.hide();

          // Show the greeting modal upon successful request
          let greetingModal = new bootstrap.Modal(
            document.getElementById("gretings_mdl")
          );
          greetingModal.show();
        }
      } catch (error) {
        // Log or handle error appropriately
        console.error("Failed to send connection request:", error.message);
      }
    }
  };

  /**
   * @function resetHandler
   * @description Resets all form fields and validation error flags to their initial states.
   */
  const resetHandler = () => {
    setSelectContactOption(null);
    setSelectKnowingOption(null);
    setConnectMessage("");
    setContactOptionError(false);
    setKnowingOptionError(false);
    setConnectMessageError(false);
  };

  /**
   * @description Custom styles for the React-Select component to apply border on error.
   * @param {boolean} hasError - True if there is a validation error for the select component.
   * @returns {object} - Style object for React-Select.
   */
  const customStyles = (hasError) => ({
    control: (base, state) => ({
      ...base,
      borderColor: hasError ? "#dc3545" : "var(--bs-light-gray)", // Red border for error
      boxShadow: hasError
        ? "0 0 0 0.25rem rgba(220, 53, 69, 0.25)"
        : base.boxShadow, // Red shadow for error
      "&:hover": {
        borderColor: hasError ? "#dc3545" : base["&:hover"].borderColor,
      },
      padding: "3px",
      minHeight: "48px", // Added min-height for better appearance
      boxShadow: "0 !important",
      "&:hover": {}, // Focus shadow effect
    }),
  });

  return (
    /** @div Main container for the connection request modal. */
    /**
     * Main container for the connection request modal.
     * Uses enhanced styling and professional layout.
     */
    <div className="post_modal job_modal conatct_modal">
      <div
        className="modal fade"
        id="send_rqst_mdl"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog">
          <div className="modal-content">
            {/* Modal header with professional layout */}
            <div className="modal-header">
              <div className="owner_hdngOuter">
                <h5>
                  <b>Send Connection Request</b>
                  <span className="online"></span>
                </h5>
                <button
                  type="button"
                  className="close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  onClick={resetHandler}
                >
                  <span className="material-icons">close</span>
                </button>
              </div>
              <p className="text-muted mt-2 fs-14">
                Build your professional network with meaningful connections
              </p>
            </div>

            {/* Modal body with enhanced form styling */}
            <div className="modal-body">
              <div className="owner_detailsOuter">
                {/* Contact Type Selection */}
                <div className="input_fill">
                  <label className="form-label">Contact Type</label>
                  <Select
                    placeholder="Select contact type (e.g., Business Inquiry)"
                    options={contactOptions}
                    value={selectContactOption}
                    onChange={(val) => {
                      setSelectContactOption(val);
                      setContactOptionError(false);
                    }}
                    styles={customStyles(contactOptionError)}
                    classNamePrefix="react-select"
                    className="select2-container"
                  />
                  {contactOptionError && (
                    <div className="invalid-feedback d-block">
                      Please select a valid contact type to proceed.
                    </div>
                  )}
                </div>

                {/* Relationship/Reason Selection */}
                <div className="input_fill">
                  <label className="form-label">Relationship or Reason</label>
                  <Select
                    placeholder="Select relationship or reason"
                    options={relationOptions}
                    value={selectKnowingOption}
                    onChange={(val) => {
                      setSelectKnowingOption(val);
                      setKnowingOptionError(false);
                    }}
                    styles={customStyles(knowingOptionError)}
                    classNamePrefix="react-select"
                    className="select2-container"
                  />
                  {knowingOptionError && (
                    <div className="invalid-feedback d-block">
                      Please select a reason for your connection.
                    </div>
                  )}
                </div>

                {/* Message Textarea */}
                <div className="description_fill input_fill">
                  <label className="form-label">Message</label>
                  <textarea
                    className={`form-control ${
                      connectMessageError ? "is-invalid" : ""
                    }`}
                    placeholder="Write a personalized message explaining why you'd like to connect..."
                    value={connectMessage}
                    onChange={(e) => {
                      setConnectMessage(e.target.value);
                      setConnectMessageError(false);
                    }}
                    rows="4"
                  ></textarea>
                  {connectMessageError && (
                    <div className="invalid-feedback d-block">
                      Please write a message to send with your connection
                      request.
                    </div>
                  )}
                </div>

                {/* Action Button */}
                <div className="mt-4 d-grid">
                  <button
                    className="btn btn-primary"
                    disabled={isSending}
                    style={{
                      height: "48px",
                    }}
                    onClick={sendConnectHandler}
                  >
                    {isSending ? (
                      <>
                        <div
                          className="spinner-border spinner-border-sm me-2"
                          role="status"
                        >
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <span className="material-icons-outlined me-2">
                          send
                        </span>
                        Send Request
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectModal;
