/* eslint-disable */
import React, { useEffect } from "react";
import JobDetailsBody from "components/JobsComponents/JobDetailsComponents/JobDetailsBody/JobDetailsBody";
import AuthHeader from "components/common/AuthHeader";

const JobDetails = () => {
  useEffect(() => {
    document.title = "Job Details";
  }, []);

  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ------- body section ------------ */}
      <JobDetailsBody />
    </>
  );
};

export default JobDetails;
