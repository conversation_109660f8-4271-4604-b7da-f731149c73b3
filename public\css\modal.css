/* ===contact--modall-start */

.owner_hdngOuter{width:100%;display:flex;align-items:center;justify-content:space-between}
.owner_hdngOuter h5{font-size:15px;color:var(--bs-color-1D);font-weight:500}
.owner_hdngOuter h5 b{font-weight:600;font-size:16px}
.modal-header .owner_hdngOuter .close{padding:0;margin:0;font-size:18px;color:var(--bs-color-8E);background-color:transparent}
.owner_hdngOuter h5 span{display:inline-block;margin-left:5px;height:10px;width:10px;background:var(--bs-color-ED);border-radius:50%}
.owner_hdngOuter h5 span.online{background-color:var(--bs-color-31)}
.owner_detailsOuter .input_fill{margin-bottom:10px}
.owner_detailsOuter .input_fill:last-child{margin-bottom:0}.conatct_modal .modal-dialog{max-width:375px}
.description_fill.input_fill .form-control {height: 103px;resize: none;padding-bottom: 15px;background-color: var(--bs-color-ED); padding: 15px 35px 0 20px;font-size: 15px; color: var(--bs-color-1D);font-weight: 400;border-radius: 8px;border-color: transparent;box-shadow: none;}

/* ===end--contact--modal=------ */

.job_modal .modal-dialog{max-width:996px;margin:1.75rem auto}
.job_modal .modal-header{background:var(--bs-color-F7);border-radius:10px 10px 0 0;position:relative;display:block;padding:40px 30px 25px;border:0}
.job_modal .modal-header .close{position:absolute;height:34px;width:34px;background-color:var(--bs-color-ED);border-radius:8px;right:20px;font-size:18px;color:var(--bs-color-8E);top:20px;padding:0;margin:0;display:flex;align-items:center;justify-content:center;z-index:99;opacity:1}
.job_modal .company_logotxt span{font-weight:500}
.job_modal .company_logotxt{margin-bottom:10px}
.modal_hdng h3{font-size:24px;color:var(--bs-color-1D);font-weight:500}
.job_modal .job_tags{margin-bottom:0}
.job_modal .modal-body{padding:0}
.prsnl_detailsArea{border-bottom:1px solid var(--bs-color-ED);padding:30px 40px;background-color:var(--bs-color-FF)}
.jobinput_fill label{font-weight:600;color:var(--bs-color-1D)}
.prsnl_detailsArea .row{margin:0 -5px -20px}
.prsnl_detailsArea .col-sm-6{padding:0 5px}
.prsnl_detailsArea .jobinput_fill{margin-bottom:20px}
.others_detailsArea{padding:30px 40px 30px}
.details_row{margin-bottom:10px}
.details_row:last-child{margin-bottom:0}
.details_input_txt label{margin:0;color:var(--bs-color-1D);font-weight:600;font-size:14px}
.details_row .input_fill{margin-bottom:0;position:relative}
.details_row .row{align-items:center}
.description_fill.input_fill .form-control{height:103px;resize:none;padding-top:15px;padding-bottom:15px}
.job_fillOuter{display:flex;flex-wrap:wrap;margin:0 -6px}
.job_fillOuter .jobinput_fill{width:50%;padding:0 6px}
.attached_row{padding:15px 0}
.upload_area{display:flex;flex-wrap:wrap;margin:0 -5px}
.upload_item{padding:0 5px}
.uploaded_innrBx,.upload_btn{height:135px;width:135px;border:2px solid var(--bs-color-ED);border-radius:8px;position:relative;padding:20px 10px;text-align:center}
.upload_icon{margin-bottom:15px}
.upload_txt h4{font-size:13px;color:var(--bs-color-1D);font-weight:500;margin-bottom:10px}
.upload_txt h5{font-size:10px;color:var(--bs-color-8E)}
.delete_btn{position:absolute;right:10px;top:10px}
.delete_btn span{color:var(--bs-color-CE)}
.delete_btn span:hover{color:red}
.upload_btn{border:2px dashed var(--bs-color-ED)}
.file_uoload{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;opacity:0;cursor:pointer}
.link_icon{background-image:url(../images/link.png);background-position:right 12px top 50%;background-repeat:no-repeat}.copy_link{right:12px;top:50%;position:absolute;transform:translateY(-50%);line-height:100%;color:var(--bs-color-8E)}
.radio-check{margin:0;cursor:pointer;display:flex}
.radio-check span{display:inline-block;vertical-align:middle}
.radio-check input[type='checkbox'],.radio-check input[type='radio']{display:none}.radio-check input[type='checkbox']+span{width:19px;min-width:19px;height:19px;border-radius:4px;border:2px solid var(--bs-color-CE);position:relative;margin-right:10px}
.radio-check input[type='checkbox']:checked+span:after{content:"";position:absolute;left:1px;top:1px;right:1px;bottom:1px;background:url(../images/check.png) no-repeat center center;background-size:10px}
.radio-check p{font-size:15px;color:var(--bs-color-1D);font-weight:500}.chek_row{padding:15px 0}
.feedback_usrTop {padding: 30px 40px;border-bottom: 1px solid var(--bs-color-ED);}
.others_detailsArea {padding: 30px 40px 30px;}
.evnt_feedbackrow .details_input_txt label {font-size: 16px; margin-bottom: 10px;}
.gray_rating {display: flex;align-items: center; gap: 5px;}
.details_row .input_fill {margin-bottom: 0;position: relative;}
.gray_rating li a {color: var(--bs-color-ED);display: inline-block;}
.gray_rating li.active a{color: var(--bs-color-DB);}
.gray_rating li a i{font-size: 40px;}
.expert_invite .modal-body {padding: 30px;}
.modal_hdng h5 {font-size: 16px;color: var(--bs-color-1D);font-weight: 400;margin-bottom: 10px;}
.modal_hdng h5 span {font-weight: 600;color: var(--bs-color-DB);}
.invite_msgBody .description_fill .form-control {height: 325px;}
.question_mdl .modal-body {padding: 30px 40px;}
.progess_outer {min-width: 100px;background-color: var(--bs-color-ED); height: 10px; border-radius: 90px; position: relative;}
.progress_bar { position: absolute;top: 0;left: 0; bottom: 0;background-color: var(--bs-color-DB);border-radius: 90px;}
 .modal_progres {display: flex;align-items: center;}
 .modal_progres label {font-size: 15px;color: var(--bs-color-1D);margin: 0;font-weight: 500;margin-right: 10px;}
.evnt_feedbackrow .description_fill.input_fill .form-control, .evnt_feedbackrow .select2-container--default .select2-selection--single{background-color: var(--bs-color-FF);border: 1px solid var(--bs-color-DD);box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);}

/* ===review--modal--start */
.review_modal .modal-header{background-color: var(--bs-color-F1);padding: 24px 15px;}
.form_commnts_area.commnts_newbx{background-color: var(--bs-color-F1);}
.course_rvw_row {border-bottom: 1px solid var(--bs-color-E5);padding: 0 15px 20px; margin-bottom: 20px;display: flex;align-items: center;}
.rating_hdng h5 {font-size: 16px;color: var(--bs-color-1D);font-weight: 600;}
.course_rvw_row ul {display: flex; align-items: center; margin-left: 15px;gap: 5px;}
.course_rvw_row ul li {line-height: 100%;}
.course_rvw_row ul li a {font-size: 38px; display: block;color: var(--bs-color-CE);}
.course_rvw_row ul li a:focus, .course_rvw_row ul li.active_star a {color: var(--bs-color-DB);}
.commnts_send_innr{padding: 0 15px 20px;}
.commnts_send_innr .rating_hdng {margin-bottom: 15px;}
.commnts_sendArea {border: 1px solid var(--bs-color-DD);box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);border-radius: 8px;background-color: var(--bs-color-FF);}
.cmnt_btn {cursor: pointer;color: var(--bs-color-DB);background-color: transparent;}
/* ===review--modal--end */
/* ==success-modal */
.success_graphics{text-align: center;margin-bottom: 30px;}
.success_text{text-align: center;}
.success_text h3{font-weight: 700;font-size: 22px;line-height: 26px;color: var(--bs-color-00);margin-bottom: 8px;}
.success_text p{font-weight: 500;font-size: 16px;line-height: 26px;color: var(--bs-color-00);}
.success_text .basic_btn{margin-top: 20px;}
/* -----end */
/* ===start--tags--modal=== */
.chat_modalbx .modal-content {background: var(--bs-color-F1);box-shadow: 0px 10px 150px rgba(0, 0, 0, 0.02);border-radius: 8px;}
.mdl_hdng h5{font-size:18px;color:var(--bs-color-1D);font-weight:500;margin:0}
.mdl_hdng .close{height:34px;width:34px;background-color:var(--bs-color-ED);border-radius:8px;right:20px;font-size:18px;color:var(--bs-color-8E);top:20px;padding:0;margin:0;display:flex;align-items:center;justify-content:center;z-index:99;opacity:1}
.group_srchfill {position: relative;margin-bottom: 15px;}
.group_srchfill .form-control{height:50px;background-color:#fff;border:1px solid var(--bs-color-DD);border-radius:8px;padding:0 15px 0 40px;background-image:url(../images/search.svg);background-position:left 10px top 50%;background-repeat:no-repeat;box-shadow:none;font-size:16px;color:var(--bs-color-97);box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);}
.tag_links {}
.add_icon{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:var(--bs-color-8E)}
.add_icon:hover{color: var(--bs-color-DB);}
.tag_links ul {display: flex;flex-wrap: wrap;gap: 5px 5px;}
.tag_links ul li a{display:inline-flex;align-items:center;justify-content:center;height:40px;font-size:14px;color:var(--bs-color-00);padding:0 20px;background-color:var(--bs-color-F4);border:1px solid var(--bs-color-6F);border-radius: 8px;}
.add_btn .basic_btn{width: 100%;gap: 10px;}
.group_innrScroll {height: 330px;padding-bottom: 10px;overflow-y: auto;}
.tag_links ul li a:hover{background-color: var(--bs-color-DB);color: var(--bs-color-FF);}

/* ==success-modal */
@media(max-width:991px){
    .feedback_usrTop, .job_modal .modal-header,.others_detailsArea, .question_mdl .modal-body{padding: 20px;}
    .modal_hdng h4{font-weight: 500;font-size: 18px;}
    .modal_hdng h3{font-size: 19px;}
}

























