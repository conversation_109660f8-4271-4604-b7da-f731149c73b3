import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
/*import url and gateway methods */
import { getData } from "utils/Gateway";
import * as url from "helper/UrlHelper";
import FilterPopup from "./Popup/FilterPopup";

const AssesmentList = () => {

    const token = localStorage.getItem("token"); // user token after login

    const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [isLoading, setIsLoading] = useState(false);
  const [assessments, setAssessments] = useState([]);
  const [cognitiveSkills, setCognitiveSkills] = useState([]);
  
  const [errorMessage, setErrorMessage] = useState('');
  const [fetchingAccessToken, setFetchingAccessToken] = useState(false);
  const [selectedAssessmentKey, setSelectedAssessmentKey] = useState('');

    const [userToken, setUserToken] = useState(localStorage.getItem("cognifitToken") || '');
    const [isCognifitConnected, setIsCognifitConnected] = useState(localStorage.getItem("cognifitToken") ? true : false);
    const [accessToken, setAccessToken] = useState('');
    const [clientId, setClientId] = useState('');

    const [filteredAssessments, setFilteredAssessments] = useState([]);


    // connect to Cognifit
    const connectCognifit = async () => {
        setUserToken('');
        setAccessToken('');
        setIsCognifitConnected(false);

        try {
            let requestURL = url.API_BASE_URL + url.API_COGNIFIT_CONNECT + `?token=${token}`;

            console.log('requestURL assesment list: ', requestURL);

            // get token using post method
            const response = await getData(requestURL);

            console.log('Response for connectCognifit: ', response);

            if (response.token) {
                setUserToken(response.token);
                setIsCognifitConnected(true);
                localStorage.setItem("cognifitToken", response.token);
            }

        } catch (error) {
            console.log(error.message);
            setErrorMessage('Error connecting to Cognifit. Please try again.');
        }
    }


    // get the cognifit token
    const getCognifitToken = async (assesmentKey) => {

      setFetchingAccessToken(true);
      setErrorMessage('');

      setSelectedAssessmentKey(assesmentKey);

      try {
        let requestURL = url.API_BASE_URL + url.API_COGNIFIT_ACCESS_TOKEN + `?token=${token}`;
  
        console.log('requestURL braingame list: ', requestURL);
  
        // get token using post method
        const response = await getData(requestURL);
  
        console.log('Response for getCognifitToken: ', response);
  
        if (response.token) {
          setAccessToken(response.accesstoken);
          localStorage.setItem("cognifitAccessToken", response.accesstoken);
          console.log("Cognifit Access Token: ", response.accesstoken);
        }else{
          setErrorMessage('Failed to fetch Cognifit token. Please try again.');
          console.error("Error fetching Cognifit token: ", response);
        }
  
      } catch (error) {
        console.log(error.message);
      } finally {
        setFetchingAccessToken(false);
      }
  
    };

    // fetch assessments from Cognifit
    const getAssessments = async () => {
        setIsLoading(true);
        setErrorMessage('');
        try {
        let requestURL = url.API_BASE_URL + url.API_COGNIFIT_ACTIVITY_LIST + `?token=${token}&type=assessment`;
            const response = await getData(requestURL);

            console.log('Response for getAssessments: ', response.data);

            if (response && response.data) {
                setAssessments(response.data);
                setClientId(response.clientid);
            } else {
            setErrorMessage('Failed to fetch assessments.');
            }

        } catch (error) {
        setErrorMessage('Error fetching assessments.');
        } finally {
        setIsLoading(false);
        }
    };

    // fetch congnitive skills
    const getCognitiveSkills = async () => {

        try {
            let requestURL = url.API_BASE_URL + url.API_COGNIFIT_SKILLS + `?token=${token}`;

            const response = await getData(requestURL);

            console.log('Response for getCognitiveSkills: ', response);

            if (response && response.data) {
                setCognitiveSkills(response.data);
            }

        } catch (error) {
            setErrorMessage('Error fetching cognitive skills.');
        }   

    };

    // Filter handler for FilterPopup
    const handleSkillFilter = (selectedSkillKeys) => {
      if (!selectedSkillKeys || selectedSkillKeys.length === 0) {
        setFilteredAssessments(assessments);
        return;
      }
      // Filter assessments that have at least one of the selected skills in their skills array
      setFilteredAssessments(
        assessments.filter((assessment) =>
          assessment.skills &&
          assessment.skills.some((skill) => selectedSkillKeys.includes(skill))
        )
      );
    };

    // Helper to map skill keys to their Spanish names using cognitiveSkills
    const getSkillNames = (skillKeys) => {
      if (!Array.isArray(skillKeys) || !cognitiveSkills || cognitiveSkills.length === 0) return [];
      return skillKeys.map(
        (key) =>
          cognitiveSkills.find((s) => s.key === key)?.assets?.titles?.es || key
      );
    };

    // Update filteredAssessments when assessments change or are fetched
    useEffect(() => {
      setFilteredAssessments(assessments);
    }, [assessments]);

    // if user is connected to Cognifit, fetch assessments
    useEffect(() => {
        if(isCognifitConnected && userToken) {
            getCognitiveSkills();

            getAssessments();
        }
    }, [isCognifitConnected, userToken]);

    // if user has access token and client ID, redirect to Cognifit launch URL
    useEffect(() => {

        console.log('useeffect clientId: ', clientId, 
          'accessToken: ', accessToken, 
          'selectedAssessmentKey: ', selectedAssessmentKey);

        if(clientId && accessToken && selectedAssessmentKey) {
          window.location.href = `${url.COGNIFIT_LAUNCH_URL}/launch?clientId=${clientId}&accessToken=${accessToken}&assessmentKey=${selectedAssessmentKey}`;
        } 
    }, [clientId, accessToken, selectedAssessmentKey]);

    

  return (
    <section className="">
      <div className="container-fluid">
        <div className="faq_pnl_wrapper">
          <div className="shadow-sm p-4 rounded-10 bg-white mb-3">
            {errorMessage && (
              <div className="alert alert-danger" role="alert">
                {errorMessage}
              </div>
            )}
            <div className="d-flex align-items-center justify-content-between mb-3">
              <h2 className="fs-xl text-black mb-0 fw-semibold">Evaluaciones cognitivas</h2>
              {isCognifitConnected && (
                <div className="d-flex ms-auto gap-2">
                  <Link
                    to="#"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#cognifit_filter_offcanvas"
                    role="button"
                    aria-controls="cognifit_filter_offcanvas"
                    className="btn btn-outline-secondary d-flex align-items-center"
                  >
                    <i className="material-icons-outlined me-1">tune</i>
                    <span>Filtrar</span>
                  </Link>
                  <Link
                    to="/cognifit/report"
                    className="btn btn-outline-secondary d-flex align-items-center"
                  >
                    <i className="material-icons-outlined me-1">assessment</i>
                    <span>Informe</span>
                  </Link>
                </div>
              )}
            </div>
            {/* FilterPopup Offcanvas */}
            <FilterPopup cognitiveSkills={cognitiveSkills} onFilter={handleSkillFilter} />
            {!isCognifitConnected ? (
              <div className="text-center my-4">
                <button
                  className="btn btn-primary"
                  onClick={connectCognifit}
                  disabled={isLoading}
                >
                  Conectar con Cognifit
                </button>
              </div>
            ) : (
              <div className="row">
                {isLoading ? (
                  Array.from({ length: 2 }).map((_, idx) => (
                    <div className="col-md-6 mb-4" key={idx}>
                      <div className="card h-100 shadow-sm d-flex flex-row align-items-center p-3">
                        <div className="placeholder-glow" style={{ width: 120, height: 120, borderRadius: 8, background: "#e0e0e0" }} />
                        <div className="flex-grow-1 ms-3">
                          <div className="placeholder-glow mb-2" style={{ height: 24, width: "60%" }}>
                            <span className="placeholder col-8"></span>
                          </div>
                          <div className="placeholder-glow mb-2" style={{ height: 18, width: "40%" }}>
                            <span className="placeholder col-6"></span>
                          </div>
                          <div className="placeholder-glow mb-2" style={{ height: 16, width: "30%" }}>
                            <span className="placeholder col-4"></span>
                          </div>
                          <div className="placeholder-glow mb-2" style={{ height: 16, width: "90%" }}>
                            <span className="placeholder col-10"></span>
                          </div>
                          <div className="placeholder-glow mb-2" style={{ height: 16, width: "80%" }}>
                            <span className="placeholder col-8"></span>
                          </div>
                          <div className="placeholder-glow" style={{ height: 32, width: "30%" }}>
                            <span className="placeholder col-4"></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : filteredAssessments && filteredAssessments.length > 0 ? (
                  filteredAssessments.map((assessment) => (
                    <div className="col-md-6 mb-4" key={assessment.key}>
                      <div className="card h-100 shadow-sm p-3 d-flex flex-column justify-content-between">
                        {/* Section 1: Image and Title/Key/Time */}
                        <div className="d-flex align-items-center mb-3">
                          <img
                            src={assessment.assets?.images?.scareIconZodiac}
                            alt={assessment.assets?.titles?.es}
                            className="rounded"
                            style={{ width: 110, height: 110, objectFit: "cover", flexShrink: 0 }}
                          />
                          <div className="ms-4 flex-grow-1">
                            <h5 className="mb-1 fw-semibold" style={{ fontSize: "1.2rem" }}>
                              {assessment.assets?.titles?.es}
                            </h5>
                            <ul className="list-unstyled mb-0 d-flex flex-wrap gap-2" style={{ fontSize: "0.98em" }}>
                              <li>
                                <span className="badge bg-secondary">{assessment.key}</span>
                              </li>
                              <li>
                                <span className="badge bg-info text-dark">
                                  Tiempo estimado: {assessment.estimatedTime ? Math.round(assessment.estimatedTime / 60) + " min" : "N/A"}
                                </span>
                              </li>
                            </ul>
                          </div>
                        </div>
                        {/* Section 2: Description, Skills, Tasks */}
                        <div className="mb-3">
                          <div className="mb-2 text-muted" style={{ fontSize: "0.98em" }}>
                            {assessment.assets?.descriptions?.es
                              ? assessment.assets.descriptions.es.slice(0, 200) + (assessment.assets.descriptions.es.length > 200 ? "..." : "")
                              : ""}
                          </div>
                          <div className="mb-2">
                            <strong>Habilidades:</strong>{" "}
                            <span className="text-muted">
                              {assessment.skills && assessment.skills.length > 0
                                ? getSkillNames(assessment.skills).join(", ")
                                : "N/A"}
                            </span>
                          </div>
                          <div>
                            <strong>Tareas:</strong>
                            <ul className="mb-1" style={{ fontSize: "0.97em", paddingLeft: "18px" }}>
                              {assessment.tasks && assessment.tasks.slice(0, 5).map((task, idx) => (
                                <li key={idx}>{task}</li>
                              ))}
                              {assessment.tasks && assessment.tasks.length > 5 && (
                                <li>...y {assessment.tasks.length - 5} más</li>
                              )}
                            </ul>
                          </div>
                        </div>
                        {/* Section 3: Action Button */}
                        <div className="mt-auto">
                          <Link
                            to="#"
                            className="btn btn-primary btn-sm px-4"
                            onClick={() => getCognifitToken(assessment.key)}
                            disabled={fetchingAccessToken}
                            style={fetchingAccessToken ? { pointerEvents: "none", opacity: 0.7 } : {}}
                          >
                            Comenzar
                          </Link>
                          {fetchingAccessToken && selectedAssessmentKey === assessment.key && (
                            <span className="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-12">
                    <p>No se encontraron evaluaciones.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AssesmentList;
