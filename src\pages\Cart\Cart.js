/* eslint-disable */
import AuthHeader from "components/common/AuthHeader";
import React, { useContext, useEffect } from "react";
//import context
import { GlobalProvider } from "context/GlobalContext";
import CartDetails from "components/CartComponents/CartDetails/CartDetails";

const Notifications = () => {
  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ------ body section --------- */}
      <CartDetails />
    </>
  );
};

export default Notifications;