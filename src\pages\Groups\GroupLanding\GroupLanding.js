/* eslint-disable */
import AuthHeader from "components/common/AuthHeader";
import React, { useState, useContext, useEffect } from "react";

import GroupLandingBody from "components/GroupsComponents/GroupLandingComponents/GroupLandingBody/GroupLandingBody";

const GroupLanding = () => {
  useEffect(() => {
    document.title = "Group";
  }, []);

  return (
    <>
      <AuthHeader moduleName={"group"} />

      <GroupLandingBody />
    </>
  );
};

export default GroupLanding;
