/* eslint-disable */
import AuthHeader from "components/common/AuthHeader";
import React, { useContext, useEffect } from "react";
//import context
import { GlobalProvider } from "context/GlobalContext";
import PaymentSuccess from "components/PaymentComponents/Success/Success";

const Success = () => {
  return (
    <>
      <header className="aftre_login_header">
        <AuthHeader />
      </header>

      {/* ------ body section --------- */}
      <PaymentSuccess />
    </>
  );
};

export default Success;