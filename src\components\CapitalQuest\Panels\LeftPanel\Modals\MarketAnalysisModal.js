/* eslint-disable */
import { useState, useEffect } from "react";

const MarketAnalysisModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });

  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      marketsize: formData.marketsize,
      markettrends: formData.markettrends,
      targetmarket: formData.targetmarket,
      competitors: formData.competitors,
      competitiveadvantage: formData.competitiveadvantage,
      entrybarriers: formData.entrybarriers,
      differentiationstrategy: formData.differentiationstrategy,
      customeracquisitionstrategy: formData.customeracquisitionstrategy
    }));
    let modal = document.querySelector("#market_analysis_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="market_analysis_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Market Analysis</h3>
                <h5>Provide details about your market analysis</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-icons-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Market Size</label>
                    <textarea
                      className="form-control"
                      name="marketsize"
                      value={formData.marketsize || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Market Size"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Market Trends</label>
                    <textarea
                      className="form-control"
                      name="markettrends"
                      value={formData.markettrends || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Market Trends"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Target Market</label>
                    <textarea
                      className="form-control"
                      name="targetmarket"
                      value={formData.targetmarket || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Target Market"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Competitors</label>
                    <textarea
                      className="form-control"
                      name="competitors"
                      value={formData.competitors || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Competitors"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Competitive Advantage</label>
                    <textarea
                      className="form-control"
                      name="competitiveadvantage"
                      value={formData.competitiveadvantage || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Competitive Advantage"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Entry Barriers</label>
                    <textarea
                      className="form-control"
                      name="entrybarriers"
                      value={formData.entrybarriers || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Entry Barriers"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Differentiation Strategy</label>
                    <textarea
                      className="form-control"
                      name="differentiationstrategy"
                      value={formData.differentiationstrategy || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Differentiation Strategy"
                    ></textarea>
                  </div>
                  <div className="form-group">
                    <label>Customer Acquisition Strategy</label>
                    <textarea
                      className="form-control"
                      name="customeracquisitionstrategy"
                      value={formData.customeracquisitionstrategy || ""}
                      onChange={handleInputChange}
                      placeholder="Enter Customer Acquisition Strategy"
                    ></textarea>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketAnalysisModal;
