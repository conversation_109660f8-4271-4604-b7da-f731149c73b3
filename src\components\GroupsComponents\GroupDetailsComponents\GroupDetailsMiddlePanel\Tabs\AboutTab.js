/* eslint-disable */
import React, { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import { fileDownloadHandler } from "utils/DownloadFile";

const AboutTab = ({ tabName = "" }) => {
  const { t } = useTranslation(); // translation marker
  const params = useParams();
  const token = localStorage.getItem("token");

  const [isLoading, setIsLoading] = useState(false);
  const [groupDetails, setGroupDetails] = useState({});

  //function for get event details
  const getGroupDetails = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL +
        url.API_GET_GROUP_DETAILS +
        `/${params.id}` +
        `?token=${token}&modulefrom=about`;

      // console.log("requestURL in about tab", requestURL);

      const response = await getData(requestURL);

      // console.log("event details response in left panel", response);

      setIsLoading(false);

      if (response.status) {
        setGroupDetails(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "about") {
      getGroupDetails();
    }
  }, [params.id, tabName]);

  if (isLoading) {
    return (
      <div className="people_detailspnl">
        {/* Summary Section */}
        <div className="people_details_bx white_shadow_bx mb-4">
          <div className="details_hdng mb-3">
            <h4 className="placeholder-glow">
              <span className="placeholder col-3"></span>
            </h4>
          </div>
          <div className="results_para">
            <p className="placeholder-glow">
              <span className="placeholder col-12"></span>
              <span className="placeholder col-12"></span>
              <span className="placeholder col-10"></span>
              <span className="placeholder col-8"></span>
            </p>
          </div>
        </div>

        {/* Details Section */}
        <div className="people_details_bx white_shadow_bx mb-4">
          <div className="details_hdng mb-3">
            <h4 className="placeholder-glow">
              <span className="placeholder col-3"></span>
            </h4>
          </div>
          <div className="results_para">
            <p className="placeholder-glow">
              <span className="placeholder col-12"></span>
              <span className="placeholder col-12"></span>
              <span className="placeholder col-11"></span>
              <span className="placeholder col-10"></span>
              <span className="placeholder col-9"></span>
              <span className="placeholder col-12"></span>
            </p>
          </div>
        </div>

        {/* Accordion Section */}
        <div className="people_details_bx white_shadow_bx mb-4">
          <div className="accordian_area">
            <div className="accordion accordion-flush" id="faq-accordian">
              {/* Category Accordion */}
              <div className="accordion-item">
                <h2 className="accordion-header">
                  <button
                    className="accordion-button collapsed placeholder-glow"
                    type="button"
                  >
                    <span className="placeholder col-2"></span>
                  </button>
                </h2>
                <div className="accordion-collapse collapse">
                  <div className="accordion-body">
                    <div className="experts_skill">
                      <ul className="list_stye_none">
                        {[...Array(3)].map((_, i) => (
                          <li key={i} className="mb-2">
                            <div className="placeholder-glow">
                              <span className="placeholder col-4"></span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Interest Accordion */}
              <div className="accordion-item">
                <h2 className="accordion-header">
                  <button
                    className="accordion-button collapsed placeholder-glow"
                    type="button"
                  >
                    <span className="placeholder col-2"></span>
                  </button>
                </h2>
                <div className="accordion-collapse collapse">
                  <div className="accordion-body">
                    <div className="experts_skill">
                      <ul className="list_stye_none">
                        {[...Array(2)].map((_, i) => (
                          <li key={i} className="mb-2">
                            <div className="placeholder-glow">
                              <span className="placeholder col-5"></span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Search Accordion */}
              <div className="accordion-item">
                <h2 className="accordion-header">
                  <button
                    className="accordion-button collapsed placeholder-glow"
                    type="button"
                  >
                    <span className="placeholder col-2"></span>
                  </button>
                </h2>
                <div className="accordion-collapse collapse">
                  <div className="accordion-body">
                    <div className="experts_skill">
                      <ul className="list_stye_none">
                        {[...Array(4)].map((_, i) => (
                          <li key={i} className="mb-2">
                            <div className="placeholder-glow">
                              <span className="placeholder col-3"></span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Docs Section */}
        <div className="people_details_bx white_shadow_bx">
          <div className="details_hdng mb-3">
            <h4 className="placeholder-glow">
              <span className="placeholder col-2"></span>
            </h4>
          </div>

          <div className="d-flex flex-wrap gap-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border rounded-3 p-3 w-100 w-md-auto">
                <div className="d-flex align-items-center gap-3">
                  <div className="placeholder-glow flex-grow-1">
                    <span className="placeholder col-8"></span>
                  </div>
                  <div className="placeholder-glow">
                    <span
                      className="placeholder"
                      style={{ width: "24px", height: "24px" }}
                    ></span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="people_detailspnl">
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Summary")}</h4>
        </div>
        <div className="results_para">
          <p>{groupDetails?.summary}</p>
        </div>
      </div>
      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Details")}</h4>
        </div>
        <div className="results_para">
          <p
            dangerouslySetInnerHTML={{ __html: groupDetails?.description }}
          ></p>
        </div>
      </div>
      <div className="people_details_bx white_shadow_bx">
        <div className="accordian_area">
          <div className="accordion accordion-flush" id="faq-accordian">
            <div className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#flush-collapseOne"
                  aria-expanded="false"
                >
                  <span>{t("Category")}</span>
                </button>
              </h2>
              <div
                id="flush-collapseOne"
                className="accordion-collapse collapse"
                data-bs-parent="#faq-accordian"
              >
                <div className="accordion-body">
                  <div className="experts_skill">
                    <ul className="list_stye_none">
                      {groupDetails?.categories?.map((category, index) => {
                        return (
                          <li key={index}>
                            <Link to="#">{category.name}</Link>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#flush-collapseTwo"
                  aria-expanded="false"
                >
                  <span>{t("Interest")}</span>
                </button>
              </h2>
              <div
                id="flush-collapseTwo"
                className="accordion-collapse collapse"
                data-bs-parent="#faq-accordian"
              >
                <div className="accordion-body">
                  <div className="experts_skill">
                    <ul className="list_stye_none">
                      {groupDetails?.interests?.map((item, index) => {
                        return (
                          <li key={index}>
                            <Link to="#">{item.name}</Link>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className="accordion-item">
              <h2 className="accordion-header">
                <button
                  className="accordion-button collapsed"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#flush-collapseThree"
                  aria-expanded="false"
                >
                  <span>{t("Search")}</span>
                </button>
              </h2>
              <div
                id="flush-collapseThree"
                className="accordion-collapse collapse"
                data-bs-parent="#faq-accordian"
              >
                <div className="accordion-body">
                  <div className="experts_skill">
                    <ul className="list_stye_none">
                      {groupDetails?.searchings?.map((item, index) => {
                        return (
                          <li key={index}>
                            <Link to="#">{item.name}</Link>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="people_details_bx white_shadow_bx">
        <div className="details_hdng">
          <h4>{t("Docs")}</h4>
        </div>

        <ul className="uploaded-files d-flex flex-wrap gap-3 p-0 m-0 mt-3">
          {groupDetails?.uploads?.map((file, index) => {
            const fileObj = {
              ...file,
              path: url.SERVER_URL + file.path,
            };
            return (
              <li
                className="file d-flex gap-3 align-items-center px-3 py-2 border border-gray rounded-3"
                key={index}
              >
                <p className="fw-medium">{file.name}</p>
                <Link to="#" onClick={() => fileDownloadHandler(fileObj)}>
                  <span className="d-block material-icons-outlined">
                    download
                  </span>
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default AboutTab;
