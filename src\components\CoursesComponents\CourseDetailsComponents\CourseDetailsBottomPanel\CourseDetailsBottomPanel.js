/* eslint-disable */
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import AboutTab from "./CourseDetailsBottomTabs/AboutTab/AboutTab";
import LessonsTab from "./CourseDetailsBottomTabs/LessonsTab/LessonsTab";
import DocsTab from "./CourseDetailsBottomTabs/DocsTab/DocsTab";
import TeamTab from "./CourseDetailsBottomTabs/TeamTab/TeamTab";
import ForumTab from "./CourseDetailsBottomTabs/ForumTab/ForumTab";
import EventsTab from "./CourseDetailsBottomTabs/EventsTab/EventsTab";
import ReviewTab from "./CourseDetailsBottomTabs/ReviewTab/ReviewTab";
import ScoreTab from "./CourseDetailsBottomTabs/ScoreTab/ScoreTab";

const CourseDetailsBottomPanel = ({
  courseReviewTabReload = false,
  setCourseReviewTabReload = () => {},
  courseLessonTabReload = false,
  setCourseLessonTabReload = () => {},
}) => {
  const [activeTab, setActiveTab] = useState("about");

  const courseTabs = [
    { name: "about", component: <AboutTab activeTab={activeTab} /> },
    {
      name: "team",
      component: <TeamTab activeTab={activeTab} />,
    },
    {
      name: "lessons",
      component: (
        <LessonsTab
          activeTab={activeTab}
          courseLessonTabReload={courseLessonTabReload}
          setCourseLessonTabReload={setCourseLessonTabReload}
        />
      ),
    },
    {
      name: "review",
      component: (
        <ReviewTab
          activeTab={activeTab}
          courseReviewTabReload={courseReviewTabReload}
          setCourseReviewTabReload={setCourseReviewTabReload}
        />
      ),
    },
    { name: "certificate", component: <ScoreTab activeTab={activeTab} /> },
  ];

  useEffect(() => {
    setActiveTab("about");
  }, []);

  return (
    <div className="coursl_lesson_total">
      <div className="people_item">
        <div className="tab_links people_tablinks">
          <ul className="nav nav-tabs list_stye_none">
            {courseTabs.map((tab) => (
              <li className="nav-item" key={tab.name}>
                <Link
                  className={`nav-link ${
                    activeTab === tab.name ? "active show" : ""
                  }`}
                  to="#"
                  onClick={() => {
                    setActiveTab(tab.name);
                  }}
                >
                  {tab.name.charAt(0).toUpperCase() + tab.name.slice(1)}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="tab-content">
          {courseTabs.map(
            (tab) =>
              activeTab === tab.name && (
                <div key={tab.name}>{tab.component}</div>
              )
          )}
        </div>
      </div>
    </div>
  );
};

export default CourseDetailsBottomPanel;
