
$(function () { var shrinkHeader = 2; $(window).scroll(function () { var scroll = getCurrentScroll(); if (scroll >= shrinkHeader) { $('.home_header').addClass('shrink'); } else { $('.home_header').removeClass('shrink'); } }); function getCurrentScroll() { return window.pageYOffset || document.documentElement.scrollTop; } });


  $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
    $('.slick-slider').slick('setPosition');
  })

$(document).ready(function() {
  $('.select2').select2();
  
    $('.nosearch_select').select2({
      minimumResultsForSearch: -1
  });
  $('#esg_modal').modal('show');
});
$(".profile_owner_editbtn a").click(function(){
  $(".profile_owner_outer").toggleClass("editname_bx");
});

$(".edit_icons").click(function(){
  $(this).parents(".profile_inner_info").toggleClass("add_fiiled")
})


$(".menu_bar a").click(function(){
  $("body").toggleClass("menu_open");
});

$(".overlay, .menu_cross, .menu_txt").click(function(){
  $("body").removeClass("menu_open");
});

$(".hamberger_menu a").click(function(){
  $("body").toggleClass("add_menupnl");
})
$(".overlay, .menu_cross").click(function(){
  $("body").removeClass("add_menupnl");
});

$(".hdr_hamberger a").click(function(){
  $("body").toggleClass("sidebar_open")
})
$(".overlay, .cross_btn").click(function(){
  $("body").removeClass("sidebar_open")
})

$(".search_btn a").click(function(){
  $(".header_serach").stop().slideToggle();
})

$(".start_task_btn a").click(function(){
  $(".task_panl").toggleClass("task_open");
})
$(".task_back .btn, .tsk_btns .outline").click(function(){
  $(".task_panl").removeClass("task_open")
})
$(".lesson_rating_row").click(function(){
  $("body").toggleClass("course_open");
});
$(".back_course").click(function(){
  $("body").removeClass("course_open");
});
$(".filter_btn a").click(function(){
  $("body").toggleClass("filter_open");
})
$(".overlay, .cross_btn").click(function(){
  $("body").removeClass("filter_open");
});
$('.review_slider').slick({
  dots: false,
  infinite: true,
  arrows: true,
  speed: 700,
  slidesToShow: 2,
  slidesToScroll: 1,
  variableWidth: true,
  appendArrows: '.inBtns',
  responsive: [
    {
    breakpoint: 1024,
    settings: {
      slidesToShow: 3,
      slidesToScroll: 3,
    }
    },
    {
    breakpoint: 992,
    settings: {
      slidesToShow: 2,
      slidesToScroll: 1,
    }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
      },
  
    // You can unslick at a given breakpoint now by adding:
    // settings: "unslick"
    // instead of a settings object
  ]
  });

$('.event_slider').slick({
    dots: false,
    infinite: true,
    arrows: true,
    speed: 700,
    slidesToShow: 2,
    slidesToScroll: 1,
    variableWidth: true,
    appendArrows: '.events_arws',
    responsive: [
      {
      breakpoint: 1080,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      }
      },
      {
      breakpoint: 992,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
        },
    
      // You can unslick at a given breakpoint now by adding:
      // settings: "unslick"
      // instead of a settings object
    ]
});
$('.people_slider').slick({
      dots: false,
      infinite: true,
      arrows: true,
      speed: 700,
      slidesToShow: 3,
      slidesToScroll: 1,
      variableWidth: true,
      appendArrows: '.people_arws',
      responsive: [
        {
        breakpoint: 1080,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        }
        },
        {
        breakpoint: 992,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
        },
        {
          breakpoint: 768,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
          }
          },
      
        // You can unslick at a given breakpoint now by adding:
        // settings: "unslick"
        // instead of a settings object
      ]
});
$('.news_slider').slick({
  dots: false,
  infinite: true,
  arrows: true,
  speed: 700,
  slidesToShow: 3,
  slidesToScroll: 1,
  appendArrows: '.inBtns',
  responsive: [
    {
    breakpoint: 1024,
    settings: {
      slidesToShow: 3,
      slidesToScroll: 3,
    }
    },
    {
    breakpoint: 992,
    settings: {
      slidesToShow: 2,
      slidesToScroll: 1,
    }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
      },
  
    // You can unslick at a given breakpoint now by adding:
    // settings: "unslick"
    // instead of a settings object
  ]
  });
  $('.testimonial_slider').slick({
    dots: true,
    infinite: true,
    arrows: false,
    speed: 700,
    slidesToShow: 1,
    slidesToScroll: 1,
  });
  $('.impact_slider').slick({
    dots: false,
    infinite: true,
    arrows: true,
    speed: 700,
    slidesToShow: 1,
    slidesToScroll: 1,
    variableWidth: true,
    centerMode: true,
    appendArrows: '.impact_arws',
    prevArrow: "<button type='button' class='prev'><span class='material-icons-outlined'>arrow_back</span></button>",
    nextArrow: "<button type='button' class='next'><span class='material-icons-outlined'>arrow_forward</span></button>",
    responsive: [
      {
      breakpoint: 1080,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          variableWidth: false,
          centerMode: false,
        }
        },
    
      // You can unslick at a given breakpoint now by adding:
      // settings: "unslick"
      // instead of a settings object
    ]
});
  const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
  const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
  $(function () {
    $('.multiple_slct').each(function () {
      $(this).select2({
        placeholder: $(this).attr('placeholder'),
        allowClear: Boolean($(this).data('allow-clear')),
      });
    });
  });

  $(".contact_srch").click(function(){
    $(".search_list").slideToggle();
  })
  $(".details_ailogo a").click(function(){
    $(".details_ai_fill").show();
  })
  $(".mob_ai_logo").click(function(){
    $("body").toggleClass("canvas_open");
  });
  $('.close-lobees_aibx').click(function(){
    $("body").removeClass("canvas_open");
  });
  $(".overlay").click(function(){
    $("body").removeClass("canvas_open");
  })

  
  $(".open_rvw_trigger a").click(function(){
    $("body").toggleClass("open-review");
  });
  $(".overlay").click(function(){
    $("body").removeClass("open-review");
  });