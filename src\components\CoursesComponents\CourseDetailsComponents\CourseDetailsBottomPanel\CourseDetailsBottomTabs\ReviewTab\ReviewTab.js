/* eslint-disable */
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData, postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import MostImportantPanel from "../../MostImportantPanel/MostImportantPanel";
import GiveReviewModal from "components/common/Modals/GiveReviewModal";
import { assetImages } from "constants";

const ReviewTab = ({
  activeTab,
  courseReviewTabReload = false,
  setCourseReviewTabReload = () => {},
}) => {
  const params = useParams();
  const token = localStorage.getItem("token");

  const [isReviewLoading, setIsReviewLoading] = useState(false);

  const [ratingCount, setRatingCount] = useState("0.0");
  const [reviewsList, setReviewsList] = useState([]);
  const [ratingPercentArr, setRatingPercentArr] = useState([]);
  const [courseAutoApproval, setCourseAutoApproval] = useState(false);
  const [userCourseRole, setUserCourseRole] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isApproved, setisApproved] = useState(false);

  //function for get all company reviews
  const getReviewsLists = async () => {
    setIsReviewLoading(true);
    let requestURL =
      url.API_BASE_URL +
      url.API_GET_COURSE_DETAILS_WITH_REVIEWS +
      `/${params.id}?token=${token}`;

    try {
      const response = await getData(requestURL);

      console.log("review list response", response);

      setIsReviewLoading(false);

      if (response.status) {
        if (response.data) {
          setCourseAutoApproval(response.data.autoapproval);
          setUserCourseRole(response.data.usercourserole);
          setisApproved(response.data.isapproved);
          setIsSubscribed(response.data.issubscribed);
          setRatingCount(response.data.rating);
          setReviewsList(response.data.reviews);
          setRatingPercentArr(response.data.ratingbar);
        } else {
          setRatingCount("");
          setReviewsList([]);
          setRatingPercentArr([]);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  const starCount = [1, 2, 3, 4, 5];
  const floatValue = parseFloat(ratingCount); // Parse the string as a floating-point number
  const intValue = Math.ceil(floatValue); // Round up to the nearest integer

  useEffect(() => {
    if (courseReviewTabReload) {
      getReviewsLists();
      setCourseReviewTabReload(false);
    }
  }, [courseReviewTabReload]);

  useEffect(() => {
    if (params.id && activeTab === "review") {
      getReviewsLists();
    }
  }, [params.id, activeTab]);

  if (isReviewLoading) {
    return (
      <div className="inner_pnl_outer">
        <div className="inner_leftpnl">
          <div className="forum_bx white_shadow_bx">
            <div className="rvw_rating_bx">
              <div className="left_rvwpnl placeholder-glow">
                <h2>
                  <span className="placeholder col-2"></span>
                  <span className="placeholder col-1 ms-2"></span>
                </h2>
                <ul className="list_stye_none d-flex gap-1">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <li key={index}>
                      <span
                        className="placeholder rounded-circle"
                        style={{
                          width: "1.5rem",
                          height: "1.5rem",
                          display: "inline-block",
                        }}
                      ></span>
                    </li>
                  ))}
                </ul>
                <div className="add_rvw_btn mt-3">
                  <button
                    disabled
                    className="btn outline placeholder col-4"
                  ></button>
                </div>
              </div>

              <div className="right_rvwpnl placeholder-glow mt-4">
                <div className="rvw_progress_outer">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div
                      className="progress_row d-flex align-items-center mb-2"
                      key={index}
                    >
                      <div className="progress_star">
                        <span className="placeholder col-2"></span>
                      </div>
                      <div className="progess_outer flex-grow-1 mx-2">
                        <span
                          className="placeholder w-100"
                          style={{ height: "0.5rem" }}
                        ></span>
                      </div>
                      <div className="rvw_percent">
                        <span className="placeholder col-1"></span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="forum_commnts_pnl review_commnts mt-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div className="forum_row placeholder-glow mb-4" key={index}>
                  <div className="forum_cmntsRow d-flex">
                    <div className="team_userOuter forum_usrOuter d-flex">
                      <div className="team_usr_pic me-3">
                        <span
                          className="placeholder rounded-circle"
                          style={{
                            width: "60px",
                            height: "60px",
                            display: "inline-block",
                          }}
                        ></span>
                      </div>
                      <div className="team_usrTxt w-100">
                        <h3 className="placeholder col-6 me-2"></h3>
                        <h3 className="placeholder col-4"></h3>
                        <div className="rating_star mb-2">
                          <ul className="list_stye_none d-flex gap-1">
                            {Array.from({ length: 5 }).map((_, idx) => (
                              <li key={idx}>
                                <span
                                  className="placeholder rounded-circle"
                                  style={{
                                    width: "1.2rem",
                                    height: "1.2rem",
                                    display: "inline-block",
                                  }}
                                ></span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div className="forum_para">
                          <p className="placeholder col-10 mb-1"></p>
                          <p className="placeholder col-8"></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* -------- most important panel ------------- */}
        <MostImportantPanel />
      </div>
    );
  }

  return (
    <div className="inner_pnl_outer">
      <div className="inner_leftpnl">
        <div className="forum_bx white_shadow_bx">
          {/* add review  */}
          <div
            className={
              userCourseRole !== "" || (isSubscribed && isApproved)
                ? "rvw_rating_bx"
                : "d-none"
            }
          >
            <div className="left_rvwpnl">
              <h2>
                {ratingCount} <span>({reviewsList.length})</span>
              </h2>
              <ul className="list_stye_none">
                {starCount.map((count, index) => {
                  return (
                    <li key={index}>
                      <Link to="#">
                        <i
                          className={`material-icons-outlined ${
                            index < intValue ? "ornge_icon" : ""
                          }`}
                        >
                          star
                        </i>
                      </Link>
                    </li>
                  );
                })}
              </ul>
              <div className="add_rvw_btn">
                <Link
                  to="#"
                  className="btn outline"
                  data-bs-toggle="modal"
                  data-bs-target="#feedback_mdl"
                >
                  Add Review
                </Link>
              </div>
            </div>

            <div className="right_rvwpnl">
              <div className="rvw_progress_outer">
                {ratingPercentArr.map((ratingBar, index) => {
                  return (
                    <div className="progress_row" key={index}>
                      <div className="progress_star">
                        <span>{ratingBar.ratingname} Star</span>
                      </div>
                      <div className="progess_outer">
                        <span
                          className="progress_bar"
                          style={{ width: `${ratingBar.ratingpercent}%` }}
                        ></span>
                      </div>
                      <div className="rvw_percent">
                        <span>{ratingBar.ratingpercent}%</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* review list  */}
          {reviewsList.length > 0 ? (
            <div className="forum_commnts_pnl review_commnts">
              {reviewsList.map((reviewData, index) => {
                return (
                  <div className="forum_row" key={index}>
                    <div className="forum_cmntsRow">
                      {/* <div className="share_btn forum_shreBtn">
                  <Link to="#">
                    <span className="material-icons-outlined">share</span>
                  </Link>
                </div> */}
                      <div className="team_userOuter forum_usrOuter">
                        <div className="team_usr_pic">
                          <img
                            src={
                              reviewData.userimage == ""
                                ? assetImages.defaultUser
                                : url.SERVER_URL + reviewData.userimage
                            }
                            alt=""
                          />
                        </div>
                        <div className="team_usrTxt">
                          <h3>{reviewData.username}</h3>
                          <h5>
                            {reviewData.reviewdate} {reviewData.reviewtime}
                          </h5>
                          <div className="rating_star">
                            <ul className="list_stye_none">
                              {starCount.map((count, index) => {
                                return (
                                  <li key={index}>
                                    <Link to="#">
                                      <i
                                        className={`material-icons-outlined ${
                                          index < parseInt(reviewData.rating)
                                            ? "ornge_icon"
                                            : ""
                                        }`}
                                      >
                                        star
                                      </i>
                                    </Link>
                                  </li>
                                );
                              })}
                              {/* {Array.from(
                          { length: reviewData.rating },
                          (_, index) => (
                            <li className="active" key={index}>
                              <Link to="#">
                                <i className="material-icons-outlined">star</i>
                              </Link>
                            </li>
                          )
                        )} */}
                            </ul>
                          </div>
                          <div className="forum_para">
                            <p>{reviewData.comment}</p>
                          </div>

                          {/* <div className="form_share">
                      <Link to="#">
                        <i className="material-icons-outlined">thumb_up</i>
                        <em>Likes</em>
                        <span>2</span>
                      </Link>
                      <Link to="#" className="commnt_forum">
                        <i className="material-icons-outlined">chat</i>
                        <em>Comment</em>
                        <span>3</span>
                      </Link>
                    </div> */}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="d-flex align-items-center justify-content-center">
              <div className="pnl_mdlpnl">
                <div className="empty_access text-center">
                  <div className="empty_pic mb-4">
                    {" "}
                    <img src={assetImages.emptyVector} alt="" />
                  </div>
                  <div className="empty_text">
                    <p className="fs-lg text-gray fw-semibold mb-4">
                      Sorry....! Reviews are not available for this course
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* -------- most important panel ------------- */}
      <MostImportantPanel />
    </div>
  );
};

export default ReviewTab;
