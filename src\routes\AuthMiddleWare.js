/* eslint-disable */
import { useEffect } from "react";
import { Switch, Route, Redirect } from "react-router-dom";
import { protectedRoutes, publicRoutes } from "./Route";

const Authmiddleware = () => {
  const fullURL = window.location.href;

  const parsedURL = new URL(fullURL);

  const searchParams = parsedURL.searchParams;

  // console.log("parsedURL ------>", parsedURL);
  // console.log("searchParams ------>", searchParams);

  const refParam = searchParams.get("ref");
  const secretParam = searchParams.get("secret");
  const emailParams = searchParams.get("memberemail");
  const memberShipIdParams = searchParams.get("membershipid");

  let redirectUrl = "";
  let pathWithoutDomain = parsedURL.pathname;

  // console.log("pathWithoutDomain 1 ------>", pathWithoutDomain);

  if (refParam && secretParam) {
    // console.log("refParam ------>", refParam);
    // console.log("secretParam ------>", secretParam);
    redirectUrl = new URL(refParam);
    pathWithoutDomain = `${redirectUrl.pathname}?secret=${secretParam}`;
  }

  if (emailParams && memberShipIdParams) {
    // console.log("emailParams ------>", emailParams);
    // console.log("memberShipIdParams ------>", memberShipIdParams);
    pathWithoutDomain += `?memberemail=${emailParams}&membershipid=${memberShipIdParams}`;
  }

  // console.log("redirectUrl ------>", redirectUrl);
  // console.log("pathWithoutDomain 2 ------>", pathWithoutDomain);

  let token = null;

  token = localStorage.getItem("token");

  useEffect(() => {
    if (!token) {
      if (
        pathWithoutDomain.startsWith("/") &&
        !pathWithoutDomain.startsWith("/logout") &&
        pathWithoutDomain.length > 1
      ) {
        // The url starts with "/" and has more characters after it
        // console.log("The url starts with '/' and has content after it.");
        localStorage.setItem("redirectToUrl", pathWithoutDomain);
      } else if (pathWithoutDomain === "/") {
        // The url is exactly "/"
        // console.log("The url is exactly '/'");
        localStorage.setItem("redirectToUrl", "");
      } else {
        console.log(
          "The url does not start with '/' or does not have content after it."
        );
      }
    }
  }, []);

  return (
    <Switch>
      {/* ====== mapping of non authenticated routes start ====== */}

      {!token &&
        publicRoutes.map((routename, index) => (
          <Route
            path={routename.path}
            component={routename.component}
            exact
            key={index}
          />
        ))}

      {/* ====== mapping of non authenticated routes end ====== */}

      {/* ====== mapping of authenticated route start ====== */}

      {token &&
        protectedRoutes.map((routename, index) => (
          <Route
            path={routename.path}
            component={routename.component}
            exact
            key={index}
          />
        ))}

      {/* ====== mapping of authenticated route end ====== */}

      {/* ====== default redirect to dashboard ======= */}
      {token ? (
        refParam && secretParam ? (
          <Redirect to={`${pathWithoutDomain}`} />
        ) : (
          <Redirect to="/feed" />
        )
      ) : (
        <Redirect to="/" />
      )}
    </Switch>
  );
};

export default Authmiddleware;
