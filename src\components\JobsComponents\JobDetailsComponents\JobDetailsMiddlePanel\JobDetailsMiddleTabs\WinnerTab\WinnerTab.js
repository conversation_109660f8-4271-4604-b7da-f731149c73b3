/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

const WinnerTab = ({ tabName = "" }) => {
  const { t } = useTranslation();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const params = useParams();

  const [winnerList, setwinnerList] = useState([]);
  const [isLoading, setisLoading] = useState(false);

  //function for get response
  const getAllChallengeResponse = async () => {
    try {
      setisLoading(true);

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_CHALLENGE_RESPONSE +
        `?token=${token}&challenge=${params.id}`;

      console.log("get challenge responses", requestUrl);

      const response = await getData(requestUrl);

      console.log("challenge responses", response);
      setisLoading(false);

      if (response.status) {
        const filteredResults = response.data.filter((resp) => resp.iswinner);

        setwinnerList(filteredResults);
      }
    } catch (error) {
      console.log(error.message);
      setisLoading(false);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "winner") {
      getAllChallengeResponse();
    }
  }, [params.id, tabName]);

  // Loading placeholder component
  const LoadingPlaceholder = () => (
    <div className="post_item">
      {[1, 2].map((item, index) => (
        <div
          className="white_shadow_bx people_bx position-relative overflow-hidden"
          key={index}
        >
          <div className="people_row">
            <div className="left_people">
              <div className="people_profile">
                <div
                  className="glow-placeholder"
                  style={{ width: "60px", height: "60px", borderRadius: "50%" }}
                ></div>
              </div>
              <div className="people_profile_info">
                <h3
                  className="glow-placeholder"
                  style={{ width: "180px", height: "20px" }}
                ></h3>
                <h5
                  className="glow-placeholder"
                  style={{ width: "220px", height: "16px", marginTop: "8px" }}
                ></h5>
                <h6
                  className="glow-placeholder fw-semibold text-gray"
                  style={{ width: "150px", height: "14px", marginTop: "8px" }}
                ></h6>
              </div>
            </div>
          </div>
          <style jsx>{`
            .glow-placeholder {
              animation: glow 1.5s ease-in-out infinite;
              background: #f6f7f8;
              background: linear-gradient(
                to right,
                #f6f7f8 8%,
                #edeef1 18%,
                #f6f7f8 33%
              );
              background-size: 800px 104px;
              border-radius: 4px;
              display: inline-block;
            }
            @keyframes glow {
              0% {
                background-position: -200px 0;
              }
              100% {
                background-position: 200px 0;
              }
            }
          `}</style>
        </div>
      ))}
    </div>
  );

  return (
    <div className="post_item_outer">
      {isLoading ? (
        <LoadingPlaceholder />
      ) : winnerList.length === 0 ? (
        <div className="pnl_mdlpnl w-100">
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-md text-gray fw-semibold">
                Sorry....! Winners not availbale at this moment
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="post_item">
          {winnerList.map((jobResponse, index) => {
            return (
              <div className="white_shadow_bx people_bx" key={index}>
                <div className="people_row">
                  <div className="left_people">
                    <div className="people_profile">
                      <img
                        src={
                          jobResponse.candidateimage === ""
                            ? assetImages.defaultUser
                            : url.SERVER_URL + jobResponse.candidateimage
                        }
                        alt={jobResponse.cvcandidatename}
                      />
                    </div>
                    <div className="people_profile_info">
                      <h3>{jobResponse.cvcandidatename}</h3>
                      <h5>{jobResponse.cvcandidateemail}</h5>
                      <h6 className="fw-semibold text-gray">
                        {jobResponse.candidateposition}
                      </h6>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default WinnerTab;
