import React from "react";

const ApplyWarningModal = () => {
  return (
    <div className="post_modal success_greetings_mdl">
      <div
        className="modal fade"
        id="apply_warning_mdl"
        data-bs-backdrop="static"
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-body">
              <div className="gretings_icon text-danger">
                <span className="material-icons-outlined">warning</span>
              </div>
              <div className="gretings_hdng">
                <h3>Moderator or member can't apply</h3>
              </div>
              <div className="post_btn post_bnts_outer">
                <button className="btn" data-bs-dismiss="modal">
                  Close Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplyWarningModal;
