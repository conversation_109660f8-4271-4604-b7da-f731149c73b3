/* eslint-disable */
import React, { useEffect } from "react";

const FoundingTeamModal = ({
  founderIndex,
  founderData,
  setFounderData,
  founderList,
  setFounderList,
  setFounderIndex,
}) => {

  // Update founderData when editing an existing founder
  useEffect(() => {
    if (founderIndex != null && founderList[founderIndex]) {
      setFounderData(founderList[founderIndex]);
    } else {
      setFounderData({
        name: "",
        position: "",
        about: "",
        experience: "",
        skills: "",
        linkedin: "",
        equityholding: "",
        notableadvisors: "",
      });
    }
  }, [founderIndex, founderList, setFounderData]);

  // Function to add a new founder
  const addFounderHandler = () => {
    setFounderList([...founderList, founderData]);
    closeModalHandler();
  };

  // Function to edit an existing founder
  const editFounderHandler = () => {
    const updatedFounderList = [...founderList];
    updatedFounderList[founderIndex] = founderData;
    setFounderList(updatedFounderList);
    closeModalHandler();
  };

  // Function to close the modal and reset the form
  const closeModalHandler = () => {
    setFounderData({
      name: "",
      role: "",
      about: "",
      experience: "",
      skills: "",
      linkedin: "",
      equityholding: "",
      notableadvisors: "",
    });
    setFounderIndex(null);
  };

  // Function to save the founder data
  const saveInfoHandler = () => {
    if (founderIndex != null) {
      editFounderHandler();
    } else {
      addFounderHandler();
    }

    let modal = document.querySelector("#founding_team_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="founding_team_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Founding Team</h3>
                <h5>Add or update details about your founding team</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={closeModalHandler}
              >
                <i className="material-icons-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Name</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Name"
                      value={founderData.name}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          name: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Role</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Role"
                      value={founderData.role}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          role: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>About</label>
                    <textarea
                      className="form-control"
                      placeholder="Enter About"
                      value={founderData.about}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          about: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Experience</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Experience"
                      value={founderData.experience}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          experience: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Skills</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Skills"
                      value={founderData.skills}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          skills: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>LinkedIn</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter LinkedIn Profile"
                      value={founderData.linkedin}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          linkedin: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Equity Holding</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Equity Holding"
                      value={founderData.equityholding}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          equityholding: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div className="form-group">
                    <label>Notable Advisors</label>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Notable Advisors"
                      value={founderData.notableadvisors}
                      onChange={(e) =>
                        setFounderData((prevData) => ({
                          ...prevData,
                          notableadvisors: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={closeModalHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoundingTeamModal;
