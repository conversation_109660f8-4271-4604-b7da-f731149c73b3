import React from "react";
import { Link } from "react-router-dom";

const ApplyJobModal = () => {
  return (
    <div className="job_modal">
      <div className="modal fade" id="apply_modal">
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <div className="company_logotxt">
                <span>
                  <img src="images/google.svg" alt="" />
                </span>
                <span>Google</span>
              </div>
              <div className="modal_hdng">
                <h3>Apply for UI and UX Designer</h3>
              </div>
              <div className="event_tags">
                <ul>
                  <li>
                    <i className="material-icons-outlined">place</i>
                    <span>USA</span>
                  </li>
                  <li>
                    <i className="material-icons-outlined">event_note</i>
                    <span>Today</span>
                  </li>
                  <li>
                    <i className="material-icons-outlined">schedule</i>
                    <span>Fulltime</span>
                  </li>
                </ul>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
              >
                <i className="material-icons-outlined">close</i>
              </button>
            </div>

            <div className="modal-body">
              <div className="prsnl_detailsArea">
                <div className="row">
                  <div className="col-lg-4 col-sm-6">
                    <div className="input_fill jobinput_fill">
                      <label>First Name</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Enter first name"
                      />
                    </div>
                  </div>
                  <div className="col-lg-4 col-sm-6">
                    <div className="input_fill jobinput_fill">
                      <label>Last Name</label>
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Enter last name"
                      />
                    </div>
                  </div>
                  <div className="col-lg-4 col-sm-6">
                    <div className="input_fill jobinput_fill">
                      <label>Email</label>
                      <input
                        type="email"
                        className="form-control"
                        placeholder="Enter email"
                      />
                    </div>
                  </div>
                  <div className="col-lg-4 col-sm-6">
                    <div className="input_fill jobinput_fill">
                      <label>Phone No.</label>
                      <input
                        type="number"
                        className="form-control"
                        placeholder="Enter phone no"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="others_detailsArea">
                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3">
                      <div className="details_input_txt">
                        <label>Resume Title</label>
                      </div>
                    </div>
                    <div className="col-lg-9">
                      <div className="input_fill jobinput_fill">
                        <input
                          type="text"
                          className="form-control"
                          placeholder="Enter title"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3">
                      <div className="details_input_txt">
                        <label>Resume Details</label>
                      </div>
                    </div>
                    <div className="col-lg-9">
                      <div className="input_fill jobinput_fill description_fill">
                        <textarea
                          className="form-control"
                          placeholder="Enter details"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3">
                      <div className="details_input_txt">
                        <label>Expected CTC</label>
                      </div>
                    </div>
                    <div className="col-lg-9">
                      <div className="job_fillOuter">
                        <div className="input_fill jobinput_fill">
                          <input
                            type="text"
                            className="form-control uro_sign"
                            placeholder="Maximum expectation"
                          />
                        </div>
                        <div className="input_fill jobinput_fill">
                          <input
                            type="text"
                            className="form-control uro_sign"
                            placeholder="Minimum expectation"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="details_row attached_row">
                  <div className="row">
                    <div className="col-lg-3">
                      <div className="details_input_txt">
                        <label>Attach Resume</label>
                      </div>
                    </div>
                    <div className="col-lg-9">
                      <div className="upload_area">
                        <div className="upload_item">
                          <div className="uploaded_innrBx">
                            <Link to="#" className="delete_btn">
                              <span className="material-icons-outlined">
                                delete
                              </span>
                            </Link>
                            <div className="upload_icon">
                              <img src="images/pdf.svg" alt="" />
                            </div>
                            <div className="upload_txt">
                              <h4>MyResume.pdf</h4>
                              <h5>1.5mb</h5>
                            </div>
                          </div>
                        </div>

                        <div className="upload_item">
                          <div className="uploaded_innrBx">
                            <Link to="#" className="delete_btn">
                              <span className="material-icons-outlined">
                                delete
                              </span>
                            </Link>
                            <div className="upload_icon">
                              <img src="images/meet.svg" alt="" />
                            </div>
                            <div className="upload_txt">
                              <h4>Myvideo.mp4</h4>
                              <h5>16mb</h5>
                            </div>
                          </div>
                        </div>
                        <div className="upload_item">
                          <div className="upload_inner_btn">
                            <input type="file" className="file_btn" />
                            <div className="upload_icon">
                              <span className="material-icons-outlined">
                                file_upload
                              </span>
                            </div>
                            <div className="upload_txt">
                              <h4>Upload</h4>
                              <h5>Max 20mb doc/mp4</h5>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3">
                      <div className="details_input_txt">
                        <label>Video Link</label>
                      </div>
                    </div>
                    <div className="col-lg-9">
                      <div className="input_fill jobinput_fill">
                        <input
                          type="text"
                          className="form-control"
                          placeholder="Enter URL"
                        />
                        <Link to="#" className="copy_link">
                          <span className="material-icons-outlined"> link</span>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3">
                      <div className="details_input_txt">
                        <label>Comments</label>
                      </div>
                    </div>
                    <div className="col-lg-9">
                      <div className="input_fill jobinput_fill description_fill">
                        <textarea
                          className="form-control"
                          placeholder="Enter comments"
                        ></textarea>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3 hidden-sm"></div>
                    <div className="col-lg-9">
                      <div className="chek_row">
                        <label className="radio-check">
                          <input type="checkbox" />
                          <span></span>
                          <p>I don't want to present my details publicly</p>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="details_row">
                  <div className="row">
                    <div className="col-lg-3 hidden-sm"></div>
                    <div className="col-lg-9">
                      <div className="apply_btn">
                        <button type="submit" className="btn">
                          Apply Now
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplyJobModal;
