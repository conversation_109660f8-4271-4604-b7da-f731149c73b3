/*eslint-disable */
import React, { useContext, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

// Import utilities
import * as url from "helper/UrlHelper";
import { postData } from "utils/Gateway";
import { GlobalProvider } from "context/GlobalContext";
import { ecosystemSlug, storeOwnerEmail } from "Config/Config";

// Constants
const OTP_LENGTH = 4;
const INPUT_MAX_LENGTH = 1;

const OtpConfirmLoginModal = () => {
  const { t } = useTranslation();
  const { userEmailforOtp, setuserEmailforOtp } = useContext(GlobalProvider);

  const redirectToUrl = localStorage.getItem("redirectToUrl") || "";

  // Refs for OTP inputs
  const inputRefs = useRef([]);

  // State management
  const [otpCode, setOtpCode] = useState(Array(OTP_LENGTH).fill(""));
  const [otpFailed, setOtpFailed] = useState(false);
  const [otpSending, setOtpSending] = useState(false);
  const [otpResendMessage, setOtpResendMessage] = useState("");
  const [isValidating, setIsValidating] = useState(false);

  // Helper function to clear error states
  const clearErrors = () => {
    setOtpFailed(false);
    setOtpResendMessage("");
  };

  // Handle OTP input change
  const handleOtpChange = (index, value) => {
    clearErrors();

    // Only allow single digit
    const digit = value.slice(-1);

    const newOtpCode = [...otpCode];
    newOtpCode[index] = digit;
    setOtpCode(newOtpCode);

    // Auto-focus next input
    if (digit && index < OTP_LENGTH - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace navigation
  const handleKeyDown = (index, e) => {
    if (e.key === "Backspace") {
      if (!otpCode[index] && index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  // Store user data in localStorage
  const storeUserData = (response) => {
    const dataToStore = {
      token: response.token,
      userInfo: JSON.stringify(response.data),
      userID: JSON.stringify(response.data._id),
      ecosystem: response.ecosystemid,
      ecosystemlogo: response.ecosystemlogo,
      ecosystemslug: response.ecosystemslug,
      moduleaccess: response.data.moduleaccessdata,
    };

    Object.entries(dataToStore).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });
  };

  // Handle redirect after successful login
  const handleSuccessRedirect = () => {
    if (redirectToUrl && redirectToUrl !== "") {
      window.location.href = redirectToUrl;
    } else {
      window.location.reload();
    }
  };

  // Close modal
  const closeModal = () => {
    const modal = document.querySelector("#otp_confirm_login_mdl");
    const bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal?.hide();
  };

  // Validate OTP
  const validateOtp = async () => {
    if (isValidating) return;

    const finalCode = otpCode.join("");
    if (finalCode.length !== OTP_LENGTH) return;

    try {
      setIsValidating(true);

      const userData = {
        email: userEmailforOtp,
        otp: finalCode,
      };

      const requestURL = `${url.API_BASE_URL}${url.API_VALIDATE_OTP}?ecosystemslug=${ecosystemSlug}&storeowneremail=${storeOwnerEmail}`;

      const response = await postData(requestURL, userData);

      if (response.status) {
        setOtpFailed(false);
        closeModal();
        storeUserData(response);
        handleSuccessRedirect();
      } else {
        setOtpFailed(true);
      }
    } catch (error) {
      console.error("OTP validation error:", error.message);
      setOtpFailed(true);
    } finally {
      setIsValidating(false);
    }
  };

  // Resend OTP
  const resendOtp = async () => {
    if (otpSending) return;

    try {
      setOtpSending(true);
      clearErrors();

      const userData = { email: userEmailforOtp };
      const requestURL = `${url.API_BASE_URL}${url.API_GET_OTP}`;

      const response = await postData(requestURL, userData);

      if (response.status) {
        setOtpResendMessage(t("We have sent OTP in your email"));
      } else {
        setOtpResendMessage(t("Failed to resend OTP. Please try again."));
      }
    } catch (error) {
      console.error("OTP resend error:", error.message);
      setOtpResendMessage(t("Failed to resend OTP. Please try again."));
    } finally {
      setOtpSending(false);
    }
  };

  // Reset modal state
  const resetModal = () => {
    setOtpCode(Array(OTP_LENGTH).fill(""));
    setOtpFailed(false);
    setOtpResendMessage("");
    setuserEmailforOtp("");
  };

  // Auto-validate when OTP is complete
  useEffect(() => {
    const completeOtp = otpCode.join("");
    if (completeOtp.length === OTP_LENGTH && !isValidating) {
      validateOtp();
    }
  }, [otpCode, isValidating]);

  // Focus first input on mount
  useEffect(() => {
    inputRefs.current[0]?.focus();
  }, []);

  return (
    <div className="registration_modal">
      <div
        className="modal fade show"
        id="otp_confirm_login_mdl"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
      >
        <div className="modal-dialog small_modal">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng">
                <h3>{t("Verify Email")}</h3>
                <h5>{t("Please enter OTP we just sent you on your email")}.</h5>
                <span className="email_info">{userEmailforOtp}</span>
              </div>

              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                data-bs-toggle="modal"
                data-bs-target="#login_modal"
                aria-label="Close"
                onClick={resetModal}
              >
                <i className="material-icons-outlined">close</i>
              </button>
            </div>

            <div className="modal-body">
              <div className="login_body">
                <form onSubmit={(e) => e.preventDefault()}>
                  <div className="input_fill">
                    <label>{t("Enter OTP")}</label>

                    <div className="validate_fill">
                      {otpCode.map((digit, index) => (
                        <input
                          key={index}
                          type="number"
                          ref={(el) => (inputRefs.current[index] = el)}
                          value={digit}
                          onChange={(e) =>
                            handleOtpChange(index, e.target.value)
                          }
                          onKeyDown={(e) => handleKeyDown(index, e)}
                          className="form-control"
                          placeholder="-"
                          maxLength={INPUT_MAX_LENGTH}
                          disabled={isValidating}
                          aria-label={`OTP digit ${index + 1}`}
                        />
                      ))}
                    </div>

                    {otpFailed && (
                      <div className="validate_error">
                        <p className="error">
                          {t(
                            "Oops! the OTP verification failed. Please try again"
                          )}
                          .
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="loginstep_btnsouter">
                    <div className="resend_btn">
                      <button
                        type="button"
                        className="btn outline"
                        onClick={resendOtp}
                        disabled={otpSending}
                        style={{
                          cursor: otpSending ? "not-allowed" : "pointer",
                        }}
                      >
                        {otpSending ? (
                          <div
                            className="mx-2 spinner-border spinner-border-sm"
                            role="status"
                          >
                            <span className="visually-hidden">Loading...</span>
                          </div>
                        ) : (
                          <i className="material-icons-outlined">cached</i>
                        )}
                        <span>{t("Resend Code")}</span>
                      </button>
                    </div>
                  </div>

                  {otpResendMessage && (
                    <div className="signin_hdng">
                      <span className="email_info">{otpResendMessage}.</span>
                    </div>
                  )}
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtpConfirmLoginModal;
