{"name": "lobeesv4-react", "version": "0.1.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^39.0.2", "@ckeditor/ckeditor5-react": "^6.2.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.20", "@mui/material": "^5.15.20", "@mui/x-date-pickers": "^6.19.2", "@stripe/stripe-js": "^2.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "clipboard-copy": "^4.0.1", "i18next": "^22.5.1", "i18next-browser-languagedetector": "^7.0.1", "jodit-react": "^5.2.19", "material-react-table": "^2.13.0", "moment": "^2.29.4", "query-string": "^8.1.0", "react": "^18.2.0", "react-autosuggest": "^10.1.0", "react-big-calendar": "^1.19.4", "react-dom": "^18.2.0", "react-i18next": "^12.3.1", "react-multi-carousel": "^2.8.4", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-select": "^5.7.4", "socket.io-client": "^4.7.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}