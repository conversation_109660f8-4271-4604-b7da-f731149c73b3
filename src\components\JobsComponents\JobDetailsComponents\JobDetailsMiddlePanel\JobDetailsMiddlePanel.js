/* eslint-disable */
import { Link } from "react-router-dom";
import DetailsTab from "./JobDetailsMiddleTabs/DetailsTab/DetailsTab";
import AppliedTab from "./JobDetailsMiddleTabs/AppliedTab/AppliedTab";
import TeamsTab from "./JobDetailsMiddleTabs/TeamsTab/TeamsTab";
import ForumTab from "./JobDetailsMiddleTabs/ForumTab/ForumTab";
import DocsTab from "./JobDetailsMiddleTabs/DocsTab/DocsTab";
import WinnerTab from "./JobDetailsMiddleTabs/WinnerTab/WinnerTab";
import ProcessTab from "./JobDetailsMiddleTabs/ProcessTab/ProcessTab";
import FAQTab from "./JobDetailsMiddleTabs/FAQTab/FAQTab";
// import use translation ------------------------------
import { useTranslation } from "react-i18next";
import FeedbackTab from "./JobDetailsMiddleTabs/FeedbackTab/FeedbackTab";
import { useState } from "react";

const JobDetailsMiddlePanel = () => {
  const { t } = useTranslation(); // translation marker

  const jobDetailsTabs = [
    { id: "overview", label: t("Details"), component: DetailsTab },
    { id: "process", label: t("Process"), component: ProcessTab },
    { id: "members", label: t("Teams"), component: TeamsTab },
    { id: "applied", label: t("Applied"), component: AppliedTab },
    { id: "winner", label: t("Winner"), component: WinnerTab },
    { id: "doc", label: t("Docs"), component: DocsTab },
    {
      id: "feedbacktab",
      label: `${t("Feedback")} 360`,
      component: FeedbackTab,
    },
  ];

  const [activeTab, setActiveTab] = useState("overview");

  /**
   * Handle tab selection
   * @param {string} tabId - ID of the selected tab
   */
  const tabSelectionHandler = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="pnl_mdlpnl" style={{ width: "70%" }}>
      <div className="people_item">
        <div className="tab_links people_tablinks">
          <ul className="nav nav-tabs list_stye_none">
            {jobDetailsTabs.map((tab) => (
              <li className="nav-item" key={tab.id}>
                <Link
                  className={`nav-link ${
                    activeTab === tab.id ? "active show" : ""
                  }`}
                  to="#"
                  onClick={() => {
                    tabSelectionHandler(tab.id);
                  }}
                >
                  {tab.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="tab-content">
          {jobDetailsTabs.map((tab) => {
            const TabComponent = tab.component;
            return (
              <div
                key={tab.id}
                className={`tab-pane fade ${
                  activeTab === tab.id ? "show active" : ""
                }`}
              >
                <TabComponent tabName={activeTab === tab.id ? tab.id : ""} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default JobDetailsMiddlePanel;
