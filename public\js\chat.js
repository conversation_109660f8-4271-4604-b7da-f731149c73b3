 
$(document).ready(function(){
    $(".chat_btn").click(function(){
        $(".chat_cmntBx").toggleClass("show_chatpopup");
    });
    $('#login_modal').modal('show');
    $(".pro_edit_btn a").click(function(){
      $(this).parents(".profile_info_row").addClass("editfill_open");
    });
    $(".reply_btn").click(function(){
      $("body").toggleClass("reply_open");
    });
    $(".emailmdl_btn").click(function(){
      $("body").toggleClass("emailpopup_open");
    });
    $(".email_cross a").click(function(){
      $("body").removeClass("emailpopup_open");
    })
    $(".next_rply").click(function(){
      $("body").toggleClass("reply_openPopup");
    });
    $(".email_cross a").click(function(){
      $("body").removeClass("reply_openPopup");
    })


    
});

$(window).on("load",function(){
    $(".chat_innrScroll, .chatting_innr, .profile_infoOuter, .profile_innrScroll, .group_innrScroll").mCustomScrollbar();
});

$(".rfresh_profilePic a").click(function(){
    $("body").toggleClass("profile_open");
});
$(".pro_back_btn a").click(function(){
    $("body").removeClass("profile_open");
});
$(".profile_bar a").click(function(){
  $("body").toggleClass("hamberger_menuOpen");
});
$(".pro_back_btn a").click(function(){
  $("body").removeClass("hamberger_menuOpen");
});

$(".srch_Btn_icon").click(function(){
  $(".serch_pnl").stop().slideToggle();
})

if ($(window).width() < 992) {
  $(".chat_usrPnl .chat_usrRow  .chat_user_item").click(function(){
    $("body").toggleClass("chatmsg_open")
  });
  $(".list_back a").click(function(){
    $("body").removeClass("chatmsg_open");
  });
};
$(document).ready(function() {
    $('.select2').select2({
      minimumResultsForSearch: -1
  });

  $(".pink_assignmnt_bx").click(function(){
    $("body").toggleClass("assignment_pnl_open");
  })
  $(".expart_cross").click(function(){
    $("body").removeClass("assignment_pnl_open");
  })
  $(".details_arwbtn").click(function(){
    $("body").toggleClass("detailspnl_open");
  })
  $(".back_btn a").click(function(){
    $("body").removeClass("detailspnl_open");
  })
  $(".suggetion_bluebx").click(function(){
    $("body").toggleClass("suggetion_open");
  })
  $(".expart_cross").click(function(){
    $("body").removeClass("suggetion_open");
  })
  $(".prescription_grnbx").click(function(){
    $("body").toggleClass("prescription_open");
  })
  $(".expart_cross").click(function(){
    $("body").removeClass("prescription_open");
  })


  

});

$(document).ready(function(){
    $('[data-fancybox="images"]').fancybox({
        buttons : [
        //   "zoom",
        //   "share",
        //   "slideShow",
        //   "fullScreen",
          "download",
          "thumbs",
          "close"
        ]
      })
   
      $(".profile_linkBtn").click(function(){
        $("body").toggleClass("profile_menuPnl_open")
      });
      $(".profile_crossbtn").click(function(){
        $("body").removeClass("profile_menuPnl_open")
      })

});


  