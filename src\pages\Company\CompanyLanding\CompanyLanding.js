/* eslint-disable */
import AuthHeader from "components/common/AuthHeader";
import React, { useContext, useEffect, useState } from "react";

import CompanyLandingBody from "components/CompanyComponents/CompanyLandingComponents/CompanyLandingBody/CompanyLandingBody";

const CompanyLanding = () => {
  useEffect(() => {
    document.title = "Company";
  }, []);

  return (
    <>
      <AuthHeader moduleName={"company"} />

      {/* --------- body section ------------ */}
      <CompanyLandingBody />
    </>
  );
};

export default CompanyLanding;
