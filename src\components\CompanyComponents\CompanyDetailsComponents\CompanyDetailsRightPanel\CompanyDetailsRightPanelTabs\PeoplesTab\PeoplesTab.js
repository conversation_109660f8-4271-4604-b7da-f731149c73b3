/* eslint-disable */
/*------ React imports ------*/
import { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

/*------ Helper imports ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

const PeoplesTab = ({ tabName = "" }) => {
  const { id: companyId } = useParams(); // Renamed for clarity
  const token = localStorage.getItem("token");

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [peopleList, setPeopleList] = useState([]);

  //function for company details
  const getCompanyDetails = async () => {
    try {
      setIsLoading(true);

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_COMPANY_DETAILS +
        `/${companyId}?token=${token}&modulefrom=${tabName}`;

      const response = await getData(requestUrl);

      if (response.status && response.data) {
        const companyName = response.data.name;

        let appUserRequestUrl =
          url.API_BASE_URL +
          url.API_GET_APP_USERS +
          `?token=${token}&modulefrom=backend&&filtercompany=${companyName}`;

        const appUserResponse = await getData(appUserRequestUrl);

        setIsLoading(false);

        console.log("response of app user list------>", appUserResponse);

        if (appUserResponse.status && appUserResponse.data) {
          setPeopleList(appUserResponse.data);
        }
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (companyId && tabName === "peoples") {
      getCompanyDetails();
    }
  }, [companyId, tabName]);

  if (isLoading) {
    return (
      <div className="post_item_outer">
        <div className="post_item">
          {/* You can map over a dummy array to show multiple placeholders */}
          {[...Array(5)].map((_, index) => (
            <div className="card shadow-sm mb-3" key={index} aria-hidden="true">
              <div className="card-body">
                <div className="placeholder-glow">
                  <div className="d-flex justify-content-between align-items-center">
                    {/* Left Side: Profile Image and Info */}
                    <div className="d-flex align-items-center">
                      {/* Placeholder for the circular profile image */}
                      <span
                        className="placeholder rounded-circle"
                        style={{ width: "80px", height: "50px" }}
                      ></span>

                      <div className="ms-3 w-100">
                        {/* Placeholder for Name (h3) */}
                        <h3 className="card-title mb-2">
                          <span
                            className="placeholder"
                            style={{ width: "100px" }}
                          ></span>
                        </h3>
                        {/* Placeholder for Role (h5) */}
                        <h5 className="card-subtitle">
                          <span
                            className="placeholder"
                            style={{ width: "100px" }}
                          ></span>
                        </h5>
                      </div>
                    </div>

                    {/* Right Side: Button */}
                    <div className="ms-3">
                      {/* Placeholder for the "View Details" button */}
                      <Link
                        to="#"
                        tabIndex="-1"
                        className="placeholder rounded-10"
                        style={{ width: "110px", height: "40px" }}
                      ></Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="post_item_outer">
      <div className="post_item">
        {peopleList.map((people, index) => {
          return (
            <div className="white_shadow_bx people_bx" key={index}>
              <div className="people_row">
                <div className="left_people">
                  <div className="people_profile">
                    <img
                      src={
                        people.userimage !== ""
                          ? url.SERVER_URL + people.userimage
                          : assetImages.defaultUser
                      }
                      alt=""
                    />
                  </div>
                  <div className="people_profile_info">
                    <h3>
                      {people.name} {people.surname}
                    </h3>
                    <h5>{people.email}</h5>
                  </div>
                </div>
                <div className="right_people_btn">
                  <Link
                    to={`/peoples/details/${people._id}`}
                    className="btn outline"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PeoplesTab;
