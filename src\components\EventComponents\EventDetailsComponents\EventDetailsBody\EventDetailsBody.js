/* eslint-disable */
import { useState } from "react";
import { Link, useParams, useHistory, useLocation } from "react-router-dom";

import EventDetailsLeftPanel from "../EventDetailsLeftPanel/EventDetailsLeftPanel";
import EventDetailsMiddlePanel from "../EventDetailsMiddlePanel/EventDetailsMiddlePanel";
// import EventDetailsRightPanel from "../EventDetailsRightPanel/EventDetailsRightPanel";

import TagsModal from "components/common/Modals/TagsModal";
import TagsAddedSuccessModal from "components/common/Modals/TagsAddedSuccessModal";
import GiveReviewModal from "components/common/Modals/GiveReviewModal";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";
import ReviewAddedSuccessModal from "components/common/Modals/ReviewAddedSuccessModal";
import JoinSuccessModal from "../Modals/JoinSuccessModal";

import ShareModal from "components/common/Modals/ShareModal";
import AccessDeniedView from "components/common/AccessDeniedView/AccessDeniedView";

const EventDetailsBody = () => {
  const { t } = useTranslation(); // translation marker

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess");
  const params = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [isModerator, setIsModerator] = useState(false);
  const [isJoined, setisJoined] = useState(false);
  const [isApproved, setisApproved] = useState(false);
  const [eventType, setEventType] = useState(null); // not used in this component but can be used later if needed

  const [copyMessage, setCopyMessage] = useState(""); // for share modal

  const [refreshEvent, setRefreshEvent] = useState(false);

  const afterModalClose = () => {
    setRefreshEvent(true);
  };

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    moduleAccess.includes("MOD_EVENT")
  ) {
    return (
      <section className="feed_pnltotal">
        <div className="container-fluid">
          <div className="back_btn">
            <Link to="/events">
              <i className="material-icons-outlined">arrow_back</i>
              <span>{t("Back")}</span>
            </Link>
          </div>
          <div className="pnl_outer">
            <EventDetailsLeftPanel
              isjoined={isJoined}
              setEventType={setEventType}
              setIsLoading={setIsLoading}
              setisJoined={setisJoined}
              setisApproved={setisApproved}
              setIsModerator={setIsModerator}
              setCopyMessage={setCopyMessage}
              refreshEvent={refreshEvent}
              setRefreshEvent={setRefreshEvent}
            />

            {/* -------- left panel -------- */}

            {/* ------ mdddle panel ------- */}
            <EventDetailsMiddlePanel
              isLoading={isLoading}
              eventType={eventType}
              isApproved={isApproved}
              isModerator={isModerator}
              isJoined={isJoined}
              refreshEvent={refreshEvent}
              setRefreshEvent={setRefreshEvent}
            />

            {/* ------ right panel ------- */}
            {/* <EventDetailsRightPanel similarList={similarList} /> */}
          </div>
        </div>

        {/* --------- tags modal ----------- */}
        <TagsModal moduleName="eventdetails" selectedId={params.id} />

        {/* --------- tags added success / greetings modal ------------- */}
        <TagsAddedSuccessModal afterTagModalClose={afterModalClose} />

        {/* success modal for join  */}
        <JoinSuccessModal afterModalClose={afterModalClose} />

        <ShareModal copyMessage={copyMessage} />

        {/* ------- feedback modal -------- */}
        <GiveReviewModal moduleName="event" />

        <ReviewAddedSuccessModal afterModalClose={afterModalClose} />
      </section>
    );
  } else {
    return <AccessDeniedView />;
  }
};

export default EventDetailsBody;
