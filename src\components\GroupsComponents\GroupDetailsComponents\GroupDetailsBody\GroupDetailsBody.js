/* eslint-disable */
import React, { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import { getData, putData, postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

// import use translation ------------------------------
import { useTranslation } from "react-i18next";

import GroupDetailsRightPanel from "../GroupDetailsRightPanel/GroupDetailsRightPanel";
import GroupDetailsMiddlePanel from "../GroupDetailsMiddlePanel/GroupDetailsMiddlePanel";
import GroupDetailsLeftPanel from "../GroupDetailsLeftPanel/GroupDetailsLeftPanel";

import IndustryInterestedModal from "../Modals/IndustryInterestedModal";
import InterestAddedSuccessModal from "../Modals/InterestAddedSuccessModal";

import GroupDetailsSuccessModal from "../Modals/GroupDetailsSuccessModal";
import TagsModal from "components/common/Modals/TagsModal";
import TagsAddedSuccessModal from "components/common/Modals/TagsAddedSuccessModal";
import GiveReviewModal from "components/common/Modals/GiveReviewModal";
import JoinSuccessModal from "../Modals/JoinSuccessModal";
import ReviewAddedSuccessModal from "components/common/Modals/ReviewAddedSuccessModal";

import { generateRandomString } from "utils/GetRandomString";
import ShareModal from "components/common/Modals/ShareModal";
import { assetImages } from "constants";

const GroupDetailsBody = () => {
  const { t } = useTranslation(); // translation marker

  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess");
  const params = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [isModerator, setIsModerator] = useState(false);
  const [isJoined, setisJoined] = useState(false);
  const [isApproved, setisApproved] = useState(false);

  const [copyMessage, setCopyMessage] = useState(""); // for share modal

  const [refreshGroup, setRefreshGroup] = useState(false);

  const afterModalClose = () => {
    setRefreshGroup(true);
  };

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    moduleAccess.includes("MOD_GROUP")
  ) {
    return (
      <section className="feed_pnltotal">
        {isLoading ? null : (
          <div className="container-fluid">
            <div className="back_btn">
              <Link to="/groups">
                <i className="material-icons-outlined">arrow_back</i>
                <span>{t("Back")}</span>
              </Link>
            </div>
            <div className="pnl_outer">
              {/* -------- left panel -------- */}
              <GroupDetailsLeftPanel
                isjoined={isJoined}
                setIsLoading={setIsLoading}
                setisJoined={setisJoined}
                setisApproved={setisApproved}
                setIsModerator={setIsModerator}
                setCopyMessage={setCopyMessage}
                refreshGroup={refreshGroup}
                setRefreshGroup={setRefreshGroup}
              />

              {/* ------ mdddle panel ------- */}
              <GroupDetailsMiddlePanel
                isLoading={isLoading}
                isApproved={isApproved}
                isModerator={isModerator}
                isJoined={isJoined}
                refreshGroup={refreshGroup}
                setRefreshGroup={setRefreshGroup}
              />

              {/* ------ right panel ------- */}
              {/* <GroupDetailsRightPanel similarList={similarList} /> */}
            </div>
          </div>
        )}

        {/* ------- feedback modal -------- */}
        <GiveReviewModal moduleName="group" />

        <ReviewAddedSuccessModal afterModalClose={afterModalClose} />

        {/* ------ success modal -------- */}
        {/* <GroupDetailsSuccessModal /> */}
        <JoinSuccessModal afterModalClose={afterModalClose} />

        <ShareModal copyMessage={copyMessage} />

        {/* --------- tags modal ----------- */}
        <TagsModal moduleName="groupdetails" selectedId={params.id} />

        {/* --------- tags added success / greetings modal ------------- */}
        <TagsAddedSuccessModal afterTagModalClose={afterModalClose} />
      </section>
    );
  } else {
    return (
      <section className="feed_pnltotal">
        <div className="empty_access text-center">
          <div className="empty_pic mb-4">
            {" "}
            <img src={assetImages.emptyVector} alt="" />
          </div>
          <div className="empty_text">
            <p className="fs-lg text-gray fw-semibold mb-4">
              Sorry....! You don't have privilege to see this content
            </p>
          </div>
        </div>
      </section>
    );
  }
};

export default GroupDetailsBody;
