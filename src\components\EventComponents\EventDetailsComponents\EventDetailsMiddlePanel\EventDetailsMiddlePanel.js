/* eslint-disable */
import React, { useState } from "react";
import { Link } from "react-router-dom";

import AboutTab from "./Tabs/AboutTab";
// import ProductTab from "./Tabs/ProductTab";
import MembersTab from "./Tabs/MembersTab";
import GalleryTab from "./Tabs/GalleryTab";
// import ForumTab from "./Tabs/ForumTab";
import BuyOffers from "./Tabs/BuyOffersTab";
import SellOffers from "./Tabs/SellOffersTab";
import ReviewsTab from "./Tabs/ReviewsTab";
import { useTranslation } from "react-i18next";

const EventDetailsMiddlePanel = ({
  eventType = null,
  isApproved = false,
  isModerator = false,
  isLoading = false,
  isJoined = false,
  refreshEvent = false,
  setRefreshEvent = () => {},
}) => {
  const { t } = useTranslation();

  // Define all possible tabs
  const eventDetailsTabs = [
    { id: "about", label: t("About"), component: AboutTab },
    { id: "members", label: t("Members"), component: MembersTab },
    { id: "gallery", label: t("Gallery"), component: GalleryTab },
    { id: "review", label: t("Review"), component: ReviewsTab },
  ];

  // Find index for inserting buy/sell tabs after members
  const insertIndex =
    eventDetailsTabs.findIndex((tab) => tab.id === "members") + 1;

  // Insert buy/sell tabs after members for buysell events
  if (eventType?.slug === "buysell") {
    eventDetailsTabs.splice(
      insertIndex,
      0,
      { id: "buyoffers", label: t("Purchase Offers"), component: BuyOffers },
      { id: "selloffers", label: t("Sales Offers"), component: SellOffers }
    );
  }

  // Active tab state with 'about' as default
  const [activeTab, setActiveTab] = useState("about");

  /**
   * Handle tab selection
   * @param {string} tabId - ID of the selected tab
   */
  const tabSelectionHandler = (tabId) => {
    setActiveTab(tabId);
  };

  return (
    <div className="pnl_mdlpnl" style={{ width: "70%" }}>
      <div className="people_item">
        <div className="tab_links people_tablinks">
          {isLoading ? (
            <ul className="nav nav-tabs list_stye_none">
              {Array.from({ length: 4 }).map((_, index) => (
                <li className="nav-item" key={index}>
                  <div
                    className="nav-link placeholder"
                    style={{
                      width: "100px",
                      height: "38px",
                    }}
                  ></div>
                </li>
              ))}
            </ul>
          ) : (
            <ul className="nav nav-tabs list_stye_none">
              {eventDetailsTabs.map((tab) => (
                <li className="nav-item" key={tab.id}>
                  <Link
                    className={`nav-link ${
                      activeTab === tab.id ? "active show" : ""
                    }`}
                    to="#"
                    onClick={() => {
                      tabSelectionHandler(tab.id);
                    }}
                  >
                    {tab.label}
                  </Link>
                </li>
              ))}
            </ul>
          )}
        </div>

        <div className={isLoading ? "d-none" : "tab-content"}>
          {eventDetailsTabs.map((tab) => {
            const TabComponent = tab.component;
            return (
              <div
                key={tab.id}
                className={`tab-pane fade ${
                  activeTab === tab.id ? "show active" : ""
                }`}
              >
                <TabComponent
                  tabName={activeTab === tab.id ? tab.id : ""}
                  isApproved={isApproved}
                  isModerator={isModerator}
                  isJoined={isJoined}
                  refreshEvent={refreshEvent}
                  setRefreshEvent={setRefreshEvent}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default EventDetailsMiddlePanel;
