import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { postData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

// Spanish skill labels mapping
const skillLabels = {
  AUDITORY_PERCEPTION: "Percepción Auditiva",
  AUDITORY_SHORT_TERM_MEMORY: "Memoria fonológica a corto plazo",
  CONTEXTUALIZED_MEMORY: "Memoria contextual",
  DIVIDED_ATTENTION: "Atención dividida",
  ESTIMATION: "Estimación",
  EYE_HAND_COORDINATION: "Coordinación ojo-mano",
  FOCUS_ATTENTION: "Atención Focalizada",
  INHIBITION: "Inhibición",
  NAMING: "Denominación",
  PLANNING: "Planificación",
  PROCESSING_SPEED: "Velocidad de procesamiento",
  RECOGNITION: "Reconocimiento",
  RESPONSE_TIME: "Tiempo de respuesta",
  SHIFTING: "Flexibilidad Cognitiva",
  SHORT_TERM_MEMORY: "Memoria a corto plazo",
  SPATIAL_PERCEPTION: "Percepción espacial",
  UPDATING: "Monitorización",
  VISUAL_MEMORY: "Memoria visual",
  VISUAL_PERCEPTION: "Percepción visual",
  VISUAL_SCANNING: "Exploración visual",
  VISUAL_SHORT_TERM_MEMORY: "Memoria visual a corto plazo",
  WIDTH_FIELD_VIEW: "Campo visual",
  WORKING_MEMORY: "Memoria de trabajo",
};

const AssessmentReport = () => {
  const token = localStorage.getItem("token");
  const [isLoading, setIsLoading] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');

  // Fetch assessment report
  const getAssessmentReport = async () => {
    setIsLoading(true);
    setErrorMessage('');
    try {
      let requestURL = url.API_BASE_URL + url.API_COGNIFIT_ASSESSMENT_REPORT + `?token=${token}`;
      const response = await postData(requestURL, {});

      if (response && response.data) {
        setReportData(response.data);
      } else {
        setErrorMessage('No report found.');
      }
    } catch (error) {
      setErrorMessage('Error fetching assessment report.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getAssessmentReport();
  }, []);

  // Helper for progress bar color
  const getBarColor = (value) => {
    if (value < 200) return "#e74c3c"; // red
    if (value < 400) return "#f1c40f"; // yellow
    return "#27ae60"; // green
  };

  return (
    <section className="">
      <div className="container-fluid">
        <div className="faq_pnl_wrapper">
          <div className="shadow-sm p-4 rounded-10 bg-white mb-3">
            <div className="d-flex align-items-center justify-content-between mb-3">
              <div className="d-flex align-items-center">
                <Link
                  to="/cognifit"
                  className="btn btn-light btn-sm me-3 d-flex align-items-center"
                  style={{ border: "1px solid #ddd" }}
                >
                  <i className="material-icons-outlined me-1">arrow_back</i>
                  Volver
                </Link>
                <h2 className="fs-xl text-black mb-0 fw-semibold">Informe de Evaluación</h2>
              </div>
              <button
                className="btn btn-outline-secondary d-flex align-items-center"
                type="button"
                onClick={getAssessmentReport}
                disabled={isLoading}
              >
                <i className="material-icons-outlined me-1">refresh</i>
                <span>Actualizar</span>
              </button>
            </div>
            {errorMessage && (
              <div className="alert alert-danger" role="alert">
                {errorMessage}
              </div>
            )}
            {isLoading ? (
              <div className="row">
                {Array.from({ length: 6 }).map((_, idx) => (
                  <div className="col-md-4 col-sm-6 mb-4" key={idx}>
                    <div className="d-flex align-items-center">
                      <div
                        className="placeholder-glow"
                        style={{
                          width: 48,
                          height: 48,
                          borderRadius: "50%",
                          background: "#e3f0fa",
                          marginRight: 16,
                        }}
                      />
                      <div style={{ flex: 1 }}>
                        <div className="placeholder-glow mb-2" style={{ height: 18, width: "70%" }}>
                          <span className="placeholder col-8"></span>
                        </div>
                        <div className="placeholder-glow" style={{ height: 10, width: "100%" }}>
                          <span className="placeholder col-12"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : reportData && Array.isArray(reportData.historicalScoreAndSkills) && reportData.historicalScoreAndSkills.length > 0 ? (
              reportData.historicalScoreAndSkills.map((session, idx) => (
                <div key={session.sessionId || idx} className="mb-5">
                  <div className="mb-3">
                    <div className="d-flex flex-wrap align-items-center gap-3">
                      <span className="badge bg-primary">{session.typeKey}</span>
                      <span className="text-muted">{session.date}</span>
                      {session.cognitiveAge && (
                        <span className="badge bg-info text-dark">
                          Cognitive Age: {session.cognitiveAge.age} ({session.cognitiveAge.precision})
                        </span>
                      )}
                      <span className="badge bg-success">Score: {session.score}</span>
                    </div>
                  </div>
                  <h4 className="mb-3">Cognitive Skills Status</h4>
                  <div className="row">
                    {session.skills && session.skills.map((skill) => (
                      <div className="col-md-4 col-sm-6 mb-4" key={skill.key}>
                        <div className="d-flex align-items-center">
                          {/* Optionally add icon here */}
                          <div
                            style={{
                              width: 48,
                              height: 48,
                              borderRadius: "50%",
                              background: "#e3f0fa",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              marginRight: 16,
                              fontSize: 24,
                            }}
                          >
                            {/* Placeholder for icon */}
                            <span role="img" aria-label="icon">🧠</span>
                          </div>
                          <div style={{ flex: 1 }}>
                            <div className="fw-semibold" style={{ fontSize: "1.1em" }}>
                              {skillLabels[skill.key] || skill.key}
                            </div>
                            <div className="d-flex align-items-center mt-1">
                              <div style={{ flex: 1, marginRight: 8 }}>
                                <div style={{
                                  background: "#eee",
                                  borderRadius: 8,
                                  height: 7,
                                  width: "100%",
                                  position: "relative",
                                }}>
                                  <div style={{
                                    background: getBarColor(skill.value),
                                    width: `${Math.min(100, (skill.value / (reportData.baseScore || 800)) * 100)}%`,
                                    height: "100%",
                                    borderRadius: 8,
                                    transition: "width 0.3s"
                                  }} />
                                </div>
                              </div>
                              <span style={{ minWidth: 60, fontWeight: 500 }}>
                                {skill.value} <span className="text-muted" style={{ fontWeight: 400 }}>/ {reportData.baseScore || 800}</span>
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  {/* Optionally, show categories or other session info here */}
                </div>
              ))
            ) : (
              <div>No report found.</div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AssessmentReport;
