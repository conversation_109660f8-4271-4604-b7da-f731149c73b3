/* eslint-disable */
import React, { useContext, useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import momentjs from "moment";

/*------ import url and method ------*/
import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import {
  getCalenderDateInString,
  getFullTextDateString,
} from "helper/EventHelper/EventHelper";
import UserCard from "../UserCard/UserCard";

const StepTwo = ({
  componentStyle = {},
  personName = "",
  userImage = "",
  companyName = "",
  userPosition = "",
  totalNoReviews = "0",
  expertRating = "0.0",
  userRate = 0,
  timeSlotList,
  setTimeSlotList,
  setSelectedMeetingDate,
  setSelectedTimeSlot,
  selectedTimeSlot,
  setcomments,
  setmeetingReason,
  dateValue,
  setDateValue,
  errorMessage,
  setErrorMessage,
  resetAvailibilty,
  setResetAvailibilty,
}) => {
  const params = useParams();
  const todayValue = new Date().toISOString().split("T")[0];

  const [selectedMeetingDateText, setSelectedMeetingDateText] = useState("");
  const [selectedSlotIndex, setSelectedSlotIndex] = useState(null);

  //get all time slot
  const getAvailibiltyHandler = async (value) => {
    const meetingDateText = getFullTextDateString(value._d);
    setSelectedMeetingDateText(meetingDateText);
    const selectedDate = getCalenderDateInString(value._d);

    setSelectedMeetingDate(selectedDate);

    let requestUrl =
      url.API_BASE_URL +
      url.API_GET_USER_TIMES_BY_DATE +
      `?memberid=${params.id}&agendadate=${selectedDate}`;

    const response = await getData(requestUrl);

    console.log(response);

    if (response.status && response.data.length > 0) {
      setTimeSlotList(response.data);
    }
  };

  //slot select
  const slotSelectionHandler = (val, index) => {
    setSelectedSlotIndex(index);
    setSelectedTimeSlot(val.slot);
  };

  const resetHandler = () => {
    setSelectedSlotIndex(null);
  };

  useEffect(() => {
    if (params.id) {
      getAvailibiltyHandler(momentjs(todayValue));
    }
  }, [params.id]);

  useEffect(() => {
    if (resetAvailibilty) {
      resetHandler();
      getAvailibiltyHandler(momentjs(todayValue));
      setResetAvailibilty(false);
    }
  }, [resetAvailibilty]);

  return (
    <div className="step-2" style={componentStyle}>
      <div className="row">
        <div className="col-lg-4 mb-4 mb-lg-0">
          <UserCard
            personName={personName}
            userImage={userImage}
            companyName={companyName}
            userPosition={userPosition}
            totalNoReviews={totalNoReviews}
            expertRating={expertRating}
            userRate={userRate}
          />
        </div>

        <div className="col-lg-8 d-flex align-items-start gap-3">
          <div className="calendar w-75">
            <LocalizationProvider dateAdapter={AdapterMoment}>
              <DateCalendar
                value={dateValue}
                onChange={(value) => {
                  setSelectedSlotIndex(null);
                  setErrorMessage("");
                  setDateValue(value);
                  getAvailibiltyHandler(value);
                }}
              />
            </LocalizationProvider>
          </div>
          <div className="date_list w-25">
            <p className="text-black text-center mb-4 fw-semibold">
              {selectedMeetingDateText}
            </p>
            <ul className="list_style_none">
              {timeSlotList.map((timeData, index) => {
                return (
                  <li
                    className={`bg_white px-3 py-2 rounded-4 fs-sm fw-semibold mb-2 text-center  ${
                      timeData.availability ? "text-primary" : "text-secondary"
                    } ${
                      selectedSlotIndex == index
                        ? "border border-primary rounded"
                        : ""
                    }`}
                    key={index}
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      if (timeData.availability) {
                        setErrorMessage("");
                        slotSelectionHandler(timeData, index);
                      } else {
                        setErrorMessage(
                          `Sorry, ${timeData.slot} is not available. Kindly select another slot.`
                        );
                      }
                    }}
                  >
                    {timeData.slot}
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
      <p className={errorMessage === "" ? "d-none" : "error"}>
        <span className="material-icons-outlined me-1">info</span>
        {errorMessage}
      </p>
    </div>
  );
};

export default StepTwo;
