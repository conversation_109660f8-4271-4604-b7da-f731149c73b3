import React from "react";
import { <PERSON> } from "react-router-dom";
// import use translation ------------------------------
import { useTranslation } from "react-i18next";
import { assetImages } from "constants";

const ConnectJobs = ({ autoScollContactNow, setSourceName }) => {
  const { t } = useTranslation(); // translation marker

  const contactNowHandler = () => {
    autoScollContactNow();
    setSourceName("Searching Job : Connect us");
  };
  return (
    <section className="searching_sec pd_130">
      <div className="container">
        <div className="howwrks_outer talent_outer">
          <div className="row">
            <div className="col-lg-5">
              <div className="tab_rightpnl">
                <div className="page_hdng">
                  <h5>{t("For Professionals")}</h5>
                  <h2>
                    {t("Searching")} <br /> {t("For A new")} <br /> {t("Job")}?
                  </h2>
                </div>
                <div className="talent_para">
                  <p>
                    {t(
                      "If you are a professional, we connect you with companies to work on projects that are generally international and 100% remote."
                    )}
                  </p>
                </div>
                <div className="talent_btn">
                  <Link
                    to="#"
                    className="btn black_btn"
                    onClick={contactNowHandler}
                  >
                    {t("Connect Us")}
                  </Link>
                </div>
              </div>
            </div>
            <div className="col-lg-7">
              <div className="tab_leftpnl">
                <div className="tab_pic">
                  <img src={assetImages.searchingJobImg} alt="" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ConnectJobs;
