.empty_access {
  padding: 100px 0px;
}

.thumb_logo {
  width: 80px;
  height: 80px;
  border-radius: 10%;
}

.tag .active {
  background: var(--bs-color-4E) !important;
  color: var(--bs-color-00);
  /* border-color: rgba(var(--bs-primary-rgb), 0.26) !important; */
}

.top_usr_srch {
  padding-left: 0 !important;
}

/* activity css */
.search_box a {
  padding-top: 18px !important;
  padding-bottom: 18px !important;
}

.search_dropdown {
  padding: 15px;
  border-radius: 90px;
  color: var(--bs-color-37);
  background-color: var(--bs-color-F2);
  /* border: 1px solid var(--bs-color-DC); */
}

.appuser_search_box {
  max-height: 400px;
  overflow-y: auto;
}

/* cv builder css */
.option_btns_active {
  background-color: var(--bs-color-4E);
  border-color: var(--bs-color-4E);
  color: var(--bs-color-00);
}

/* Cart Css starts ---------------------------- */

.pt-70 {
  padding-top: 70px;
}

.fs-sm {
  font-size: 14px;
}

.fs-md {
  font-size: 18px;
}

.fs-lg {
  font-size: 20px;
}

.fs-xl {
  font-size: 30px;
}

.text-black {
  color: #000;
}

.text-gray {
  color: #5a6471;
}

.text-green {
  color: #31a61e;
}

.text-ornge {
  color: #e8604d;
}

.fw-medium {
  font-weight: 500;
}

.fw-semibold {
  font-weight: 600;
}

.rounded-8 {
  border-radius: 8px;
}

.rounded-10 {
  border-radius: 10px;
}

.light-green-bg {
  background-color: #e2efe6;
}

.light-ornge-bg {
  background-color: #ffeedf;
}

.bg-white {
  background-color: #fff;
}

/* commented because of this will conflict in other css like market place  */
/* .bg-light {
  background-color: #d3e3fd !important;
} */

.bg-extra-light {
  background-color: #f2f6fd !important;
}

.cart_leftpnl {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1),
    0px 0px 1px 0px rgba(0, 0, 0, 0.25);
}

.number .plus,
.number .minus {
  height: 34px;
  width: 34px;
  min-width: 34px;
  cursor: pointer;
}

.number .form-control {
  height: 34px;
  width: 63px;
  box-shadow: none;
  border-color: #d3e3fd;
}

.cart_item {
  border-bottom: 1px solid #d3e3fd;
}

.cart_item:last-child {
  border-bottom: 0;
}

.right_cart_info {
  padding-left: 40px;
  text-align: right;
}

.form-select {
  width: 115px;
}

.form-select:focus {
  box-shadow: none;
}

.cart_rightPnl {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1),
    0px 0px 1px 0px rgba(0, 0, 0, 0.25);
  border: 1px solid #99a1aa;
}

.oredr_row.pt-3 {
  border-top: 1px solid #d3e3fd;
}

.event_loc {
  min-width: max-content;
}

/* chat css  */
.enter_group_name .form-control {
  border: 1px solid #d8d8d8 !important;
  height: auto !important;
}

/* css for reciever side  */
.reciever_uploads {
  width: 150px;
  min-width: 150px;
  background-color: #dfdfdf;
}

.reciever_uploads .chat_upload_caption p {
  color: #565659;
}

.reciever_uploads .chat_upload_pic {
  height: 120px;
}

.chat_expand_arws {
  top: 14px !important;
}

/* offcanvas width */
.offcanvas-sm {
  width: 300px;
}

.offcanvas-md {
  width: 600px;
  max-width: none;
}

.offcanvas-lg {
  width: 900px;
  max-width: none;
}

.offcanvas-xl {
  width: 1140px;
  max-width: none;
}

@media (max-width: 991px) {
  .cart_rightPnl {
    margin-top: 20px;
  }
}

@media (max-width: 767px) {
  .right_cart_info {
    padding-left: 0;
    text-align: left;
  }

  .right_cart_info {
    margin-top: 20px;
  }

  .selectbox,
  .payment_info h3 {
    justify-content: flex-start !important;
  }

  .fs-xl {
    font-size: 22px;
  }
}

/* Cart css ends ----------------------------------- */

/* email popup css */
.emailpopup_open .email_popup_view {
  z-index: 9999;
  min-width: 70%;
}

/* faq css  */
.ask_btn {
  width: max-content;
}

/* course css  */
.pending_status span {
  color: var(--bs-color-E8);
}

/* meeting css  */
.date_list ul {
  max-height: 300px;
  overflow-y: auto;
}

.availibiliy {
  max-width: 500px;
  overflow-x: scroll;
}

.availibiliy ul > li > span {
  width: max-content !important;
}

.custom-offcanvas-width {
  width: 65% !important; /* Override the default Bootstrap width */
  max-width: 65% !important; /* Ensure it doesn't exceed 65% */
}

.custom-offcanvas-width-90 {
  width: 90% !important; /* Override the default Bootstrap width */
  max-width: 90% !important; /* Ensure it doesn't exceed 90% */
}

.border-dark-gray {
  border: 1px solid #c9c6ff;
}

.bids_counter_item {
  background: linear-gradient(180deg, #e8f3ff 0%, #fff 22.82%);
}

#appuser_filter_offcanvas {
  max-width: 400px;
}

#company_filter_offcanvas {
  max-width: 400px;
}

#event_filter_offcanvas {
  max-width: 400px;
}

#course_filter_offcanvas {
  max-width: 400px;
}

#group_filter_offcanvas {
  max-width: 400px;
}

#faq_filter_offcanvas {
  max-width: 400px;
}

#job_filter_offcanvas {
  max-width: 400px;
}

#forum_filter_offcanvas {
  max-width: 400px;
}

/* challegene css  */
.results_rightPnl {
  max-height: 550px;
  overflow-y: scroll;
}

.offcanvas_close_btn {
  border-radius: 50%;
  padding: 5px;
  background-color: var(--bs-color-E5);
  color: var(--bs-color-5A);
}
