/* eslint-disable */
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";

const HobbiesModal = ({ userHobbies, setuserHobbies }) => {
  const hobbiesText = "";

  const [hobbiesBlock, setHobbiesBlock] = useState([]);

  const addNewHobbiesBlock = () => {
    setHobbiesBlock([...hobbiesBlock, hobbiesText]);
  };

  const deleteHobbiesBlock = (index) => {
    const deleteHobbiesArr = [...hobbiesBlock];
    deleteHobbiesArr.splice(index, 1);
    setHobbiesBlock(deleteHobbiesArr);
  };

  //function for change survey select
  const hobbiesInputHandler = (index, field, value) => {
    const updatedFormValues = hobbiesBlock.map((block, i) =>
      index == i ? (block = value) : block
    );

    setHobbiesBlock(updatedFormValues);
  };

  const updatedHobbiesBlock = (updatedData) => {
    setHobbiesBlock(updatedData);
  };

  const saveInfoHandler = () => {
    if (hobbiesBlock.length > 0) {
      setuserHobbies(hobbiesBlock);
    } else {
      setuserHobbies([]);
    }

    resetHandler();

    let loginModal = document.querySelector("#hobbies_modal");
    let modal = bootstrap.Modal.getInstance(loginModal);
    modal.hide();
  };

  const resetHandler = () => {
    setHobbiesBlock([]);
  };

  useEffect(() => {
    if (userHobbies.length > 0) {
      updatedHobbiesBlock(userHobbies);
    }
  }, [userHobbies]);

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade show" id="hobbies_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Hobbies</h3>
                <h5>Add your interests or favourite activities</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                // onClick={resetHandler}
              >
                <i className="material-icons-outlined">close </i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    {hobbiesBlock.map((hobbiesData, index) => {
                      return (
                        <div className="d-flex gap-1 mb-2" key={index}>
                          <input
                            type="text"
                            className="form-control"
                            placeholder="Enter Links"
                            value={hobbiesData}
                            onChange={(e) => {
                              hobbiesInputHandler(
                                index,
                                "hobbiestext",
                                e.target.value
                              );
                            }}
                          />
                          <div className="adnew_btn">
                            <Link
                              onClick={() => {
                                deleteHobbiesBlock(index);
                              }}
                              to="#"
                              className="d-inline-flex align-items-center justify-content-center gap-1"
                            >
                              <i className="material-icons-outlined">delete</i>
                            </Link>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  <div className="form-group">
                    <div className="adnew_btn">
                      <Link
                        to="#"
                        className="d-inline-flex align-items-center justify-content-center gap-1"
                        onClick={addNewHobbiesBlock}
                      >
                        <i className="material-icons-outlined">add </i>
                        <span>Add New</span>
                      </Link>
                    </div>
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HobbiesModal;
