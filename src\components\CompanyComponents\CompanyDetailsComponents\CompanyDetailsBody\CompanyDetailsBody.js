/* eslint-disable */
import { useState } from "react";
import { Link } from "react-router-dom";

import { assetImages } from "constants";
import CompanyDetailsLeftPanel from "../CompanyDetailsLeftPanel/CompanyDetailsLeftPanel";
import CompanyDetailsRightPanel from "../CompanyDetailsRightPanel/CompanyDetailsRightPanel";

import GiveReviewModal from "components/common/Modals/GiveReviewModal";

import ReviewAddedSuccessModal from "components/common/Modals/ReviewAddedSuccessModal";

const CompanyDetailsBody = () => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  const moduleAccess = localStorage.getItem("moduleaccess");

  const [companyPanelReload, setcompanyPanelReload] = useState(false);

  const afterModalClose = () => {
    setcompanyPanelReload(true);
  };

  if (
    userInfo.role.slug === "ADMIN" ||
    userInfo.role.slug === "SUPER_ADMIN" ||
    moduleAccess.includes("MOD_COMPANY")
  ) {
    return (
      <section className="feed_pnltotal">
        <div className="container-fluid">
          <div className="back_btn">
            <Link to="/company">
              <i className="material-icons-outlined">arrow_back</i>
              <span>Back</span>
            </Link>
          </div>
          <div className="pnl_outer company_details_outer">
            {/* -------- left panel -------- */}
            <CompanyDetailsLeftPanel
              companyPanelReload={companyPanelReload}
              setcompanyPanelReload={setcompanyPanelReload}
            />

            {/* ------ right panel ------- */}
            <CompanyDetailsRightPanel
              companyPanelReload={companyPanelReload}
              setcompanyPanelReload={setcompanyPanelReload}
            />
          </div>
        </div>

        {/* ----------- company details feedback modal -------------- */}
        <GiveReviewModal moduleName="company" />

        <ReviewAddedSuccessModal afterModalClose={afterModalClose} />
      </section>
    );
  } else {
    return (
      <section className="feed_pnltotal">
        <div className="empty_access text-center">
          <div className="empty_pic mb-4">
            {" "}
            <img src={assetImages.emptyVector} alt="" />
          </div>
          <div className="empty_text">
            <p className="fs-lg text-gray fw-semibold mb-4">
              Sorry....! You don't have privilege to see this content
            </p>
          </div>
        </div>
      </section>
    );
  }
};

export default CompanyDetailsBody;
