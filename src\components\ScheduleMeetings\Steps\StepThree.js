import React from "react";

import UserCard from "../UserCard/UserCard";

const StepThree = ({
  componentStyle = {},
  personName = "",
  userImage = "",
  companyName = "",
  userPosition = "",
  totalNoReviews = "0",
  expertRating = "0.0",
  userRate = 0,
  selectedMeetingDate = "",
  selectedTimeSlot = "",
  userInputName = "",
  setuserInputName = () => {},
  userEmail = "",
  setuserEmail = () => {},
  meetingDetails = "",
  setMeetingDetails = () => {},
  emailError = false,
  setemailError = () => {},
}) => {
  return (
    <div className="step-3" style={componentStyle}>
      <div className="row">
        <div className="col-lg-6 mb-4 mb-lg-0">
          <UserCard
            personName={personName}
            userImage={userImage}
            companyName={companyName}
            userPosition={userPosition}
            totalNoReviews={totalNoReviews}
            expertRating={expertRating}
            userRate={userRate}
            currentStep={3}
            selectedMeetingDate={selectedMeetingDate}
            selectedTimeSlot={selectedTimeSlot}
          />
        </div>

        <div className="col-lg-6">
          <h4 className="fw-bold mb-4">ENTER DETAILS</h4>
          <div className="form-group mb-3">
            <label htmlFor="name">Name</label>
            <input
              type="text"
              id="name"
              className="form-control shadow-none"
              placeholder="Enter Name"
              value={userInputName}
              onChange={(e) => setuserInputName(e.target.value)}
            />
          </div>
          <div className="form-group mb-3">
            <label htmlFor="email">
              Enter email <span className="text-danger fw-bold">*</span>
            </label>
            <input
              type="email"
              id="email"
              className="form-control shadow-none"
              placeholder="Email"
              value={userEmail}
              onChange={(e) => {
                setuserEmail(e.target.value);
                setemailError(false);
              }}
            />
            <p className={emailError ? "error mt-1" : "d-none"}>
              <span className="material-icons-outlined me-1">info</span>
              Please enter a valid email
            </p>
          </div>
          <div className="form-group">
            <label htmlFor="meetingDetails">Meeting Details</label>
            <textarea
              name="meetingDetails"
              id="meetingDetails"
              cols="30"
              rows="5"
              className="form-control"
              placeholder="Enter details"
              value={meetingDetails}
              onChange={(e) => setMeetingDetails(e.target.value)}
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepThree;
