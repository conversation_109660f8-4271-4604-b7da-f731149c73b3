/* eslint-disable */
import { useState, useEffect } from "react";

import SaveI<PERSON> from "./SaveIdea";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";

import GiveReviewModal from "components/common/Modals/GiveReviewModal";
import { getDateFormatInSpanish } from "helper/Common/CommonHelper";

const ListIdeas = ({ challengeResponseId, challengeId, userId, showIdea }) =>{

    const token = localStorage.getItem("token");
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));

    const [ideas, setIdeas] = useState([]);
    const [selectedIdea, setSelectedIdea] = useState(null);
    const [showSaveIdea, setShowSaveIdea] = useState(false);

    const [fetchingData, setFetchingData] = useState(false);

    useEffect(() => {

        console.log('challengeResponseId', challengeResponseId);
        console.log('userId', userId);

        if(showIdea){
            fetchIdeas();
        }        

    }, [userId, challengeResponseId, showIdea]);


    // get all ideas sent to the user for the challenge
    const fetchIdeas = async () => {

        // show listIdeasOffcanvas
        // const offcanvasElement = document.getElementById('listIdeasOffcanvas');
        // const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        // bsOffcanvas.show();
        setIdeas([]);

        try {

            let requestURL = url.API_BASE_URL + url.API_LIST_IDEA + `?token=${token}&challengeresponse=${challengeResponseId}&challenge=${challengeId}`;
            
            console.log('get ideas requestURL', requestURL);

            const response = await getData(requestURL);
            
            console.log('get ideas response', response);
            
            if(response.status){
                setIdeas(response.data);
            }

        } catch (error) {
            console.log("error", error);
        }
        
    };

    // open Givereview modal with id feedback_mdl
    const givereview = (idea) => {

        setSelectedIdea(idea);

        let reviewModal = document.getElementById("feedback_mdl");
        let offcanvas = document.getElementById("listIdeasOffcanvas");

        if (reviewModal && offcanvas) {
            // Close the offcanvas
            let offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvas);
            if (offcanvasInstance) {
                offcanvasInstance.hide();
            }

            // Wait for the offcanvas animation to finish before showing the modal
            //setTimeout(() => {
                let modal = new bootstrap.Modal(reviewModal);
                modal.show();
            //}, 500); 
        }
    };


    const handleDetails = (idea) => {
        setSelectedIdea(idea);
        setShowSaveIdea(true);
        handleClose();
    };

    const handleClose = () => {
        const offcanvasElement = document.getElementById('saveIdeaOffcanvas');
        const bsOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        bsOffcanvas.show();
    };



    useEffect(() => {
        if(fetchingData){
            console.log('fetchingData', fetchingData);
            fetchIdeas();
        }
        setFetchingData(false);
    }
    , [fetchingData]);

    return (
        <>
        <div
            className="offcanvas offcanvas-end offcanvas-lg"
            tabIndex="-1"
            id="listIdeasOffcanvas"
        >
            <div className="offcanvas-header">
            <h3>Suggested Ideas</h3>
            <button
                type="button"
                className="btn-close"
                data-bs-dismiss="offcanvas"
            ></button>
            </div>
            <div className="offcanvas-body">
                <div className="d-flex justify-content-end mb-3">
                    <button
                        className="btn outline mb-3"
                        data-bs-toggle="offcanvas"
                        data-bs-target="#saveIdeaOffcanvas"
                        onClick={() => 
                            setSelectedIdea(null)
                        }
                    >
                        Add New
                    </button>
                </div>

                <table className="table table-bordered">
                    <thead>
                    <tr>
                        <th>Title</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    {ideas.map((idea) => (
                        <tr key={idea._id}>
                        <td>{idea.title}</td>
                        <td>{new Date(idea.createdAt).toLocaleDateString()}</td>
                        <td>Viewed : {idea.isread ? 'Yes' : 'No'}
                            <br />
                            Response : {idea.isapproved ? 'Approved' : idea.isrejected ? 'Rejected' : 'Pending'}
                            
                        </td>
                        <td className="d-flex">
                            <div className="me-3">
                            <button
                            className=""
                            onClick={() => handleDetails(idea)}
                            >
                                <i className="material-icons-outlined">edit</i>
                            </button>
                            </div>
                            <div className="ms-3">
                            <button
                            className=""
                            onClick={() => givereview(idea)}
                            >
                                <i class="material-icons-outlined">feedback</i>
                            </button>
                            </div>
                        </td>
                        </tr>
                    ))}
                    </tbody>
                </table>
            </div>
        </div>
        
        {/* Save Idea Offcanvas */}
        <SaveIdea 
            idea={selectedIdea} 
            challengeId={challengeId}
            challengeResponseId={challengeResponseId}
            receiverId={userId} 
            setFetchingData={setFetchingData} 
            />

        {/* Give review modal */}
        <GiveReviewModal 
            moduleName="idea" 
            componentid={selectedIdea?selectedIdea._id:null}
            receiverid={userId}
            />

        </>
    );
}

export default ListIdeas;
