/* eslint-disable */
import React, { useEffect, useState } from "react";

/*import url and gateway methods */
import { getData, postData, putData } from "utils/Gateway";
import * as url from "helper/UrlHelper";

import { makeStripePayment } from "helper/StripePaymentHelper/StripePaymentHelper";

const BuyProductQuotationPopup = ({
  eventid,
  buyprice,
  galleryImages,
  endDate,
  onClose,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const [eventProducts, setEventProducts] = useState([]);
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quotationId, setQuotationId] = useState("");

  const [quantity, setQuantity] = useState(1);
  const [price, setPrice] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentId, setPaymentId] = useState("");
  const [isEditing, setIsEditing] = useState(false);

  // Function to get event products
  const getEventProducts = async () => {
    resetStates();

    try {
      setIsLoading(true);
      const requestURL =
        url.API_BASE_URL +
        url.API_GET_EVENT_PRODUCTS +
        `/${eventid}` +
        `?token=${token}`;

      const response = await getData(requestURL);
      setIsLoading(false);

      if (response.status) {
        setEventProducts(response.data);
        if (response.data.length > 0) {
          setProducts(response.data.map((item) => item.product));
          setSelectedProduct(response.data[0].product); // Set the first product as selected by default
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // get saved quotation details for this eventid, productid and customerid
  const getSavedQuotation = async () => {
    try {
      setIsLoading(true);
      let requestURL =
        url.API_BASE_URL + url.API_GET_ALL_QUOTATIONS + `?token=${token}`;

      requestURL += `&eventid=${eventid}`;
      requestURL += `&productid=${selectedProduct?._id}`;
      requestURL += `&customerid=${userInfo._id}`;
      requestURL += `&type=buy`; // Assuming type is always 'buy' for this case

      const response = await getData(requestURL);

      console.log("getSavedQuotation response:", response);

      setIsLoading(false);
      if (response.status) {
        // Handle successful response
        if (response.data && response.data.length > 0) {
          const savedQuotation = response.data[0];
          setQuotationId(savedQuotation._id); // Save the quotation ID
          setQuantity(savedQuotation.units || 1); // Set quantity from saved quotation or default to 1
          setPrice(savedQuotation.totalprice || 0); // Set price from saved quotation or default to 0

          if (savedQuotation.payment && savedQuotation.payment !== null) {
            setPaymentId(savedQuotation.payment);
          }
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // Function to save quotation
  const saveQuotation = async () => {
    try {
      setIsLoading(true);

      const quotationData = {
        eventid: eventid,
        productid: selectedProduct?._id,
        units: quantity,
        totalprice: price,
        type: "buy", // Assuming type is always 'buy' for this case
        processingfee: buyprice,
      };

      if (quotationId) {
        const requestURL =
          url.API_BASE_URL +
          url.API_UPDATE_QUOTATION +
          `/${quotationId}?token=${token}`;

        const response = await putData(requestURL, quotationData);

        console.log("saveQuotation response:", response);
        setIsLoading(false);

        if (response.status) {
          setQuotationId(response.data._id); // Save the quotation ID from the response
          setIsEditing(false); // Exit editing mode after saving
        }
      } else {
        const requestURL =
          url.API_BASE_URL + url.API_SAVE_QUOTATION + `?token=${token}`;

        const response = await postData(requestURL, quotationData);

        console.log("saveQuotation response:", response);
        setIsLoading(false);

        if (response.status) {
          setQuotationId(response.data._id); // Save the quotation ID from the response
        }
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // Function to generate invoice
  const generateInvoice = async () => {
    try {
      setIsLoading(true);
      const requestURL =
        url.API_BASE_URL +
        url.API_REQUEST_QUOTATION_INVOICE +
        `?token=${token}`;

      const invoiceData = {
        eventid: eventid,
        quotationid: quotationId,
        amount: buyprice,
        duedate: endDate,
      };

      console.log("generateInvoice invoiceData:", requestURL, invoiceData);

      const response = await postData(requestURL, invoiceData);

      console.log("generateInvoice response:", response);

      if (response.status) {
        // Handle successful invoice generation
        const invoiceData = response.data;

        const invoiceId = invoiceData._id;

        const redirectUrl =
          url.FRONTEND_BASE_URL + "/events/details/" + eventid;

        if (invoiceId) {
          await makeStripePayment(invoiceId, token, userInfo._id, redirectUrl);
        }

        setIsLoading(false);
      }
    } catch (error) {
      console.log(error.message);
      setIsLoading(false);
    }
  };

  // reset all states when the popup is closed
  const resetStates = () => {
    setEventProducts([]);
    setProducts([]);
    setSelectedProduct(null);
    setQuotationId("");
    setQuantity(1);
    setPrice(0);
    setIsLoading(false);
    setPaymentId("");
    setIsEditing(false);
  };

  // Fetch event products on component mount
  useEffect(() => {
    getEventProducts();
  }, [eventid]);

  // when selected product is changed, fetch saved quotation details
  useEffect(() => {
    if (selectedProduct) {
      getSavedQuotation();
    }
  }, [selectedProduct]);

  return (
    <div
      className="offcanvas offcanvas-end custom-offcanvas-width"
      tabIndex="-1"
      id="offcanvasBuyQuotation"
      aria-labelledby="offcanvasBuyQuotationLabel"
      aria-modal="true"
      role="dialog"
    >
      <div className="offcanvas-header p-4 shadow-sm">
        <div className="d-flex gap-2 medicine_wrap">
          <div className="sell_pic rounded-10 overflow-hidden">
            <img src="images/medicine-pic.jpg" alt="" />
          </div>
          <div className="sell_info">
            <h3 className="fw-semibold mb-2 text-black fs-xl">Buy Quotation</h3>
          </div>
        </div>
        <div className="d-flex align-items-center gap-2 add_watch_wrap">
          <button
            type="button"
            className="btn-close text-reset bg-light p-3 rounded-circle"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
            onClick={onClose}
          ></button>
        </div>
      </div>

      {selectedProduct && (
        <div className="offcanvas-body p-4">
          <div className="sell_canvas_wrapper">
            <div className="row gy-3">
              <div className="col-lg-7">
                {/* Product Details */}
                <div className="selling_pnl_item border mb-3 rounded-10 overflow-hidden">
                  <div className="p-3 sell_product_details">
                    <h4 className="fs-lg fw-semibold mb-3 text-black">
                      Details Of the Product
                    </h4>
                    <div className="row gy-3">
                      <div className="col-md-12 col-12">
                        <div className="sell_details_info">
                          <h5 className="fs-sm text-gray mb-2">Name</h5>
                          <p className="text-black fw-semibold mb-0 fs-sm">
                            {selectedProduct ? selectedProduct.name : ""}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6 col-12">
                        <div className="sell_details_info">
                          <h5 className="fs-sm text-gray mb-2">Company</h5>
                          <p className="text-black fw-semibold mb-0 fs-sm">
                            {selectedProduct?.companyid?.name ?? ""}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6 col-12">
                        <div className="sell_details_info">
                          <h5 className="fs-sm text-gray mb-2">Category</h5>
                          <p className="text-black fw-semibold mb-0 fs-sm">
                            {selectedProduct.categoryids &&
                            selectedProduct.categoryids.length > 0
                              ? selectedProduct.categoryids
                                  .map((cat) => cat.name)
                                  .join(", ")
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6 col-12">
                        <div className="sell_details_info">
                          <h5 className="fs-sm text-gray mb-2">SKUs</h5>
                          <p className="text-black fw-semibold mb-0 fs-sm">
                            {selectedProduct?.sku ?? "N/A"}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-6 col-12">
                        <div className="sell_details_info">
                          <h5 className="fs-sm text-gray mb-2">Precio</h5>
                          <p className="text-black fw-semibold mb-0 fs-sm">
                            {selectedProduct?.price
                              ? `€${selectedProduct.price.toFixed(2)}`
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                      <div className="col-md-12 col-12">
                        <div className="sell_details_info">
                          <h5 className="fs-sm text-gray mb-2">Summary</h5>
                          <p className="text-black fw-normal mb-0 fs-sm">
                            {selectedProduct?.summary ?? "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Documentos Section */}
                <div className="border-top p-3 sell_product_docs">
                  <h4 className="fs-lg fw-semibold mb-3 text-black">
                    Documentos
                  </h4>
                  <div className="upload_area row">
                    {galleryImages && galleryImages.length > 0 ? (
                      galleryImages.map((image, index) => (
                        <div className="col-md-4 col-6 mb-3" key={index}>
                          <div className="upload_item">
                            <img
                              src={url.SERVER_URL + image.path}
                              alt={`Document ${index + 1}`}
                              className="img-fluid rounded-10"
                            />
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted">No documents uploaded</p>
                    )}
                  </div>
                </div>
              </div>

              <div className="col-lg-5">
                <div className="bids_rightpnl">
                  {/* Quotation Deposit Section */}
                  <div className="live_bidding border rounded-10 p-3 mb-3">
                    <h4 className="fs-lg fw-semibold mb-3 text-black">
                      Quotation Deposit
                    </h4>
                    <div className="bidding_item d-flex align-items-center gap-3 mb-3 justify-content-between">
                      <div className="bid_info">
                        <h5 className="text-black mb-1 fs-md">Amount to pay</h5>
                        <p className="text-success fw-semibold mb-0 fs-lg">
                          €{buyprice ? buyprice.toFixed(2) : "0.00"}
                        </p>
                      </div>
                      <div className="bid_timer">
                        <h5 className="text-black mb-1 fs-md">End Date</h5>
                        <p className="text-danger fw-normal mb-0 fs-md">
                          {endDate
                            ? new Date(endDate).toLocaleDateString("en-GB")
                            : "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Quotation Section */}
                  <div className="border rounded-10 p-3">
                    <div className="d-flex justify-content-between align-items-center">
                      <h4 className="fs-lg fw-semibold mb-3 text-black">
                        {quotationId ? "Quotation Details" : "Your Quotation"}
                      </h4>
                      {quotationId && !paymentId && (
                        <button
                          className="btn btn-sm btn-outline-secondary"
                          onClick={() => setIsEditing(true)} // Enable editing mode
                        >
                          <i className="material-icons">edit</i>
                        </button>
                      )}
                    </div>

                    {!quotationId ? (
                      <>
                        <div className="row gy-3">
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Quantity
                              </label>
                              <input
                                type="number"
                                className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                value={quantity}
                                onChange={(e) => setQuantity(e.target.value)}
                                min="1"
                                step="1"
                              />
                            </div>
                          </div>
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Max Price
                              </label>
                              <div className="input-group">
                                <span className="input-group-text">€</span>
                                <input
                                  type="number"
                                  className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                  value={price}
                                  onChange={(e) => setPrice(e.target.value)}
                                  min="0"
                                  step="1"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="text-center mt-3">
                          <button
                            className="btn btn-primary rounded-10 px-4 py-2 fs-md fw-semibold"
                            onClick={saveQuotation}
                            disabled={isLoading || price <= 1} // Disable button if price <= 1 or loading
                          >
                            {isLoading ? "Submitting..." : "Confirm & Submit"}
                          </button>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="row gy-3">
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Quantity
                              </label>
                              {isEditing ? (
                                <input
                                  type="number"
                                  className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                  value={quantity}
                                  onChange={(e) => setQuantity(e.target.value)}
                                  min="1"
                                  step="1"
                                />
                              ) : (
                                <p className="text-black fw-semibold fs-md">
                                  {quantity}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="col-md-6">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Max Price
                              </label>
                              {isEditing ? (
                                <div className="input-group">
                                  <span className="input-group-text">€</span>
                                  <input
                                    type="number"
                                    className="form-control bg-gray px-4 py-3 h-auto rounded-10 border-gray-100 shadow-none"
                                    value={price}
                                    onChange={(e) => setPrice(e.target.value)}
                                    min="0"
                                    step="1"
                                  />
                                </div>
                              ) : (
                                <p className="text-black fw-semibold fs-md">
                                  €{Number(price).toFixed(2)}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="row gy-3">
                          <div className="col-md-12">
                            <div className="form-group">
                              <label className="text-black mb-2 fs-sm fw-semibold d-block">
                                Payment Status
                              </label>
                              <p
                                className={`fw-semibold fs-md ${
                                  paymentId ? "text-success" : "text-danger"
                                }`}
                              >
                                {paymentId ? "Paid" : "Pending"}
                              </p>
                            </div>
                          </div>
                        </div>
                        {isEditing && (
                          <div className="text-center mt-3">
                            <button
                              className="btn btn-primary rounded-10 px-4 py-2 fs-md fw-semibold"
                              onClick={saveQuotation}
                              disabled={isLoading || price <= 1} // Disable button if price <= 1 or loading
                            >
                              {isLoading ? "Saving..." : "Save Changes"}
                            </button>
                          </div>
                        )}
                        {!paymentId && !isEditing && (
                          <div className="text-center mt-3">
                            <button
                              className="btn btn-primary rounded-10 px-4 py-2 fs-md fw-semibold"
                              onClick={generateInvoice}
                              disabled={isLoading}
                            >
                              {isLoading ? "Processing..." : "Make Payment"}
                            </button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BuyProductQuotationPopup;
