/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Link, useParams } from "react-router-dom";

import { useTranslation } from "react-i18next";

import * as url from "helper/UrlHelper";
import { getData } from "utils/Gateway";
import { assetImages } from "constants";

const ProcessTab = ({ tabName = "" }) => {
  const { t } = useTranslation();
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const params = useParams();

  const [processList, setprocessList] = useState([]);
  const [isLoading, setisLoading] = useState(false);

  const getAllJobProcess = async () => {
    try {
      setisLoading(true);

      let requestUrl =
        url.API_BASE_URL +
        url.API_GET_ALL_JOB_PROCESS +
        `?token=${token}&challengeid=${params.id}`;

      const response = await getData(requestUrl);

      console.log(response);

      setisLoading(false);

      if (response.status) {
        setprocessList(response.data);
      }
    } catch (error) {
      console.log(error.message);
      setisLoading(false);
    }
  };

  useEffect(() => {
    if (params.id && tabName === "process") {
      getAllJobProcess();
    }
  }, [params.id, tabName]);

  const formatDate = (date) => {
    if (!date) return "N/A"; // Return "N/A" if date is not provided
    const [year, month, day] = date.split("-");
    return `${day}-${month}-${year}`;
  };

  // Loading placeholder component
  const LoadingPlaceholder = () => (
    <div className="dates_pnlOuter">
      {[1, 2].map((item, index) => (
        <div
          className="dates_pnlItem position-relative overflow-hidden"
          key={index}
        >
          <span className="dates_nmbr glow-placeholder"></span>
          <div className="dates_hdng">
            <h4
              className="glow-placeholder"
              style={{ width: "70%", height: "20px" }}
            ></h4>
          </div>
          <div className="event_tags">
            <ul className="list_stye_none">
              <li>
                <i className="material-icons-outlined glow-placeholder">
                  place
                </i>
                <span
                  className="glow-placeholder"
                  style={{ width: "150px", height: "16px" }}
                ></span>
              </li>
              <li>
                <i className="material-icons-outlined glow-placeholder">
                  event_note
                </i>
                <span
                  className="glow-placeholder"
                  style={{ width: "200px", height: "16px" }}
                ></span>
              </li>
            </ul>
          </div>
          <div className="dates_listing">
            <h3
              className="glow-placeholder"
              style={{ width: "50%", height: "18px" }}
            ></h3>
            <ul className="list_stye_none">
              {[1, 2, 3].map((kpi, index2) => (
                <li key={index2}>
                  <i className="material-icons-outlined glow-placeholder">
                    done
                  </i>
                  <span
                    className="glow-placeholder"
                    style={{ width: "120px", height: "16px" }}
                  ></span>
                </li>
              ))}
            </ul>
          </div>
          <style jsx>{`
            .glow-placeholder {
              animation: glow 1.5s ease-in-out infinite;
              background: #f6f7f8;
              background: linear-gradient(
                to right,
                #f6f7f8 8%,
                #edeef1 18%,
                #f6f7f8 33%
              );
              background-size: 800px 104px;
              border-radius: 4px;
              display: inline-block;
              position: relative;
            }
            @keyframes glow {
              0% {
                background-position: -200px 0;
              }
              100% {
                background-position: 200px 0;
              }
            }
          `}</style>
        </div>
      ))}
    </div>
  );

  return (
    <div className="white_shadow_bx people_bx">
      {isLoading ? (
        <LoadingPlaceholder />
      ) : processList.length === 0 ? (
        <div className="pnl_mdlpnl w-100">
          <div className="empty_access text-center">
            <div className="empty_pic mb-4">
              {" "}
              <img src={assetImages.emptyVector} alt="" />
            </div>
            <div className="empty_text">
              <p className="fs-md text-gray fw-semibold">
                Sorry....! Processlist not availbale for this job
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="dates_pnlOuter">
          {processList.map((process, index) => {
            return (
              <div className="dates_pnlItem" key={index}>
                <span className="dates_nmbr">{process.order}</span>
                <div className="dates_hdng">
                  <h4>{process.title}</h4>
                </div>
                <div className="event_tags">
                  <ul className="list_stye_none">
                    <li>
                      <i className="material-icons-outlined">place</i>
                      <span>{process.location}</span>
                    </li>
                    <li>
                      <i className="material-icons-outlined">event_note</i>
                      <span>
                        {formatDate(process.startdate)} to{" "}
                        {formatDate(process.enddate)}
                      </span>
                    </li>
                    {/* <li>
                    <i className="material-icons-outlined">person_outline</i>
                    <span>15 Participants</span>
                  </li> */}
                  </ul>
                </div>
                <div className="dates_listing">
                  <h3>KPIs for evaluation</h3>
                  <ul className="list_stye_none">
                    {process.kpisarr.map((kpis, index2) => {
                      return (
                        <li key={index2}>
                          <i className="material-icons-outlined"> done</i>{" "}
                          {kpis.skillname}
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default ProcessTab;
