/* eslint-disable */
import { useState, useEffect } from "react";

const FinancialProjectionsModal = ({ capitalQuestResponseData, setCapitalQuestResponseData }) => {
  const [formData, setFormData] = useState({ ...capitalQuestResponseData });

  useEffect(() => {
    setFormData({ ...capitalQuestResponseData });
  }, [capitalQuestResponseData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const resetHandler = () => {
    setFormData({ ...capitalQuestResponseData });
  };

  const saveInfoHandler = () => {
    setCapitalQuestResponseData((prevData) => ({
      ...prevData,
      revenueprojections: formData.revenueprojections,
      breakevenpoint: formData.breakevenpoint,
      mainassumptions: formData.mainassumptions,
      scenarios: formData.scenarios
    }));
    let modal = document.querySelector("#financial_projections_modal");
    let bootstrapModal = bootstrap.Modal.getInstance(modal);
    bootstrapModal.hide();
  };

  return (
    <div className="process_modal builder_modal">
      <div className="modal fade" id="financial_projections_modal">
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <div className="signin_hdng text-left">
                <h3>Financial Projections</h3>
                <h5>Provide details about your financial projections</h5>
              </div>
              <button
                type="button"
                className="close"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={resetHandler}
              >
                <i className="material-icons-outlined">close</i>
              </button>
            </div>
            <div className="modal-body">
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form_innr">
                  <div className="form-group">
                    <label>Revenue Projections</label>
                    <textarea
                      className="form-control"
                      name="revenueprojections"
                      value={formData.revenueprojections || ""}
                      onChange={handleChange}
                      placeholder="Enter your revenue projections"
                      rows="3"
                    />
                  </div>
                  <div className="form-group">
                    <label>Break-even Point</label>
                    <input
                      type="text"
                      className="form-control"
                      name="breakevenpoint"
                      value={formData.breakevenpoint || ""}
                      onChange={handleChange}
                      placeholder="Enter break-even point"
                    />
                  </div>
                  <div className="form-group">
                    <label>Main Assumptions</label>
                    <textarea
                      className="form-control"
                      name="mainassumptions"
                      value={formData.mainassumptions || ""}
                      onChange={handleChange}
                      placeholder="Enter main assumptions"
                      rows="3"
                    />
                  </div>
                  <div className="form-group">
                    <label>Scenarios</label>
                    <textarea
                      className="form-control"
                      name="scenarios"
                      value={formData.scenarios || ""}
                      onChange={handleChange}
                      placeholder="Describe different financial scenarios"
                      rows="3"
                    />
                  </div>
                </div>
                <div className="process_btns_outer d-flex align-items-center justify-content-between mt-3">
                  <button
                    onClick={resetHandler}
                    data-bs-dismiss="modal"
                    className="btn gray"
                  >
                    Cancel
                  </button>
                  <button className="btn" onClick={saveInfoHandler}>
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialProjectionsModal;
