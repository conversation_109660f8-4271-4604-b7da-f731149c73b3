/* eslint-disable */
import React, { useEffect, useContext } from "react";

import * as url from "helper/UrlHelper";

//import context
import { GlobalProvider } from "context/GlobalContext";

import Header from "../../components/common/Header";
import HomeBanner from "../../components/HomeComponents/HomeBanner/HomeBanner";
import Footer from "../../components/common/Footer";
import Brands from "../../components/common/Brands";
import HomeWorking from "../../components/HomeComponents/HomeWorking/HomeWorking";
import HomeWhyLobees from "../../components/HomeComponents/HomeWhyLobees/HomeWhyLobees";
import HomeModules from "../../components/HomeComponents/HomeModules/HomeModules";
import CustomerFeedback from "../../components/common/CustomerFeedback";
import ContactNow from "../../components/common/ContactNow";

const Home = () => {
  const { setuserEmailforOtp } = useContext(GlobalProvider);

  const redirectToUrl = localStorage.getItem("redirectToUrl");

  useEffect(() => {
    document.title = "Home"; // Update the title when Register component mounts

    // console.log("redirectToUrl ------>", redirectToUrl);

    let emailParams = "";
    let memberShipIdParams = "";

    if (redirectToUrl && redirectToUrl !== "") {
      const parsedURL = new URL(url.FRONTEND_BASE_URL + redirectToUrl);
      const searchParams = parsedURL.searchParams;

      emailParams = searchParams.get("memberemail");
      memberShipIdParams = searchParams.get("membershipid");
    }

    // console.log("emailParams", emailParams);
    // console.log("memberShipIdParams", memberShipIdParams);

    if (
      emailParams &&
      emailParams !== "" &&
      memberShipIdParams &&
      memberShipIdParams !== ""
    ) {
      setuserEmailforOtp(emailParams);

      //this is for showing login modal first render.
      const myModal = new bootstrap.Modal(
        document.getElementById("otp_confirm_login_mdl")
      );

      myModal.show();
    } else {
      //this is for showing login modal first render.
      const myModal = new bootstrap.Modal(
        document.getElementById("login_modal")
      );
      myModal.show();
    }
  }, []);

  return (
    <>
      {/* ---- Header section ----- */}
      <Header />

      {/* ---- Banner section ---- */}
      <HomeBanner />

      {/* ---- Brands / companies section ---- */}
      <Brands />

      {/* ---- how it works section ---- */}
      <HomeWorking />

      {/* ---- why lobees section ---- */}
      <HomeWhyLobees />

      {/* ---- Modules section ---- */}
      <HomeModules />

      {/* ---- customer feedback section ---- */}
      <CustomerFeedback />

      {/* ---- connect now section ---- */}
      <ContactNow sourceName="Connect Together" />

      {/* ---- footer section ---- */}
      <Footer />
    </>
  );
};

export default Home;
