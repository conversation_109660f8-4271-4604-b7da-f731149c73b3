/* eslint-disable */
import { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";

/*import url and gateway methods */
import * as url from "helper/UrlHelper";
import { getData, postData } from "utils/Gateway";

import { assetImages } from "constants";
import LoadingSpinner from "components/common/LoadingSpinner";

const CourseDetailsBodyTopPanel = ({
  setCourseTopPanelReload,
  courseTopPanelReload,
}) => {
  const token = localStorage.getItem("token");
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const params = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [courseDetails, setCourseDetails] = useState({});
  const [isJoining, setIsJoining] = useState(false);

  //get cuourse details
  const getCourseDetails = async () => {
    try {
      setIsLoading(true);

      let requestURL =
        url.API_BASE_URL +
        url.API_GET_COURSE_DETAILS +
        `/${params.id}?token=${token}&modulefrom=frontendtoppanel`;

      const response = await getData(requestURL);

      setIsLoading(false);

      console.log(response);

      if (response.status) {
        setCourseDetails(response.data);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  //function for join course
  const joinCourseHandler = async () => {
    const todayValue = new Date().toISOString().split("T")[0];

    try {
      setIsJoining(true);

      let courseJoinData = {
        user: userInfo._id,
        course: params.id,
        company: null,
        role: null,
        useremail: userInfo.email,
        memberstatus: "1",
        requestdate: todayValue,
      };

      console.log("Join course Data------>", courseJoinData);

      let requestURL =
        url.API_BASE_URL + url.API_JOIN_COURSE + `?token=${token}`;

      const response = await postData(requestURL, courseJoinData);

      setIsJoining(false);

      console.log(response);

      if (response.status) {
        // if (secretParam) {
        //   const baseURL = fullURL.split("?")[0];
        //   window.location.href = baseURL;
        // }
        const bootstrapModal = new bootstrap.Modal(
          document.getElementById("course_join_success_mdl")
        );
        bootstrapModal.show();
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  useEffect(() => {
    if (courseTopPanelReload) {
      getCourseDetails();
      setCourseTopPanelReload(false);
    }
  }, [courseTopPanelReload]);

  useEffect(() => {
    if (params.id) {
      getCourseDetails();
    }
  }, [params.id]);

  if (isLoading) {
    return (
      <div className="course_toppnl">
        <div className="course_bluebx white_shadow_bx">
          <div className="course_topbx">
            <h3 className="placeholder-glow">
              <span className="placeholder col-6"></span>
            </h3>

            <div className="event_tags course_tags">
              <ul className="list_stye_none">
                <li>
                  <a href="#" className="intermidate_btn placeholder-glow">
                    <span className="placeholder col-4"></span>
                  </a>
                </li>
                <li>
                  <a href="#" className="rating_bx placeholder-glow">
                    <i className="material-icons-outlined ornge_icon">star</i>
                    <span className="rating">
                      <span className="placeholder col-2"></span>
                      <em className="placeholder col-2 ms-2"></em>
                    </span>
                  </a>
                </li>
              </ul>
            </div>

            <div className="userexprt_pnltxt">
              <div className="placeholder-glow">
                <span
                  className="placeholder rounded-circle"
                  style={{
                    width: "50px",
                    height: "50px",
                    display: "inline-block",
                  }}
                ></span>
              </div>
              <div className="exprt_text placeholder-glow">
                <h5 className="placeholder col-4"></h5>
              </div>
            </div>
          </div>

          <div className="course_bttmbx course_rightbx">
            <div className="duration_outer">
              <div className="course_duartion">
                <ul className="list_stye_none placeholder-glow">
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                  <li>
                    <span className="placeholder col-3"></span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="experts_skill course_skill justify-content-between">
              <ul className="list_stye_none placeholder-glow">
                <li>
                  <span className="placeholder col-2"></span>
                </li>
                <li>
                  <span className="placeholder col-2"></span>
                </li>
                <li>
                  <span className="placeholder col-2"></span>
                </li>
              </ul>
              <div className="job_sharebtn tag_btns placeholder-glow">
                <span className="placeholder col-1"></span>
              </div>
            </div>

            <div className="feedback_jointbtnOuter">
              <div className="details_apply placeholder-glow">
                <span
                  className="placeholder btn col-4"
                  style={{
                    width: "100px",
                  }}
                ></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Extracted enrollment logic to a separate function
  const renderEnrollmentSection = () => {
    if (courseDetails?.usercourserole) {
      return (
        <div className="details_apply">
          <Link to="#" className="btn">
            {courseDetails.usercourserole}
          </Link>
        </div>
      );
    }

    if (courseDetails?.isapproved) {
      return renderDateLabel("Joining Date", courseDetails.joiningdate);
    }

    if (courseDetails?.issubscribed) {
      return renderDateLabel("Subscribe Date", courseDetails.subscribedate);
    }

    return (
      <div className="details_apply">
        <button
          className={`btn ${isJoining ? "disabled" : ""}`}
          onClick={joinCourseHandler}
          disabled={isJoining}
        >
          Enroll
          {isJoining && <LoadingSpinner />}
        </button>
      </div>
    );
  };

  // Helper function for date labels

  const renderDateLabel = (label, date) => (
    <div className="subscribe_text">
      <label>{label}:</label>

      <span className="ms-1">{date}</span>
    </div>
  );

  return (
    <div className="course_toppnl">
      <div className="course_bluebx white_shadow_bx">
        <div className="course_topbx">
          <h3>{courseDetails?.title}</h3>

          {/* Categories & Rating */}
          <div className="event_tags course_tags">
            <ul className="list_stye_none">
              <li>
                {courseDetails.categories?.map((item, index) => (
                  <Link to="#" className="intermidate_btn" key={index}>
                    <span>{item.name}</span>
                  </Link>
                ))}
              </li>

              <li>
                <Link to="#" className="rating_bx">
                  <i className="material-icons-outlined ornge_icon">star</i>

                  <span className="rating">
                    {courseDetails?.totalratings}

                    <em>({courseDetails?.totalreviews})</em>
                  </span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Moderator Info */}
          <div className="userexprt_pnltxt">
            <div className="expert_usr">
              <img
                src={
                  courseDetails?.moderatorimage
                    ? `${url.SERVER_URL}${courseDetails.moderatorimage}`
                    : assetImages.defaultUser
                }
                alt={courseDetails?.moderatorname || "Course moderator"}
              />
            </div>

            <div className="exprt_text">
              <h5>{courseDetails?.moderatorname}</h5>
            </div>
          </div>
        </div>

        {/* Bottom Section */}

        <div className="course_bttmbx course_rightbx">
          <div className="duration_outer">
            <div className="course_duartion">
              <ul className="list_stye_none">
                <li>{courseDetails?.chaptercount} chapters</li>

                <li>{courseDetails?.lessoncount} lessons</li>
              </ul>
            </div>
          </div>

          {/* Tags Section */}

          <div className="experts_skill course_skill justify-content-end">
            {courseDetails?.tags?.length > 0 && (
              <ul className="list_stye_none">
                {courseDetails.tags.map((tag, index) => (
                  <li key={index}>
                    <Link to="#">{tag.title}</Link>
                  </li>
                ))}
              </ul>
            )}

            <div className="job_sharebtn tag_btns">
              <Link to="#" data-bs-target="#tags_modal" data-bs-toggle="modal">
                <span className="material-icons">more</span>
              </Link>
            </div>
          </div>

          {/* Enrollment Section */}

          <div className="feedback_jointbtnOuter">
            {renderEnrollmentSection()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetailsBodyTopPanel;
