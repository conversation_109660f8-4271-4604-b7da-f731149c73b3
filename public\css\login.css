.addons_tabLinks.jobdetails_tablinks.login_tabs ul {margin: 0 0 24px;}
.addons_tabLinks.jobdetails_tablinks.login_tabs ul li {width: 50%;text-align: center;margin: 0;}
.addons_tabLinks.jobdetails_tablinks.login_tabs ul li a { margin: 0; border: 0;border-bottom: 2px solid var(--bs-color-F1);padding: 14px 10px;font-weight: 600;font-size: 14px;color: var(--bs-color-00);}
.addons_tabLinks .nav-tabs .nav-item.show .nav-link, .addons_tabLinks .nav-tabs .nav-link.active {color: var(--bs-color-38);border-color: var(--bs-color-38);}
.login_body .input_fill_landing .form-control{border: 1px solid var(--bs-color-D3);height: 50px;border-radius: 10px;padding: 0 16px;font-size: 15px;box-shadow: none;}
.login_body .input_fill_landing .form-control:focus{border-color: var(--bs-color-38);}
.login_body .input_fill_landing label{margin-bottom: 8px;} 
.form-group label{margin-bottom: 8px;}
.registration_modal .modal-header{position: relative;padding: 40px 30px 15px;border: 0;}
.registration_modal .close{background-color: var(--bs-color-F1);color: var(--bs-color-00);font-size: 20px;position: absolute;right: 15px;top: 15px;height: 44px;width: 44px;border-radius: 50%;display: flex;align-items: center;justify-content: center;}
.modal-content{border-radius: 10px;}
.small_modal{max-width: 460px;}
.medium_modal{max-width: 640px;}
.signin_hdng h3 {font-size: 30px;color: var(--bs-color-00);font-weight: 600;}
.signin_hdng h5{margin:12px 0 0;font-size:14px;color:var(--bs-color-75);font-weight:500}
.landing_login_fillOuter .input_fill_landing label{margin-bottom: 8px;font-weight: 600;font-size: 14px;line-height: 20px;color: var(--bs-color-00);}
.landing_login_fillOuter .input_fill_landing .form-control{border: 1px solid var(--bs-color-D3);border-radius: 5px;height: 46px;}
.landing_login_fillOuter .input_fill_landing p{display: flex;gap: 6px;margin: 10px 0 0;align-items: center;}
.error{font-size: 14px;line-height: 18px;color: var(--bs-color-E8);}
.error span{font-size: 14px;}
.landing_login_fillOuter .input_fill_landing{margin-bottom: 20px;}
.registration_modal .modal-body{padding: 15px 30px 44px;}
.registration_modal .radio-check p{color: var(--bs-color-00);}
.registration_modal .radio-check p a{display: inline-block;color: var(--bs-color-38);}
.eye_btnfill{position: relative;}
.forgot_applyOuter{display: flex;align-items: center;justify-content: space-between;}
.create_userOuter{margin: 20px 0 0;}
.linkedin_btn a{display: flex;align-items: center;height: 46px;gap: 10px;border: 1px solid var(--bs-color-F1);border-radius: 5px;justify-content: center;font-weight: 600; font-size: 15px;line-height: 18px;color: var(--bs-color-00);background-color: var(--bs-color-F1);}
.login_body .btn{height: 44px;font-size: 15px;}
.google_btn{margin-bottom: 8px;}
.create_text{margin: 20px 0 0;text-align: center;}
.create_text p{font-size: 15px;line-height: 19px;color: var(--bs-color-00);}
.create_text p a{display: inline-block;color: var(--bs-color-38);}
.create_text p a:hover{color: var(--bs-color-00);text-decoration: underline;}
.registration_modal .radio-check input[type='checkbox']+span{width:19px;min-width:19px;height:19px;border-radius:4px;border:2px solid var(--bs-color-D3);position:relative;margin-right:10px}
.registration_modal .radio-check input[type='checkbox']:checked+span:after{content:"";position:absolute;left:1px;top:1px;right:1px;bottom:1px;background:url(../images/check.png) no-repeat center center;background-size:10px}
.copy_link{right:12px;top:50%;position:absolute;transform:translateY(-50%);line-height:100%;color:var(--bs-color-75)}
.copy_link:hover{color: var(--bs-color-38);}
.copy_link span{font-size: 20px;}
.email_info{display: inline-flex;align-items: center;justify-content: center;margin-top: 10px;color: var(--bs-color-38);font-size: 14px;line-height: 18px;color: var(--bs-color-38);border-radius: 40px;height: 30px;gap: 10px;}
.email_info a{display: inline-block;color: var(--bs-color-38);}
.email_info a i{font-size: 18px;}
.input_fill_landing > label{font-weight: 600;}
.validate_fill {display: flex; gap: 8px;}
.validate_fill .form-control{box-shadow: none;height: 46px;text-align: center;border: 1px solid var(--bs-color-D3);color: var(--bs-color-00);width: 52px;border-radius: 5px;background-color: var(--bs-color-FF);padding: 0 10px;}
.validate_error{margin-top: 22px;}
.resend_btn a{gap: 5px;}
.resend_btn a i{font-size: 18px;}
.loginstep_btnsouter{display: flex;align-items: center;justify-content: space-between;margin-top: 30px;}
.text-left{text-align: left;}
.validate_msg{margin-bottom: 20px;}
.validate_msg p{font-size: 14px;line-height: 18px;color: var(--bs-color-75);}
.validate_list label, .validate_list li{font-size: 14px;color: var(--bs-color-75);line-height: 20px;margin-bottom: 15px;}
.validate_list li{position: relative;padding-left: 15px;}
.validate_list li::after{position: absolute;content: "";height: 5px;width: 5px;border-radius: 50%;background-color: var(--bs-color-75);left: 0;top: 8px;}
.validate_list ul{padding-left: 15px;}
.interest_hdng h4{color: var(--bs-color-00);font-weight: 500;font-size: 16px;line-height: 20px;}
.info_hdng_top{display: flex;align-items: center;justify-content: space-between;margin-bottom: 16px;}
.info_hdng_top h3{font-weight: 500;font-size: 24px;line-height: 30px;color: var(--bs-color-00);}
.info_hdng_top a{display: inline-flex;align-items: center;gap: 5px;color: var(--bs-color-75);font-size: 14px;}
.info_hdng_top a i{font-size: 18px;}
.info_hdng_top a:hover{color: var(--bs-color-38);}
.info_outerFill{display: flex;flex-wrap: wrap;margin: 0 -8px;}
.info_outerFill .input_fill_landing{padding: 0 8px;margin-bottom: 24px;}
.w_50{width: 50%;}
.w_100{width: 100%;}
.info_outerFill .input_fill_landing .form-control{height: 46px;box-shadow: none;border: 1px solid var(--bs-color-F1);border-radius: 5px;background-color: var(--bs-color-FF);font-size: 15px;line-height: 19px;color: var(--bs-color-00);padding: 0 15px;}
.info_outerFill .input_fill_landing label, .personel_information_area .description_fill.input_fill_landing label{font-weight: 500;font-size: 14px;line-height: 20px;color: var(--bs-color-00);}
.info_outerFill .input_fill_landing .select2-container--default .select2-selection--single{background-color: var(--bs-color-FF);color: var(--bs-color-75);font-weight: 400;border: 1px solid var(--bs-color-F1);}
.srvc_tags{margin: 8px 0 0;}
.srvc_tags ul{display: flex;gap: 5px 5px;flex-wrap: wrap;}
.srvc_tags ul li a{height: 34px;display: inline-flex;align-items: center;padding: 0 12px;border-radius: 90px;color: var(--bs-color-75);font-size: 14px;line-height: 18px;background-color: var(--bs-color-F1);}
.srvc_tags ul li a:hover{background-color: var(--bs-color-38);color: var(--bs-color-FF);}
.login_info_item{margin-bottom: 30px;}
.login_info_item:last-child{margin-bottom: 0;}
.personel_information_area{background-color: var(--bs-color-F1);padding: 20px 16px;}
.personel_information_area .description_fill.input_fill_landing .form-control{background-color: var(--bs-color-FF);}
.step_bullet ul{display: flex;align-items: center;justify-content: center;gap: 5px;}
.step_bullet ul li{height: 10px;width: 10px;border: 1px solid var(--bs-color-75);border-radius: 50%;cursor: pointer;}
.step_bullet ul li.active{background-color: var(--bs-color-38);border-color: var(--bs-color-38);}
.documents_uploaditem{margin-bottom: 40px;}
.documents_uploaditem:last-child{margin-bottom: 0;}
.documents_uploaditem h4{font-weight: 500;font-size: 24px;line-height: 30px;color: var(--bs-color-00);margin-bottom: 16px;}
.upload_bx{position: relative;border: 1px dashed var(--bs-color-75);border-radius: 5px;height: 180px;color: var(--bs-color-00);display: flex;align-items: center;justify-content: center;padding: 15px;text-align: center;}
.upload_bx i{display: block;margin-bottom: 10px;}
.upload_bx span{font-weight: 500;font-size: 16px;line-height: 20px;}
.profile_pic_modal .modal-content{background-color: var(--bs-color-00);}
.rotate ul{display: flex;align-items: center;justify-content: center;gap: 15px;}
.profile_pic_modal .modal-header{padding: 30px 24px;justify-content: flex-start;}
.arrw_back a{display: inline-block;color: var(--bs-color-FF);}
.days_bx ul{display: flex;align-items: center;gap: 8px;}
.days_bx ul li{width: 100%;}
.days_bx ul li span{display: flex;height: 46px;align-items: center;justify-content: center;font-size: 15px;line-height: 19px;color: var(--bs-color-75);background-color: var(--bs-color-F1);border-radius: 8px;cursor: pointer;}
.days_bx ul li.active span{background: var(--bs-color-FF); box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);border-radius: 8px;color: var(--bs-color-38);}
.time_bx ul{gap: 0;margin: 0 -4px;flex-wrap: wrap;}
.time_bx ul li{width: 20%;padding: 0 4px 8px;}
.job_change_outer ul{padding: 5px;background-color: var(--bs-color-F1);display: flex;gap: 8px;}
.job_change_outer ul li{width: 100%;}
.job_change_outer ul li a{display: flex;align-items: center;justify-content: center;height: 46px;border-radius: 8px;padding: 0 24px;font-size: 15px;line-height: 19px;color: var(--bs-color-75);}
.job_change_outer ul li.active a{background-color: var(--bs-color-FF);box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.25);color: var(--bs-color-38);}
.srvc_tags.joining_tags ul{display: block;}
.srvc_tags.joining_tags ul li{margin-bottom: 10px;}
.srvc_tags.joining_tags ul li:last-child{margin-bottom: 0;}
.circle_tick{color: var(--bs-color-38);}
.circle_tick span{font-size: 18px;}
.grn_circle{color: var(--bs-color-31);}
.success_text .btns_group .basic_btn{margin: 0;}
.create_chk_outer .trms_check{margin-bottom: 20px;}
.create_chk_outer .trms_check .radio-check{align-items: flex-start;}
.registration_modal .create_chk_outer .radio-check input[type='checkbox']+span{margin-top: 3px;}
.registration_modal .create_chk_outer .radio-check p{color: var(--bs-color-75);font-weight: 400;}
.forgot_btn a{color: var(--bs-color-38);font-size: 15px;font-weight: 600;}
.or_text{color: var(--bs-color-B3);font-size: 15px;font-weight: 400;line-height: 25px;letter-spacing: 0px;text-align: center;}
#login_modal .modal-body{padding-top: 0;}
.otp_fill_outer .form-group{margin-bottom: 24px;}
.otp_fill_outer .form-group .btn.outline{margin-top: 10px;gap: 6px;height: 44px;}
.conatct_modal.success_popup .modal-dialog {max-width: 364px;}
.success_popup .modal-body {padding: 40px 20px;}
.success_graphics {text-align: center;margin-bottom: 30px;}
.success_text {text-align: center;}
.success_text h3 {font-weight: 700;font-size: 22px; line-height: 26px;color: var(--bs-color-00);margin-bottom: 8px;}
.success_text p {font-weight: 500;font-size: 16px;line-height: 26px; color: var(--bs-color-00);}
.success_popup .btn{height: 44px;}

@media(max-width:767px){
    .registration_modal .modal-body {padding: 15px 15px 25px;}
    .signin_hdng h3{font-size: 22px;}
    .registration_modal .modal-header{padding: 40px 15px 15px;}
    .info_hdng_top h3, .documents_uploaditem h4{font-size: 18px;line-height: 24px;}
    .registration_modal .close{height: 36px;width: 36px;}
    .registration_modal .close i{font-size: 20px;}
}
@media(max-width:575px){
    .w_50 {width: 100%;}
    .days_bx ul li span{font-size: 12px;line-height: 17px;height: 36px;}
    .days_bx ul{gap: 4px;}
    .time_bx ul {gap: 0;margin: 0 -2px;}
    .time_bx ul li {padding: 0 2px 6px;}
    .info_outerFill .input_fill_landing{margin-bottom: 16px;}
    .job_change_outer ul{flex-wrap: wrap;border-radius: 5px;}
    .info_outerFill .input_fill_landing .form-control, .landing_login_fillOuter .input_fill_landing .form-control{height: 48px;}
    .validate_list label, .validate_list li{font-size: 13px;}
    .info_outerFill .input_fill_landing label, .personel_information_area .description_fill.input_fill_landing label{font-size: 13px;}
    .job_change_outer ul li a{padding: 0 15px;}
}
@media(max-width:420px){
    .create_userOuter{flex-wrap: wrap;}
}